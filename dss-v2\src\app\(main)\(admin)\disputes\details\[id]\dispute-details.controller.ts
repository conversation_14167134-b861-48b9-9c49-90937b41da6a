"use client";

import { useCallback, useState, useEffect, useMemo } from "react";
import { useSearchParams } from "next/navigation";
import { IBreadcrumbDisplay } from "@/components/common";
import { RoutePathEnum } from "@/enum";

// Mock data for demonstration
const mockDisputeData = {
  id: "123",
  sn: 1,
  sendersEmail: "<EMAIL>",
  receiversEmail: "<EMAIL>",
  subject: "Your OTP for Login",
  counter: 3,
  aiStatus: "Pending",
  adminStatus: "Pending",
  createdAt: "23-04-2023 11:26 AM",
  securityStatus: {
    final: "UNSAFE",
    reason: "Status reflected by ai"
  },
  authentication: {
    dkim: true,
    spf: false,
    dmarc: true
  },
  threatIntelligence: {
    databaseCheck: true
  },
  contentAnalysis: {
    urls: [
      { url: "https://example.com/login", status: "Malicious" },
      { url: "https://example.com/verify", status: "Safe" }
    ],
    attachments: [
      { name: "document.pdf", status: "Safe" },
      { name: "invoice.xlsx", status: "Malicious" }
    ]
  },
  disputeHistory: [
    {
      date: "23-04-2023 11:30 AM",
      userComment: "This email is legitimate and was expected.",
      adminResponse: "After review, the email appears to be legitimate.",
      adminStatus: "Safe"
    },
    {
      date: "23-04-2023 10:45 AM",
      userComment: "I received this email but wasn't expecting it.",
      adminResponse: "Initial analysis shows suspicious elements.",
      adminStatus: "Unsafe"
    }
  ],
  emailBody: `From: <EMAIL>
To: <EMAIL>
Subject: Your OTP for Login

Dear User,

Your one-time password (OTP) for login is: 123456

This OTP will expire in 10 minutes. Please do not share this with anyone.

If you did not request this OTP, please ignore this email.

Best regards,
Security Team
Ekvayus Tech`
};

/**
 * Dispute Details Controller
 * @param {string} disputeId - The ID of the dispute to display
 * @return {Object} Controller with getters and handlers
 */
export const DisputeDetailsController = (disputeId: string) => {
  const [loading, setLoading] = useState(true);
  const [disputeData, setDisputeData] = useState<any>(null);
  const searchParams = useSearchParams();
  const source = searchParams.get('source') || 'pending';

  // In a real application, you would fetch the dispute data based on the disputeId
  useEffect(() => {
    // Simulate API call
    const fetchDisputeData = async () => {
      setLoading(true);
      try {
        // In a real app, you would fetch data from an API
        // const response = await fetch(`/api/disputes/${disputeId}`);
        // const data = await response.json();

        // For now, we'll use mock data
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update the mock data with the provided ID
        const data = { ...mockDisputeData, id: disputeId };
        setDisputeData(data);
      } catch (error) {
        console.error("Error fetching dispute data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDisputeData();
  }, [disputeId]);

  /**
   * Breadcrumbs for the page
   * Determine if this is from Pending or Resolved disputes based on the source parameter
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Disputes",
      path: RoutePathEnum.DISPUTES,
      forwardParam: false,
    },
    {
      name: source === 'resolved' ? "Resolved Disputes" : "Pending Disputes",
      path: source === 'resolved' ? RoutePathEnum.DISPUTES_RESOLVED : RoutePathEnum.DISPUTES_PENDING,
      forwardParam: false,
      clickable: true,
    },
    {
      name: `Dispute Details #${disputeId}`,
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  return {
    getters: {
      loading,
      disputeData,
      breadcrumbs,
    },
    handlers: {},
  };
};

export default DisputeDetailsController;
