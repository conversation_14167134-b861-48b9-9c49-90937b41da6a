import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Badge,
  Tooltip,
  Fade,
  Popover,
  Button,
  Divider,
  IconButton,
  Collapse,
  useTheme
} from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';

interface SystemOverviewItem {
  title: string;
  value: string | number;
  color?: string;
  badge?: number;
  trend?: 'up' | 'down' | 'flat';
  trendValue?: number;
  chartData?: number[];
  onClick?: () => void;
}

interface SystemOverviewCardProps {
  title: string;
  icon: React.ReactNode;
  color: string;
  items: SystemOverviewItem[];
  tabs?: string[];
  description?: string;
  onViewMore?: () => void;
  onCardClick?: () => void;
  isRefreshing?: boolean;
}

const EnhancedSystemOverviewCard: React.FC<SystemOverviewCardProps> = ({
  title,
  icon,
  color,
  items,
  tabs,
  description,
  onViewMore,
  onCardClick,
  isRefreshing = false,
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [activeTab, setActiveTab] = useState(0);
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  const [expandedItem, setExpandedItem] = useState<number | null>(null);
  const [infoAnchorEl, setInfoAnchorEl] = useState<HTMLElement | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<HTMLElement | null>(null);
  const [animatedValues, setAnimatedValues] = useState<(string | number)[]>(
    items.map(() => "0")
  );

  // Animate values on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedValues(items.map(item => item.value));
    }, 300);
    return () => clearTimeout(timer);
  }, [items]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleInfoClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setInfoAnchorEl(event.currentTarget);
  };

  const handleInfoClose = () => {
    setInfoAnchorEl(null);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleItemClick = (index: number, item: SystemOverviewItem) => {
    if (expandedItem === index) {
      setExpandedItem(null);
    } else {
      setExpandedItem(index);
    }

    if (item.onClick) {
      item.onClick();
    }
  };

  // Mini sparkline chart component
  const MiniSparkline = ({ data = [0, 0, 0, 0, 0], color }: { data: number[], color: string }) => {
    if (!data || data.length < 2) return null;

    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue || 1;

    return (
      <Box sx={{ width: 50, height: 24, ml: 1, position: 'relative' }}>
        <svg width="100%" height="100%" style={{ overflow: 'visible' }}>
          {/* Grid line */}
          <line
            x1="0"
            y1="12"
            x2="50"
            y2="12"
            stroke={isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'}
            strokeWidth="0.5"
            strokeDasharray="1,1"
          />

          {/* Area under the line */}
          <path
            d={`
              M ${0} ${24 - ((data[0] - minValue) / range) * 24}
              ${data.map((value, i) => {
                const x = (i / (data.length - 1)) * 50;
                const y = 24 - ((value - minValue) / range) * 24;
                return `L ${x} ${y}`;
              }).join(' ')}
              L ${50} ${24}
              L ${0} ${24}
              Z
            `}
            fill={`url(#miniGradient-${color.replace('#', '')})`}
            strokeWidth="0"
          />

          {/* Line */}
          <path
            d={`
              M ${0} ${24 - ((data[0] - minValue) / range) * 24}
              ${data.map((value, i) => {
                const x = (i / (data.length - 1)) * 50;
                const y = 24 - ((value - minValue) / range) * 24;
                return `L ${x} ${y}`;
              }).join(' ')}
            `}
            fill="none"
            stroke={color}
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {/* Data points */}
          {data.map((value, i) => {
            if (i === 0 || i === data.length - 1 || i % Math.floor(data.length / 2) === 0) {
              const x = (i / (data.length - 1)) * 50;
              const y = 24 - ((value - minValue) / range) * 24;

              return (
                <circle
                  key={i}
                  cx={x}
                  cy={y}
                  r={i === data.length - 1 ? 2 : 1}
                  fill={i === data.length - 1 ? color : 'white'}
                  stroke={color}
                  strokeWidth={i === data.length - 1 ? 0 : 1}
                />
              );
            }
            return null;
          })}

          {/* Gradient definition */}
          <defs>
            <linearGradient id={`miniGradient-${color.replace('#', '')}`} x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor={color} stopOpacity="0.3" />
              <stop offset="100%" stopColor={color} stopOpacity="0.05" />
            </linearGradient>
          </defs>
        </svg>
      </Box>
    );
  };

  // Trend indicator component
  const TrendIndicator = ({ trend, value }: { trend?: 'up' | 'down' | 'flat', value?: number }) => {
    if (!trend || value === undefined) return null;

    const color = trend === 'up' ? '#4CAF50' : trend === 'down' ? '#F44336' : '#757575';
    const Icon = trend === 'up' ? TrendingUpIcon : trend === 'down' ? TrendingDownIcon : TrendingFlatIcon;

    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          backgroundColor: `${color}15`,
          color,
          fontSize: '0.75rem',
          fontWeight: 'medium',
          ml: 1,
          px: 0.7,
          py: 0.2,
          borderRadius: '4px',
          border: `1px solid ${color}30`,
          transition: 'all 0.2s ease',
          '&:hover': {
            backgroundColor: `${color}25`,
            transform: 'translateY(-1px)'
          }
        }}
      >
        <Icon fontSize="small" sx={{
          fontSize: '0.875rem',
          mr: 0.3,
          animation: trend === 'up' ? 'trendUpPulse 1.5s infinite' :
                    trend === 'down' ? 'trendDownPulse 1.5s infinite' : 'none',
          '@keyframes trendUpPulse': {
            '0%': { transform: 'translateY(0)' },
            '50%': { transform: 'translateY(-1px)' },
            '100%': { transform: 'translateY(0)' }
          },
          '@keyframes trendDownPulse': {
            '0%': { transform: 'translateY(0)' },
            '50%': { transform: 'translateY(1px)' },
            '100%': { transform: 'translateY(0)' }
          }
        }} />
        {value}%
      </Box>
    );
  };

  // Expanded detail view for an item
  const ItemDetailView = ({ item, index }: { item: SystemOverviewItem, index: number }) => {
    return (
      <Collapse in={expandedItem === index}>
        <Box
          sx={{
            mt: 1.5,
            p: 2,
            backgroundColor: isDarkMode ? `${color}15` : `${color}08`,
            borderRadius: '8px',
            border: `1px solid ${color}30`,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              width: '4px',
              height: '100%',
              backgroundColor: item.color || color,
              borderTopLeftRadius: '8px',
              borderBottomLeftRadius: '8px',
            }
          }}
        >
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1.5,
              color: item.color || color,
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Box
              component="span"
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: item.color || color,
                display: 'inline-block',
                mr: 1,
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { boxShadow: `0 0 0 0 ${item.color || color}40` },
                  '70%': { boxShadow: `0 0 0 6px ${item.color || color}00` },
                  '100%': { boxShadow: `0 0 0 0 ${item.color || color}00` }
                }
              }}
            />
            {item.title} Details
          </Typography>

          {item.chartData && item.chartData.length > 0 && (
            <Box sx={{ height: 120, mb: 2, mt: 2 }}>
              <svg width="100%" height="100%" style={{ overflow: 'visible' }}>
                {/* Grid lines */}
                {[0, 1, 2, 3, 4].map((i) => (
                  <line
                    key={i}
                    x1="0"
                    y1={24 * i}
                    x2="100%"
                    y2={24 * i}
                    stroke={isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'}
                    strokeWidth="1"
                    strokeDasharray="3,3"
                  />
                ))}

                {/* Value labels */}
                {[0, 1, 2].map((i) => {
                  const value = Math.round(Math.min(...item.chartData!) +
                    (Math.max(...item.chartData!) - Math.min(...item.chartData!)) * (1 - i/2));
                  return (
                    <text
                      key={i}
                      x="0"
                      y={48 * i + 10}
                      fontSize="10"
                      fill={isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.4)'}
                      textAnchor="start"
                    >
                      {value}
                    </text>
                  );
                })}

                {/* Area under the line */}
                <path
                  d={`
                    M ${0} ${100 - (item.chartData[0] / Math.max(...item.chartData)) * 100}
                    ${item.chartData.map((value, i) => {
                      const x = (i / (item.chartData!.length - 1)) * 100;
                      const y = 100 - (value / Math.max(...item.chartData!)) * 100;
                      return `L ${x}% ${y}`;
                    }).join(' ')}
                    L ${100}% ${100}
                    L ${0} ${100}
                    Z
                  `}
                  fill={`url(#gradient-${index})`}
                  strokeWidth="0"
                />

                {/* Line */}
                <path
                  d={`
                    M ${0} ${100 - (item.chartData[0] / Math.max(...item.chartData)) * 100}
                    ${item.chartData.map((value, i) => {
                      const x = (i / (item.chartData!.length - 1)) * 100;
                      const y = 100 - (value / Math.max(...item.chartData!)) * 100;
                      return `L ${x}% ${y}`;
                    }).join(' ')}
                  `}
                  fill="none"
                  stroke={item.color || color}
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />

                <defs>
                  <linearGradient id={`gradient-${index}`} x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor={item.color || color} stopOpacity="0.4" />
                    <stop offset="100%" stopColor={item.color || color} stopOpacity="0.05" />
                  </linearGradient>
                </defs>

                {/* Data points */}
                {item.chartData.map((value, i) => {
                  const x = (i / (item.chartData!.length - 1)) * 100;
                  const y = 100 - (value / Math.max(...item.chartData!)) * 100;

                  return (
                    <g key={i}>
                      <circle
                        cx={`${x}%`}
                        cy={y}
                        r={3.5}
                        fill="white"
                        stroke={item.color || color}
                        strokeWidth="2"
                      />
                      {i === item.chartData!.length - 1 && (
                        <circle
                          cx={`${x}%`}
                          cy={y}
                          r={6}
                          fill="transparent"
                          stroke={item.color || color}
                          strokeWidth="1"
                          opacity="0.5"
                        />
                      )}
                    </g>
                  );
                })}

                {/* X-axis labels */}
                {item.chartData.map((value, i) => {
                  if (i === 0 || i === item.chartData!.length - 1 || i === Math.floor(item.chartData!.length / 2)) {
                    const x = (i / (item.chartData!.length - 1)) * 100;
                    const label = i === 0 ? 'Start' : i === item.chartData!.length - 1 ? 'Now' : 'Mid';

                    return (
                      <text
                        key={i}
                        x={`${x}%`}
                        y="115"
                        fontSize="9"
                        fill={isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.4)'}
                        textAnchor="middle"
                      >
                        {label}
                      </text>
                    );
                  }
                  return null;
                })}
              </svg>
            </Box>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              Current value: <Box component="span" sx={{ fontWeight: 'bold', color: item.color || color }}>{item.value}</Box>
            </Typography>

            <Button
              variant="contained"
              size="small"
              endIcon={<ArrowForwardIcon />}
              sx={{
                backgroundColor: item.color || color,
                color: 'white',
                boxShadow: `0 4px 8px ${(item.color || color)}40`,
                '&:hover': {
                  backgroundColor: item.color || color,
                  boxShadow: `0 6px 12px ${(item.color || color)}60`,
                }
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (item.onClick) item.onClick();
              }}
            >
              View Details
            </Button>
          </Box>
        </Box>
      </Collapse>
    );
  };

  return (
    <Paper
      elevation={0}
      onClick={onCardClick}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: isDarkMode ? `1px solid ${color}30` : '1px solid #e0e0e0',
        borderRadius: '12px',
        overflow: 'hidden',
        position: 'relative',
        transition: 'all 0.3s ease',
        cursor: onCardClick ? 'pointer' : 'default',
        backgroundColor: isDarkMode ? `${color}08` : 'background.paper',
        transform: isRefreshing ? 'scale(0.98)' : 'scale(1)',
        opacity: isRefreshing ? 0.8 : 1,
        '&:hover': {
          boxShadow: isDarkMode
            ? `0 8px 24px -4px rgba(0,0,0,0.3), 0 0 10px ${color}30`
            : `0 12px 24px -4px rgba(0,0,0,0.1), 0 0 10px ${color}20`,
          transform: isRefreshing ? 'scale(0.98)' : 'translateY(-4px)',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '5px',
          background: `linear-gradient(90deg, ${color} 0%, ${color}CC 100%)`,
          boxShadow: `0 1px 8px ${color}40`,
        }
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2.5,
          pb: 2,
          borderBottom: tabs ? (isDarkMode ? `1px solid ${color}20` : '1px solid #f0f0f0') : 'none',
          background: isDarkMode ? 'transparent' : `linear-gradient(to bottom, ${color}08 0%, transparent 100%)`,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 42,
              height: 42,
              borderRadius: '12px',
              backgroundColor: isDarkMode ? `${color}30` : `${color}15`,
              color: color,
              mr: 1.5,
              transition: 'all 0.3s ease',
              boxShadow: `0 4px 8px ${color}20`,
              '&:hover': {
                transform: 'scale(1.05) rotate(5deg)',
                boxShadow: `0 6px 12px ${color}30`,
              }
            }}
          >
            {icon}
          </Box>
          <Box>
            <Typography
              variant="subtitle1"
              fontWeight="bold"
              sx={{
                color: isDarkMode ? color : 'text.primary',
                letterSpacing: '0.01em',
              }}
            >
              {title}
            </Typography>
            {description && (
              <Typography
                variant="caption"
                sx={{
                  color: 'text.secondary',
                  display: 'block',
                  maxWidth: '180px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
                onClick={handleInfoClick}
              >
                {description.substring(0, 35)}...
              </Typography>
            )}
          </Box>

          {description && (
            <>
              <IconButton
                size="small"
                onClick={handleInfoClick}
                sx={{
                  ml: 0.5,
                  color: color,
                  backgroundColor: isDarkMode ? `${color}15` : `${color}10`,
                  width: 22,
                  height: 22,
                  '&:hover': {
                    backgroundColor: isDarkMode ? `${color}25` : `${color}20`,
                  }
                }}
              >
                <InfoOutlinedIcon sx={{ fontSize: '0.875rem' }} />
              </IconButton>

              <Popover
                open={Boolean(infoAnchorEl)}
                anchorEl={infoAnchorEl}
                onClose={handleInfoClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'center',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'center',
                }}
                PaperProps={{
                  sx: {
                    p: 2,
                    maxWidth: 300,
                    boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                    border: isDarkMode ? `1px solid ${color}30` : 'none',
                    borderRadius: '8px',
                  }
                }}
              >
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 1,
                    color,
                    fontWeight: 'bold',
                    borderBottom: `1px solid ${color}20`,
                    pb: 0.5
                  }}
                >
                  {title} Information
                </Typography>
                <Typography variant="body2">{description}</Typography>
              </Popover>
            </>
          )}
        </Box>

        <Box>
          <IconButton
            size="small"
            onClick={handleMenuClick}
            sx={{
              color: isDarkMode ? color : 'text.secondary',
              backgroundColor: isDarkMode ? `${color}15` : 'transparent',
              border: isDarkMode ? 'none' : '1px solid #e0e0e0',
              '&:hover': {
                backgroundColor: isDarkMode ? `${color}25` : '#f5f5f5',
              }
            }}
          >
            <MoreHorizIcon fontSize="small" />
          </IconButton>

          <Popover
            open={Boolean(menuAnchorEl)}
            anchorEl={menuAnchorEl}
            onClose={handleMenuClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'right',
            }}
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            PaperProps={{
              sx: {
                p: 1,
                boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                border: isDarkMode ? `1px solid ${color}30` : 'none',
                borderRadius: '8px',
              }
            }}
          >
            <Button
              size="small"
              startIcon={<ArrowForwardIcon />}
              onClick={(e) => {
                e.stopPropagation();
                handleMenuClose();
                if (onViewMore) onViewMore();
              }}
              sx={{
                color: color,
                '&:hover': {
                  backgroundColor: `${color}10`,
                }
              }}
            >
              View All
            </Button>
            <Divider sx={{ my: 1 }} />
            <Button
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                handleMenuClose();
              }}
              sx={{
                color: 'text.primary',
                '&:hover': {
                  backgroundColor: isDarkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                }
              }}
            >
              Refresh Data
            </Button>
          </Popover>
        </Box>
      </Box>

      {tabs && (
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            minHeight: '44px',
            backgroundColor: isDarkMode ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.02)',
            '& .MuiTabs-indicator': {
              backgroundColor: color,
              height: 3,
              borderTopLeftRadius: 3,
              borderTopRightRadius: 3,
            },
            '& .MuiTab-root': {
              minHeight: '44px',
              textTransform: 'none',
              fontSize: '0.875rem',
              fontWeight: 'medium',
              color: 'text.secondary',
              transition: 'all 0.2s ease',
              '&.Mui-selected': {
                color: color,
                fontWeight: 'bold',
              },
              '&:hover': {
                backgroundColor: isDarkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.03)',
                color: isDarkMode ? 'rgba(255,255,255,0.9)' : 'rgba(0,0,0,0.8)',
              },
            },
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab}
              sx={{
                py: 1,
                position: 'relative',
                overflow: 'hidden',
                '&::after': activeTab === index ? {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: `linear-gradient(to top, ${color}10 0%, transparent 100%)`,
                  zIndex: -1,
                } : {}
              }}
            />
          ))}
        </Tabs>
      )}

      <Box sx={{ p: 2.5, pt: 2, flexGrow: 1 }}>
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <Box
              onClick={() => handleItemClick(index, item)}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                py: 1.5,
                borderBottom: index < items.length - 1 ?
                  (isDarkMode ? `1px solid ${color}15` : '1px solid rgba(0,0,0,0.06)') :
                  'none',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                borderRadius: '8px',
                px: 1.5,
                backgroundColor: hoveredItem === index || expandedItem === index ?
                  (isDarkMode ? `${color}15` : `${color}08`) :
                  'transparent',
                transform: hoveredItem === index ? 'translateX(2px)' : 'none',
                '&:hover': {
                  backgroundColor: isDarkMode ? `${color}15` : `${color}08`,
                  transform: 'translateX(2px)',
                  boxShadow: hoveredItem === index ? `0 2px 8px ${color}20` : 'none',
                }
              }}
              onMouseEnter={() => setHoveredItem(index)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box
                  sx={{
                    width: 6,
                    height: 6,
                    borderRadius: '50%',
                    backgroundColor: item.color || color,
                    mr: 1.5,
                    transition: 'all 0.2s ease',
                    transform: hoveredItem === index ? 'scale(1.5)' : 'scale(1)',
                  }}
                />
                <Typography
                  variant="body2"
                  color={hoveredItem === index ? (item.color || color) : 'text.secondary'}
                  sx={{
                    fontWeight: hoveredItem === index || expandedItem === index ? 'bold' : 'medium',
                    transition: 'all 0.2s ease',
                    letterSpacing: '0.01em',
                  }}
                >
                  {item.title}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {item.badge !== undefined && (
                  <Badge
                    badgeContent={item.badge}
                    sx={{
                      mr: 1,
                      '& .MuiBadge-badge': {
                        backgroundColor: item.color || color,
                        color: 'white',
                        fontWeight: 'bold',
                        boxShadow: `0 2px 6px ${(item.color || color)}60`,
                      }
                    }}
                  />
                )}

                {item.chartData && item.chartData.length > 0 && (
                  <MiniSparkline
                    data={item.chartData}
                    color={item.color || color}
                  />
                )}

                <TrendIndicator
                  trend={item.trend}
                  value={item.trendValue}
                />

                <Tooltip
                  title={
                    <Box sx={{ p: 0.5 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: item.color || color }}>
                        {item.title}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        Current value: <Box component="span" sx={{ fontWeight: 'bold' }}>{item.value}</Box>
                      </Typography>
                      {item.trend && item.trendValue && (
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          mt: 0.5,
                          color: item.trend === 'up' ? '#4CAF50' : item.trend === 'down' ? '#F44336' : '#757575',
                        }}>
                          {item.trend === 'up' ? <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} /> :
                           item.trend === 'down' ? <TrendingDownIcon fontSize="small" sx={{ mr: 0.5 }} /> :
                           <TrendingFlatIcon fontSize="small" sx={{ mr: 0.5 }} />}
                          <Typography variant="body2">
                            {item.trend === 'up' ? 'Increased by' : item.trend === 'down' ? 'Decreased by' : 'Changed by'} {item.trendValue}%
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  }
                  arrow
                  placement="top"
                  componentsProps={{
                    tooltip: {
                      sx: {
                        backgroundColor: isDarkMode ? '#2d2d2d' : 'white',
                        color: isDarkMode ? 'white' : 'rgba(0,0,0,0.87)',
                        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                        border: isDarkMode ? `1px solid ${color}30` : `1px solid ${color}20`,
                        borderRadius: '8px',
                        p: 1.5,
                      }
                    },
                    arrow: {
                      sx: {
                        color: isDarkMode ? '#2d2d2d' : 'white',
                        '&::before': {
                          border: isDarkMode ? `1px solid ${color}30` : `1px solid ${color}20`,
                          backgroundColor: isDarkMode ? '#2d2d2d' : 'white',
                        }
                      }
                    }
                  }}
                >
                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    color={item.color || color}
                    sx={{
                      ml: 1,
                      px: 1,
                      py: 0.3,
                      borderRadius: '4px',
                      backgroundColor: isDarkMode ? `${color}15` : `${color}10`,
                      border: `1px solid ${color}20`,
                      transition: 'all 0.3s ease-out',
                      animation: animatedValues[index] !== item.value ? 'pulse 1s' : 'none',
                      '@keyframes pulse': {
                        '0%': { transform: 'scale(1)' },
                        '50%': { transform: 'scale(1.1)' },
                        '100%': { transform: 'scale(1)' },
                      }
                    }}
                  >
                    {animatedValues[index]}
                  </Typography>
                </Tooltip>
              </Box>
            </Box>

            {/* Expanded detail view */}
            <ItemDetailView item={item} index={index} />
          </React.Fragment>
        ))}
      </Box>

      {onViewMore && (
        <Box
          sx={{
            p: 2,
            borderTop: isDarkMode ? `1px solid ${color}20` : '1px solid #f0f0f0',
            display: 'flex',
            justifyContent: 'center',
            background: isDarkMode ? 'transparent' : `linear-gradient(to top, ${color}08 0%, transparent 100%)`,
          }}
        >
          <Button
            variant="outlined"
            size="small"
            endIcon={
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 18,
                  height: 18,
                  borderRadius: '50%',
                  backgroundColor: color,
                  transition: 'all 0.2s ease',
                  ml: 0.5,
                }}
              >
                <ArrowForwardIcon sx={{ color: 'white', fontSize: '0.875rem' }} />
              </Box>
            }
            onClick={(e) => {
              e.stopPropagation();
              onViewMore();
            }}
            sx={{
              borderColor: color,
              color,
              fontWeight: 'medium',
              px: 2,
              borderRadius: '20px',
              borderWidth: '1.5px',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: `${color}10`,
                borderColor: color,
                transform: 'translateY(-2px)',
                boxShadow: `0 4px 8px ${color}30`,
              }
            }}
          >
            View All {title}
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default EnhancedSystemOverviewCard;
