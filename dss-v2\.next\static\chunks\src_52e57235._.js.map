{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/spacing/enum/index.ts"], "sourcesContent": ["export enum SpacingEnum {\r\n  TOP = 'top',\r\n  BOTTOM = 'bottom',\r\n  RIGHT = 'right',\r\n  LEFT = 'left',\r\n  HORIZONTAL = 'horizontal',\r\n  VERTICAL = 'vertical',\r\n  ALL = 'all',\r\n  RECTANGULAR = 'rectangular',\r\n}\r\n\r\nexport default SpacingEnum;\r\n"], "names": [], "mappings": ";;;;AAAO,IAAA,AAAK,qCAAA;;;;;;;;;WAAA;;uCAWG", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/spacing/spacing.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Box, styled, Theme } from '@mui/material';\r\n\r\nimport { SpacingEnum } from './enum';\r\n\r\ninterface ISpacingProps {\r\n  theme?: Theme;\r\n  spacing: number;\r\n  variant?: SpacingEnum;\r\n}\r\n\r\n/**\r\n * Spacing Component\r\n */\r\nexport const Spacing = styled(Box)(\r\n  ({ theme, spacing = 3, variant }: ISpacingProps) => {\r\n    switch (variant) {\r\n      case SpacingEnum.TOP:\r\n        return {\r\n          marginTop: theme?.spacing(spacing),\r\n        };\r\n      case SpacingEnum.BOTTOM:\r\n        return {\r\n          marginBottom: theme?.spacing(spacing),\r\n        };\r\n      case SpacingEnum.RIGHT:\r\n        return {\r\n          marginRight: theme?.spacing(spacing),\r\n        };\r\n      case SpacingEnum.LEFT:\r\n        return {\r\n          marginLeft: theme?.spacing(spacing),\r\n        };\r\n      case SpacingEnum.HORIZONTAL:\r\n        return {\r\n          marginRight: theme?.spacing(spacing),\r\n          marginLeft: theme?.spacing(spacing),\r\n        };\r\n      case SpacingEnum.VERTICAL:\r\n        return {\r\n          marginTop: theme?.spacing(spacing),\r\n          marginBottom: theme?.spacing(spacing),\r\n        };\r\n      default:\r\n        return {\r\n          margin: theme?.spacing(spacing),\r\n        };\r\n    }\r\n  },\r\n);\r\n\r\nexport default Spacing;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAEA;AAJA;;;AAeO,MAAM,UAAU,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAC/B,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,OAAO,EAAiB;IAC7C,OAAQ;QACN,KAAK,0JAAA,CAAA,cAAW,CAAC,GAAG;YAClB,OAAO;gBACL,WAAW,OAAO,QAAQ;YAC5B;QACF,KAAK,0JAAA,CAAA,cAAW,CAAC,MAAM;YACrB,OAAO;gBACL,cAAc,OAAO,QAAQ;YAC/B;QACF,KAAK,0JAAA,CAAA,cAAW,CAAC,KAAK;YACpB,OAAO;gBACL,aAAa,OAAO,QAAQ;YAC9B;QACF,KAAK,0JAAA,CAAA,cAAW,CAAC,IAAI;YACnB,OAAO;gBACL,YAAY,OAAO,QAAQ;YAC7B;QACF,KAAK,0JAAA,CAAA,cAAW,CAAC,UAAU;YACzB,OAAO;gBACL,aAAa,OAAO,QAAQ;gBAC5B,YAAY,OAAO,QAAQ;YAC7B;QACF,KAAK,0JAAA,CAAA,cAAW,CAAC,QAAQ;YACvB,OAAO;gBACL,WAAW,OAAO,QAAQ;gBAC1B,cAAc,OAAO,QAAQ;YAC/B;QACF;YACE,OAAO;gBACL,QAAQ,OAAO,QAAQ;YACzB;IACJ;AACF;uCAGa", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/breadcrumb/breadcrumb.controller.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCallback } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Theme, useMediaQuery, useTheme } from \"@mui/material\";\r\n\r\nimport { RoutePathEnum } from \"@/enum\";\r\n\r\ninterface IBreadcrumbController {\r\n  getters: {\r\n    isMobileView: boolean;\r\n  };\r\n  handlers: {\r\n    handleClick: (path: RoutePathEnum, forwardParam: boolean) => void;\r\n  };\r\n}\r\n\r\n/**\r\n * Breadcrumb Controller\r\n * @return {IBreadcrumbController}\r\n */\r\nexport function BreadcrumbController(): IBreadcrumbController {\r\n  const navigate = useRouter();\r\n  const theme: Theme = useTheme();\r\n  const isMobileView: boolean = useMediaQuery(theme.breakpoints.down(\"md\"));\r\n\r\n  /**\r\n   * Add the Parameters to Path to redirect\r\n   * @param {RoutePathEnum} path Path to Redirect\r\n   * @param {boolean} isForwardParam\r\n   */\r\n  const handleClick = useCallback(\r\n    (path: RoutePathEnum, isForwardParam: boolean): void => {\r\n      if (path === RoutePathEnum.NONE) return;\r\n      if (!isForwardParam) {\r\n        navigate.push(path);\r\n        return;\r\n      }\r\n    },\r\n    [navigate]\r\n  );\r\n\r\n  return {\r\n    getters: {\r\n      isMobileView,\r\n    },\r\n    handlers: { handleClick },\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAAA;AAEA;AAAA;;AANA;;;;;AAqBO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACzB,MAAM,QAAe,CAAA,GAAA,iMAAA,CAAA,WAAQ,AAAD;IAC5B,MAAM,eAAwB,CAAA,GAAA,uNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,WAAW,CAAC,IAAI,CAAC;IAEnE;;;;GAIC,GACD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAC5B,CAAC,MAAqB;YACpB,IAAI,SAAS,oIAAA,CAAA,gBAAa,CAAC,IAAI,EAAE;YACjC,IAAI,CAAC,gBAAgB;gBACnB,SAAS,IAAI,CAAC;gBACd;YACF;QACF;wDACA;QAAC;KAAS;IAGZ,OAAO;QACL,SAAS;YACP;QACF;QACA,UAAU;YAAE;QAAY;IAC1B;AACF;GA3BgB;;QACG,qIAAA,CAAA,YAAS;QACL,iMAAA,CAAA,WAAQ;QACC,uNAAA,CAAA,gBAAa;;;KAH7B", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/breadcrumb/breadcrumb.style.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { Box, styled, Typography } from '@mui/material';\r\n\r\nexport const BaseBreadcrumbCard = styled(Box)(() => ({\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n}));\r\n\r\nexport const BreadcrumbText = styled(Typography, {\r\n  shouldForwardProp: (props) => props !== 'clickable',\r\n})<{ clickable?: boolean }>(({ theme, clickable = false }) => ({\r\n  cursor: clickable ? 'pointer' : 'default',\r\n  color: clickable ? theme.palette.primary.main : theme.palette.text.primary,\r\n}));\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAFA;;AAIO,MAAM,qBAAqB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,IAAM,CAAC;QACnD,SAAS;QACT,YAAY;IACd,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,yMAAA,CAAA,aAAU,EAAE;IAC/C,mBAAmB,CAAC,QAAU,UAAU;AAC1C,GAA4B,CAAC,EAAE,KAAK,EAAE,YAAY,KAAK,EAAE,GAAK,CAAC;QAC7D,QAAQ,YAAY,YAAY;QAChC,OAAO,YAAY,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;IAC5E,CAAC", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/breadcrumb/breadcrumb.tsx"], "sourcesContent": ["import React, { ReactElement, ReactNode, useCallback, useMemo } from \"react\";\r\nimport Box from \"@mui/material/Box\";\r\nimport BaseBreadcrumbs from \"@mui/material/Breadcrumbs\";\r\nimport { faArrowLeft } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\nimport RoutePathEnum from \"@/enum/routePaths.enum\";\r\nimport SpacingEnum from \"@/components/common/spacing/enum\";\r\nimport Spacing from \"@/components/common/spacing/spacing\";\r\nimport { Icon } from \"@/components/common/icon\";\r\nimport { StringHelper } from \"@/helpers\";\r\n\r\nimport { BreadcrumbController } from \"./breadcrumb.controller\";\r\nimport { IBreadcrumbDisplay, IBreadcrumbsProps } from \"./interface\";\r\nimport { BaseBreadcrumbCard, BreadcrumbText } from \"./breadcrumb.style\";\r\n\r\n/**\r\n * Breadcrumb Component\r\n * @param {IBreadcrumbsProps} breadcrumbs\r\n * @return {ReactElement}\r\n */\r\nexport function Breadcrumb({ breadcrumbs }: IBreadcrumbsProps): ReactElement {\r\n  const { handlers, getters } = BreadcrumbController();\r\n  const { isMobileView } = getters;\r\n  const { handleClick } = handlers;\r\n\r\n  /**\r\n   * Breadcrumb text data\r\n   * @param {IBreadcrumbDisplay} displayBreadcrumbData\r\n   * @param {number} index\r\n   * @return {ReactElement}\r\n   */\r\n  const breadcrumbTextData = useCallback(\r\n    (\r\n      displayBreadcrumbData: IBreadcrumbDisplay,\r\n      index: number\r\n    ): ReactElement => {\r\n      const { name, path, forwardParam, clickable } = displayBreadcrumbData;\r\n      // Make breadcrumbs clickable if they have a valid path and are either:\r\n      // 1. The first breadcrumb (index 0), or\r\n      // 2. Explicitly marked as clickable\r\n      const isClickable: boolean = path !== RoutePathEnum.NONE && (index === 0 || clickable === true);\r\n      return (\r\n        <Box key={StringHelper.generateUID(name, index)}>\r\n          <BreadcrumbText\r\n            variant=\"body2\"\r\n            clickable={isClickable}\r\n            onClick={() => isClickable && handleClick(path, forwardParam)}\r\n          >\r\n            {isMobileView && <Icon icon={faArrowLeft} />}\r\n            {name}\r\n          </BreadcrumbText>\r\n          {isMobileView && <Spacing spacing={2} variant={SpacingEnum.BOTTOM} />}\r\n        </Box>\r\n      );\r\n    },\r\n    [handleClick, isMobileView]\r\n  );\r\n\r\n  /**\r\n   * BreadCrumb for desktop view\r\n   */\r\n  const desktopView: ReactNode = useMemo(\r\n    () => breadcrumbs.map(breadcrumbTextData),\r\n    [breadcrumbs, breadcrumbTextData]\r\n  );\r\n\r\n  /**\r\n   * BreadCrumb for mobile view\r\n   */\r\n  const mobileView: ReactNode = useMemo(() => {\r\n    const reversedBreadcrumbs: IBreadcrumbDisplay[] = [\r\n      ...breadcrumbs,\r\n    ].reverse();\r\n    // For mobile view, we'll only show the first clickable breadcrumb\r\n    // Since we're using reversed breadcrumbs, we need to calculate the original index\r\n    for (let i = 0; i < reversedBreadcrumbs.length; i += 1) {\r\n      const breadcrumb: IBreadcrumbDisplay = reversedBreadcrumbs[i];\r\n      const originalIndex = breadcrumbs.length - 1 - i;\r\n      // Show the breadcrumb if it's clickable (either first one or explicitly marked)\r\n      if ((originalIndex === 0 || breadcrumb.clickable === true) && breadcrumb.path !== RoutePathEnum.NONE) {\r\n        return breadcrumbTextData(breadcrumb, originalIndex);\r\n      }\r\n    }\r\n    return <Box />;\r\n  }, [breadcrumbs, breadcrumbTextData]);\r\n\r\n  return (\r\n    <BaseBreadcrumbCard>\r\n      <BaseBreadcrumbs aria-label=\"breadcrumb\">\r\n        {isMobileView ? mobileView : desktopView}\r\n      </BaseBreadcrumbs>\r\n    </BaseBreadcrumbCard>\r\n  );\r\n}\r\n\r\nexport default React.memo(Breadcrumb);\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAEA;AAEA;;;;;;;;;;;;;;AAOO,SAAS,WAAW,EAAE,WAAW,EAAqB;;IAC3D,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,uBAAoB,AAAD;IACjD,MAAM,EAAE,YAAY,EAAE,GAAG;IACzB,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB;;;;;GAKC,GACD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDACnC,CACE,uBACA;YAEA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG;YAChD,uEAAuE;YACvE,wCAAwC;YACxC,oCAAoC;YACpC,MAAM,cAAuB,SAAS,oIAAA,CAAA,UAAa,CAAC,IAAI,IAAI,CAAC,UAAU,KAAK,cAAc,IAAI;YAC9F,qBACE,6LAAC,kJAAA,CAAA,UAAG;;kCACF,6LAAC,mKAAA,CAAA,iBAAc;wBACb,SAAQ;wBACR,WAAW;wBACX,OAAO;0EAAE,IAAM,eAAe,YAAY,MAAM;;;4BAE/C,8BAAgB,6LAAC,+IAAA,CAAA,OAAI;gCAAC,MAAM,2KAAA,CAAA,cAAW;;;;;;4BACvC;;;;;;;oBAEF,8BAAgB,6LAAC,qJAAA,CAAA,UAAO;wBAAC,SAAS;wBAAG,SAAS,0JAAA,CAAA,UAAW,CAAC,MAAM;;;;;;;eATzD,qIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,MAAM;;;;;QAY7C;qDACA;QAAC;QAAa;KAAa;IAG7B;;GAEC,GACD,MAAM,cAAyB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CACnC,IAAM,YAAY,GAAG,CAAC;0CACtB;QAAC;QAAa;KAAmB;IAGnC;;GAEC,GACD,MAAM,aAAwB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE;YACpC,MAAM,sBAA4C;mBAC7C;aACJ,CAAC,OAAO;YACT,kEAAkE;YAClE,kFAAkF;YAClF,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,MAAM,EAAE,KAAK,EAAG;gBACtD,MAAM,aAAiC,mBAAmB,CAAC,EAAE;gBAC7D,MAAM,gBAAgB,YAAY,MAAM,GAAG,IAAI;gBAC/C,gFAAgF;gBAChF,IAAI,CAAC,kBAAkB,KAAK,WAAW,SAAS,KAAK,IAAI,KAAK,WAAW,IAAI,KAAK,oIAAA,CAAA,UAAa,CAAC,IAAI,EAAE;oBACpG,OAAO,mBAAmB,YAAY;gBACxC;YACF;YACA,qBAAO,6LAAC,kJAAA,CAAA,UAAG;;;;;QACb;yCAAG;QAAC;QAAa;KAAmB;IAEpC,qBACE,6LAAC,mKAAA,CAAA,qBAAkB;kBACjB,cAAA,6LAAC,kKAAA,CAAA,UAAe;YAAC,cAAW;sBACzB,eAAe,aAAa;;;;;;;;;;;AAIrC;GAzEgB;KAAA;2DA2ED,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/breadcrumb/interface/index.ts"], "sourcesContent": ["export * from './breadcrumbs.interface';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/breadcrumb/index.ts"], "sourcesContent": ["import { Breadcrumb } from './breadcrumb';\r\n\r\nexport * from './breadcrumb';\r\nexport * from './interface';\r\n\r\nexport default Breadcrumb;\r\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;;uCAEe,2JAAA,CAAA,aAAU", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/spacing/index.ts"], "sourcesContent": ["import { Spacing } from './spacing';\r\n\r\nexport * from './spacing';\r\nexport * from './enum';\r\n\r\nexport default Spacing;\r\n"], "names": [], "mappings": ";;;AAAA;AAGA;;;;uCAEe,qJAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/error/error.style.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { styled, Box } from '@mui/material';\r\n\r\nexport const ErrorWrapper = styled(Box)(({ theme }) => ({\r\n  textAlign: 'center',\r\n  color: theme.typography.h2.color,\r\n  fontWeight: theme.typography.fontWeightMedium,\r\n  maxWidth: '800px',\r\n\r\n  '.lottieBox': {\r\n    height: '200px',\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;AAFA;;AAIO,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACtD,WAAW;QACX,OAAO,MAAM,UAAU,CAAC,EAAE,CAAC,KAAK;QAChC,YAAY,MAAM,UAAU,CAAC,gBAAgB;QAC7C,UAAU;QAEV,cAAc;YACZ,QAAQ;QACV;IACF,CAAC", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/error/error.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactElement, useEffect, useState } from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { Backdrop, Typography } from \"@mui/material\";\r\n\r\nimport { Spacing, SpacingEnum } from \"@/components/common/spacing\";\r\n\r\nimport { ErrorWrapper } from \"./error.style\";\r\n\r\n// Dynamically import Lottie with no SSR\r\nconst Lottie = dynamic(() => import(\"lottie-react\"), { ssr: false });\r\n\r\ninterface IErrorProps {\r\n  title: string;\r\n  caption?: string;\r\n  hasError: boolean;\r\n  animation?: unknown;\r\n}\r\n\r\n/**\r\n * Error Component\r\n * @param {IErrorProps} props\r\n * @return {ReactElement}\r\n */\r\nexport function Error({\r\n  title,\r\n  caption,\r\n  hasError,\r\n  animation,\r\n}: IErrorProps): ReactElement {\r\n  // Use state to track client-side rendering\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  // Only render on client-side\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  if (!hasError) {\r\n    return <></>;\r\n  }\r\n\r\n  return (\r\n    <Backdrop\r\n      open={hasError}\r\n      style={{ backgroundColor: \"white\", zIndex: 1000 }}\r\n    >\r\n      <ErrorWrapper>\r\n        {isMounted && (\r\n          <Lottie\r\n            animationData={animation}\r\n            loop\r\n            renderer=\"svg\"\r\n            autoplay\r\n            className=\"lottieBox\"\r\n          />\r\n        )}\r\n        <Typography variant=\"h2\">{title}</Typography>\r\n        <Spacing spacing={3} variant={SpacingEnum.TOP} />\r\n        <Typography variant=\"h4\">{caption}</Typography>\r\n      </ErrorWrapper>\r\n    </Backdrop>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAEA;;;;AARA;;;;;;AAUA,wCAAwC;AACxC,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAAgC,KAAK;;KAAtD;AAcC,SAAS,MAAM,EACpB,KAAK,EACL,OAAO,EACP,QAAQ,EACR,SAAS,EACG;;IACZ,2CAA2C;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,aAAa;QACf;0BAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,qBAAO;IACT;IAEA,qBACE,6LAAC,mMAAA,CAAA,WAAQ;QACP,MAAM;QACN,OAAO;YAAE,iBAAiB;YAAS,QAAQ;QAAK;kBAEhD,cAAA,6LAAC,mKAAA,CAAA,eAAY;;gBACV,2BACC,6LAAC;oBACC,eAAe;oBACf,IAAI;oBACJ,UAAS;oBACT,QAAQ;oBACR,WAAU;;;;;;8BAGd,6LAAC,yMAAA,CAAA,aAAU;oBAAC,SAAQ;8BAAM;;;;;;8BAC1B,6LAAC,qJAAA,CAAA,UAAO;oBAAC,SAAS;oBAAG,SAAS,0JAAA,CAAA,cAAW,CAAC,GAAG;;;;;;8BAC7C,6LAAC,yMAAA,CAAA,aAAU;oBAAC,SAAQ;8BAAM;;;;;;;;;;;;;;;;;AAIlC;GAvCgB;MAAA", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/error/index.ts"], "sourcesContent": ["export * from './error';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 579, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/noDataFound/noDataFound.style.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { styled, Box } from '@mui/material';\r\n\r\nexport const NoDataFoundWrapper = styled(Box, {\r\n  shouldForwardProp: (props) => props !== 'height',\r\n})(({ theme, height = '250px' }) => ({\r\n  textAlign: 'center',\r\n  fontWeight: theme.typography.fontWeightMedium,\r\n  marginTop: theme.spacing(4),\r\n\r\n  '.lottieBox': {\r\n    height,\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;AAFA;;AAIO,MAAM,qBAAqB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE;IAC5C,mBAAmB,CAAC,QAAU,UAAU;AAC1C,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,OAAO,EAAE,GAAK,CAAC;QACnC,WAAW;QACX,YAAY,MAAM,UAAU,CAAC,gBAAgB;QAC7C,WAAW,MAAM,OAAO,CAAC;QAEzB,cAAc;YACZ;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 605, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/noDataFound/noDataFound.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactElement, useEffect, useState } from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { AnimationSegment } from \"lottie-web\";\r\n\r\nimport { Typography } from \"@mui/material\";\r\nimport { Spacing, SpacingEnum } from \"@/components/common/spacing\";\r\n\r\nimport { NoDataFoundWrapper } from \"./noDataFound.style\";\r\n\r\n// Dynamically import Lottie with no SSR\r\nconst Lottie = dynamic(() => import(\"lottie-react\"), { ssr: false });\r\n\r\ninterface INoDataFoundProps {\r\n  title: string;\r\n  height?: string;\r\n  caption?: string;\r\n  animation?: unknown;\r\n  keyframe?: AnimationSegment;\r\n}\r\n/**\r\n * No Data Found Component\r\n * @param {INoDataFoundProps} props\r\n * @return {ReactElement}\r\n */\r\nexport function NoDataFound({\r\n  title,\r\n  height,\r\n  caption,\r\n  animation,\r\n  keyframe,\r\n}: INoDataFoundProps): ReactElement {\r\n  // Use state to track client-side rendering\r\n  const [isMounted, setIsMounted] = useState(false);\r\n\r\n  // Only render on client-side\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  return (\r\n    <NoDataFoundWrapper height={height}>\r\n      {isMounted && (\r\n        <Lottie\r\n          animationData={animation}\r\n          loop\r\n          renderer=\"svg\"\r\n          autoplay\r\n          className=\"lottieBox\"\r\n          initialSegment={keyframe}\r\n        />\r\n      )}\r\n      <Spacing spacing={3} variant={SpacingEnum.VERTICAL}>\r\n        <Typography variant=\"h6\">{title}</Typography>\r\n        <Spacing spacing={1} variant={SpacingEnum.TOP} />\r\n        <Typography variant=\"body1\">{caption}</Typography>\r\n      </Spacing>\r\n    </NoDataFoundWrapper>\r\n  );\r\n}\r\n\r\nexport default NoDataFound;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAGA;AACA;AAAA;AAAA;AAEA;;;;AATA;;;;;;AAWA,wCAAwC;AACxC,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAAgC,KAAK;;KAAtD;AAcC,SAAS,YAAY,EAC1B,KAAK,EACL,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACU;;IAClB,2CAA2C;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,aAAa;QACf;gCAAG,EAAE;IAEL,qBACE,6LAAC,+KAAA,CAAA,qBAAkB;QAAC,QAAQ;;YACzB,2BACC,6LAAC;gBACC,eAAe;gBACf,IAAI;gBACJ,UAAS;gBACT,QAAQ;gBACR,WAAU;gBACV,gBAAgB;;;;;;0BAGpB,6LAAC,qJAAA,CAAA,UAAO;gBAAC,SAAS;gBAAG,SAAS,0JAAA,CAAA,cAAW,CAAC,QAAQ;;kCAChD,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;kCAAM;;;;;;kCAC1B,6LAAC,qJAAA,CAAA,UAAO;wBAAC,SAAS;wBAAG,SAAS,0JAAA,CAAA,cAAW,CAAC,GAAG;;;;;;kCAC7C,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;kCAAS;;;;;;;;;;;;;;;;;;AAIrC;GAlCgB;MAAA;uCAoCD", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/noDataFound/index.ts"], "sourcesContent": ["import NoDataFound from './noDataFound';\r\n\r\nexport * from './noDataFound';\r\nexport default NoDataFound;\r\n"], "names": [], "mappings": ";;;AAAA;;;uCAGe,sKAAA,CAAA,UAAW", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/error/index.ts"], "sourcesContent": ["export * from './error';\r\nexport * from './noDataFound';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/pageHeader/pageHeader.style.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Box, Grid, styled } from \"@mui/material\";\r\n\r\nexport const GridActionItem = styled(Grid)(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n\r\n  [theme.breakpoints.down(\"sm\")]: {\r\n    width: \"100%\",\r\n  },\r\n}));\r\n\r\nexport const PageHeaderBox = styled(Box)(({ theme }) => ({\r\n  width: \"100%\",\r\n  marginBottom: theme.spacing(1),\r\n  position: \"sticky\",\r\n  top: 0,\r\n  zIndex: 1100, // Higher than default AppBar z-index (1000)\r\n}));\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAFA;;AAIO,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,uLAAA,CAAA,OAAI,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,SAAS;QACT,YAAY;QAEZ,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,OAAO;QACT;IACF,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,OAAO;QACP,cAAc,MAAM,OAAO,CAAC;QAC5B,UAAU;QACV,KAAK;QACL,QAAQ;IACV,CAAC", "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/pageHeader/pageHeader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactElement, ReactNode } from \"react\";\r\nimport { Grid, Typography } from \"@mui/material\";\r\nimport { Breadcrumb, IBreadcrumbDisplay } from \"@/components/common/breadcrumb\";\r\nimport { GridActionItem, PageHeaderBox } from \"./pageHeader.style\";\r\n\r\ninterface IPageHeader {\r\n  title: string;\r\n  breadcrumbs?: IBreadcrumbDisplay[];\r\n  actions?: ReactNode;\r\n  subtitle?: string;\r\n}\r\n\r\n/**\r\n * Page Header Component\r\n * @param {IPageHeader} props\r\n * @return {ReactElement}\r\n */\r\nexport function PageHeader(props: IPageHeader): ReactElement {\r\n  const {\r\n    title,\r\n    actions,\r\n    breadcrumbs,\r\n    subtitle,\r\n  } = props;\r\n\r\n  return (\r\n    <PageHeaderBox>\r\n      <Grid container justifyContent=\"space-between\" alignItems=\"center\">\r\n        <Grid>\r\n          <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\r\n            {title}\r\n          </Typography>\r\n          {subtitle && <Typography variant=\"body2\">{subtitle}</Typography>}\r\n          {breadcrumbs && (\r\n            <Breadcrumb breadcrumbs={breadcrumbs ? breadcrumbs : []} />\r\n          )}\r\n        </Grid>\r\n        <GridActionItem sx={{ display: \"flex\", alignItems: \"center\", gap: 2 }}>\r\n          {actions}\r\n        </GridActionItem>\r\n      </Grid>\r\n    </PageHeaderBox>\r\n  );\r\n}\r\n\r\nexport default PageHeader;\r\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AAAA;AACA;AALA;;;;;AAmBO,SAAS,WAAW,KAAkB;IAC3C,MAAM,EACJ,KAAK,EACL,OAAO,EACP,WAAW,EACX,QAAQ,EACT,GAAG;IAEJ,qBACE,6LAAC,oKAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC,uLAAA,CAAA,OAAI;YAAC,SAAS;YAAC,gBAAe;YAAgB,YAAW;;8BACxD,6LAAC,uLAAA,CAAA,OAAI;;sCACH,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAK,IAAI;gCAAE,YAAY;4BAAI;sCAC5C;;;;;;wBAEF,0BAAY,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;sCAAS;;;;;;wBACzC,6BACC,6LAAC,2JAAA,CAAA,aAAU;4BAAC,aAAa,cAAc,cAAc,EAAE;;;;;;;;;;;;8BAG3D,6LAAC,oKAAA,CAAA,iBAAc;oBAAC,IAAI;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,KAAK;oBAAE;8BACjE;;;;;;;;;;;;;;;;;AAKX;KA1BgB;uCA4BD", "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/pageHeader/index.ts"], "sourcesContent": ["import PageHeader from \"./pageHeader\";\r\n\r\nexport * from \"./pageHeader\";\r\nexport default PageHeader;\r\n"], "names": [], "mappings": ";;;AAAA;;;uCAGe,2JAAA,CAAA,UAAU", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/textInputField/enum/textInputFieldEvent.enum.ts"], "sourcesContent": ["export enum TextInputFieldEventEnum {\r\n  HTML,\r\n  FORCE,\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,IAAA,AAAK,iDAAA;;;WAAA", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/textInputField/enum/index.ts"], "sourcesContent": ["export * from './textInputFieldEvent.enum';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/textInputField/textInputField.controller.ts"], "sourcesContent": ["\"use client\"\r\n\r\nimport { OutlinedTextFieldProps } from '@mui/material';\r\nimport {\r\n  ChangeEvent,\r\n  ForwardedRef,\r\n  useCallback,\r\n  useImperativeHandle,\r\n  useState,\r\n} from 'react';\r\n\r\nimport { TextInputFieldEventEnum } from './enum';\r\nimport {\r\n  ITextInputFieldData,\r\n  ITextInputFieldRef,\r\n  IValidationResponse,\r\n} from './interface/textInputField.interface';\r\n\r\ninterface IProps {\r\n  onChange: (event: ITextInputFieldData) => void;\r\n  value: string;\r\n  validation: (value: string) => IValidationResponse;\r\n  InputProps?: OutlinedTextFieldProps['InputProps'];\r\n  ref: ForwardedRef<ITextInputFieldRef>;\r\n}\r\n\r\ninterface ITextInputFieldControllerResponse {\r\n  getters: {\r\n    isError: boolean;\r\n    helperText: string;\r\n  };\r\n  handlers: {\r\n    onChangeLocal: (event: ChangeEvent<HTMLInputElement>) => void;\r\n  };\r\n}\r\n\r\n/**\r\n * Text Input Field Controller\r\n * @param {IProps} props\r\n * @return {ITextInputFieldControllerResponse}\r\n */\r\nexport const useTextInputFieldController = (\r\n  props: IProps,\r\n): ITextInputFieldControllerResponse => {\r\n  const { onChange, validation, ref, value } = props;\r\n  const [isError, setIsError] = useState<boolean>(false);\r\n  const [helperText, setHelperText] = useState<string>('');\r\n\r\n  // Set Error\r\n  const setError = (validationData: IValidationResponse) => {\r\n    setIsError(!validationData.isValid);\r\n    if (!validationData.isValid) {\r\n      setHelperText(validationData.message);\r\n    } else {\r\n      setHelperText('');\r\n    }\r\n  };\r\n\r\n  /**\r\n   * @function onChangeLocal On Change Updates the Local State as well as Validation the Value given by the User\r\n   * @param {ChangeEvent<HTMLInputElement>} event\r\n   */\r\n  const onChangeLocal = useCallback(\r\n    (event: ChangeEvent<HTMLInputElement>): void => {\r\n      const valueData = event.target.value;\r\n      const validationResult: IValidationResponse = validation(valueData);\r\n      const customEvent: ITextInputFieldData = {\r\n        errorMsg: validationResult.message || '',\r\n        value: valueData,\r\n        event,\r\n        eventType: TextInputFieldEventEnum.HTML,\r\n      };\r\n\r\n      onChange(customEvent);\r\n      setError(validationResult);\r\n    },\r\n    [onChange, validation],\r\n  );\r\n\r\n  useImperativeHandle(ref, () => ({\r\n    validateValue: (): boolean => {\r\n      const validationResponse: IValidationResponse = validation\r\n        ? validation(value)\r\n        : ({} as IValidationResponse);\r\n      setError(validationResponse);\r\n      setHelperText(validationResponse.message);\r\n      return validationResponse.isValid;\r\n    },\r\n  }));\r\n\r\n  return {\r\n    getters: {\r\n      isError,\r\n      helperText,\r\n    },\r\n    handlers: {\r\n      onChangeLocal,\r\n    },\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAGA;AAQA;AAAA;;AAXA;;;AAyCO,MAAM,8BAA8B,CACzC;;IAEA,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,YAAY;IACZ,MAAM,WAAW,CAAC;QAChB,WAAW,CAAC,eAAe,OAAO;QAClC,IAAI,CAAC,eAAe,OAAO,EAAE;YAC3B,cAAc,eAAe,OAAO;QACtC,OAAO;YACL,cAAc;QAChB;IACF;IAEA;;;GAGC,GACD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAC9B,CAAC;YACC,MAAM,YAAY,MAAM,MAAM,CAAC,KAAK;YACpC,MAAM,mBAAwC,WAAW;YACzD,MAAM,cAAmC;gBACvC,UAAU,iBAAiB,OAAO,IAAI;gBACtC,OAAO;gBACP;gBACA,WAAW,uLAAA,CAAA,0BAAuB,CAAC,IAAI;YACzC;YAEA,SAAS;YACT,SAAS;QACX;iEACA;QAAC;QAAU;KAAW;IAGxB,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;2DAAK,IAAM,CAAC;gBAC9B,aAAa;uEAAE;wBACb,MAAM,qBAA0C,aAC5C,WAAW,SACV,CAAC;wBACN,SAAS;wBACT,cAAc,mBAAmB,OAAO;wBACxC,OAAO,mBAAmB,OAAO;oBACnC;;YACF,CAAC;;IAED,OAAO;QACL,SAAS;YACP;YACA;QACF;QACA,UAAU;YACR;QACF;IACF;AACF;GA1Da", "debugId": null}}, {"offset": {"line": 1031, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/textInputField/textInputField.tsx"], "sourcesContent": ["// import React, { ForwardedRef, forwardRef } from \"react\";\r\n// import { BaseTextFieldProps, OutlinedTextFieldProps } from \"@mui/material\";\r\n\r\n// import { useTextInputFieldController } from \"./textInputField.controller\";\r\n// import {\r\n//   ITextInputFieldData,\r\n//   ITextInputFieldRef,\r\n//   IValidationResponse,\r\n// } from \"./interface/textInputField.interface\";\r\n// import { StyledTextField } from \"./textInputField.style\";\r\n\r\n// export type TranslationFunctionType = (value: string) => string;\r\n\r\n// interface IProps extends BaseTextFieldProps {\r\n//   onChange: (event: ITextInputFieldData) => void;\r\n//   value: string;\r\n//   validation: (value: string) => IValidationResponse;\r\n//   InputProps?: OutlinedTextFieldProps[\"InputProps\"];\r\n// }\r\n\r\n// /**\r\n//  * @component {Text Input Field}\r\n//  * @param {IProps}\r\n//  * @param {ForwardedRef<ITextInputFieldRef>}\r\n//  * @return {ForwardRefExoticComponent}\r\n//  */\r\n// export const TextInputField = forwardRef(\r\n//   (props: IProps, ref: ForwardedRef<ITextInputFieldRef>) => {\r\n//     const { value, multiline, InputProps, onChange, validation, ...rest } =\r\n//       props;\r\n//     const { getters, handlers } = useTextInputFieldController({\r\n//       value,\r\n//       ref,\r\n//       onChange,\r\n//       validation,\r\n//     });\r\n//     const { helperText, isError } = getters;\r\n//     const { onChangeLocal } = handlers;\r\n\r\n//     return (\r\n//       <StyledTextField\r\n//         value={value}\r\n//         helperText={helperText}\r\n//         variant=\"outlined\"\r\n//         error={isError}\r\n//         size=\"medium\"\r\n//         multiline={multiline}\r\n//         InputProps={{ ...InputProps }}\r\n//         {...rest}\r\n//         onChange={onChangeLocal}\r\n//       />\r\n//     );\r\n//   }\r\n// );\r\n\r\n// export default TextInputField;\r\n\r\n\"use client\";\r\n\r\nimport React, { ForwardedRef, forwardRef, useEffect, useState } from \"react\";\r\nimport {\r\n  TextField,\r\n  BaseTextFieldProps,\r\n  OutlinedTextFieldProps,\r\n} from \"@mui/material\";\r\n\r\nimport { useTextInputFieldController } from \"./textInputField.controller\";\r\nimport {\r\n  ITextInputFieldData,\r\n  ITextInputFieldRef,\r\n  IValidationResponse,\r\n} from \"./interface/textInputField.interface\";\r\n\r\ninterface IProps extends BaseTextFieldProps {\r\n  onChange: (event: ITextInputFieldData) => void;\r\n  value: string;\r\n  validation: (value: string) => IValidationResponse;\r\n  InputProps?: OutlinedTextFieldProps[\"InputProps\"];\r\n}\r\n\r\n/**\r\n * @component {TextInputField}\r\n * @param {IProps}\r\n * @param {ForwardedRef<ITextInputFieldRef>}\r\n * @return {ForwardRefExoticComponent}\r\n */\r\n\r\nexport const TextInputField = forwardRef(\r\n  (props: IProps, ref: ForwardedRef<ITextInputFieldRef>) => {\r\n    const { value, multiline, InputProps, onChange, validation, ...rest } =\r\n      props;\r\n    const { getters, handlers } = useTextInputFieldController({\r\n      value,\r\n      ref,\r\n      onChange,\r\n      validation,\r\n    });\r\n    const { helperText, isError } = getters;\r\n    const { onChangeLocal } = handlers;\r\n    const [mounted, setMounted] = useState(false);\r\n\r\n    // Only run after client-side hydration is complete\r\n    useEffect(() => {\r\n      setMounted(true);\r\n    }, []);\r\n\r\n    return (\r\n      <div className=\"relative w-full\" suppressHydrationWarning>\r\n        <TextField\r\n          value={value}\r\n          helperText={helperText}\r\n          variant=\"outlined\"\r\n          error={isError}\r\n          size=\"medium\"\r\n          multiline={multiline}\r\n          InputProps={{\r\n            ...InputProps,\r\n            className:\r\n              \"bg-transparent border-gray-300 dark:border-gray-700 rounded-md focus:ring-2 focus:ring-blue-500\",\r\n          }}\r\n          {...rest}\r\n          onChange={onChangeLocal}\r\n          className=\"w-full focus:ring-red-500 text-red-500\"\r\n          suppressHydrationWarning\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nTextInputField.displayName = \"TextInputField\";\r\n\r\nexport default TextInputField;\r\n"], "names": [], "mappings": "AAAA,2DAA2D;AAC3D,8EAA8E;AAE9E,6EAA6E;AAC7E,WAAW;AACX,yBAAyB;AACzB,wBAAwB;AACxB,yBAAyB;AACzB,iDAAiD;AACjD,4DAA4D;AAE5D,mEAAmE;AAEnE,gDAAgD;AAChD,oDAAoD;AACpD,mBAAmB;AACnB,wDAAwD;AACxD,uDAAuD;AACvD,IAAI;AAEJ,MAAM;AACN,mCAAmC;AACnC,qBAAqB;AACrB,+CAA+C;AAC/C,yCAAyC;AACzC,MAAM;AACN,4CAA4C;AAC5C,gEAAgE;AAChE,8EAA8E;AAC9E,eAAe;AACf,kEAAkE;AAClE,eAAe;AACf,aAAa;AACb,kBAAkB;AAClB,oBAAoB;AACpB,UAAU;AACV,+CAA+C;AAC/C,0CAA0C;AAE1C,eAAe;AACf,yBAAyB;AACzB,wBAAwB;AACxB,kCAAkC;AAClC,6BAA6B;AAC7B,0BAA0B;AAC1B,wBAAwB;AACxB,gCAAgC;AAChC,yCAAyC;AACzC,oBAAoB;AACpB,mCAAmC;AACnC,WAAW;AACX,SAAS;AACT,MAAM;AACN,KAAK;AAEL,iCAAiC;;;;;;AAIjC;AACA;AAMA;;;AATA;;;;AA8BO,MAAM,+BAAiB,GAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,UACrC,CAAC,OAAe;;IACd,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,MAAM,GACnE;IACF,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gLAAA,CAAA,8BAA2B,AAAD,EAAE;QACxD;QACA;QACA;QACA;IACF;IACA,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG;IAChC,MAAM,EAAE,aAAa,EAAE,GAAG;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,mDAAmD;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;QACb;mCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;QAAkB,wBAAwB;kBACvD,cAAA,6LAAC,sMAAA,CAAA,YAAS;YACR,OAAO;YACP,YAAY;YACZ,SAAQ;YACR,OAAO;YACP,MAAK;YACL,WAAW;YACX,YAAY;gBACV,GAAG,UAAU;gBACb,WACE;YACJ;YACC,GAAG,IAAI;YACR,UAAU;YACV,WAAU;YACV,wBAAwB;;;;;;;;;;;AAIhC;;QApCgC,gLAAA,CAAA,8BAA2B;;;;QAA3B,gLAAA,CAAA,8BAA2B;;;;AAuC7D,eAAe,WAAW,GAAG;uCAEd", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/textInputField/index.ts"], "sourcesContent": ["import { TextInputField } from './textInputField';\r\n\r\nexport * from './textInputField';\r\nexport * from './enum/textInputFieldEvent.enum';\r\nexport * from './interface/textInputField.interface';\r\nexport default TextInputField;\r\n"], "names": [], "mappings": ";;;AAAA;AAGA;AACA;;;;;uCACe,mKAAA,CAAA,iBAAc", "debugId": null}}, {"offset": {"line": 1207, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/enum/tableComponent.enum.ts"], "sourcesContent": ["export enum TableComponentEnum {\r\n  STRING,\r\n  NUMBER,\r\n  DATE,\r\n  CO<PERSON><PERSON><PERSON><PERSON>,\r\n  ARRAY,\r\n  TAG,\r\n  <PERSON><PERSON><PERSON>EAN,\r\n  IMAGE,\r\n}\r\n\r\nexport enum TableHeadColorEnum {\r\n  DARK = '#1F2937',\r\n  LIGHT = '#F3F4F6',\r\n}\r\n\r\nexport enum TableHeightEnum {\r\n  HEADERHEIGHT = 185,\r\n  RATINGCARDHEIGHT = 70,\r\n  OPTINSEARCHBARHEIGHT = 50,\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,IAAA,AAAK,4CAAA;;;;;;;;;WAAA;;AAWL,IAAA,AAAK,4CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 1243, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableCellGenerator/tableCellGenerator.style.ts"], "sourcesContent": ["import { Box, styled } from '@mui/material';\r\n\r\nimport { Icon } from '../../icon/icon';\r\n\r\nexport const TableImage = styled(Box)({\r\n  height: '35px',\r\n  width: '35px',\r\n  position: 'relative',\r\n  img: {\r\n    borderRadius: '100px',\r\n  },\r\n});\r\n\r\nexport const CalenderIcon = styled(Icon)(({ theme }) => ({\r\n  marginRight: theme.spacing(1),\r\n  pointerEvents: 'none',\r\n  color: 'default',\r\n}));\r\n\r\nexport const TableTextBox = styled(Box)({\r\n  textOverflow: 'ellipsis',\r\n  overflow: 'hidden',\r\n  whiteSpace: 'nowrap',\r\n});\r\n\r\nexport const DateCellWrapper = styled(Box)({\r\n  display: 'flex',\r\n  alignItems: 'start',\r\n  justifyContent: 'start',\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAEA;;;AAEO,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE;IACpC,QAAQ;IACR,OAAO;IACP,UAAU;IACV,KAAK;QACH,cAAc;IAChB;AACF;AAEO,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,+IAAA,CAAA,OAAI,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,aAAa,MAAM,OAAO,CAAC;QAC3B,eAAe;QACf,OAAO;IACT,CAAC;AAEM,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE;IACtC,cAAc;IACd,UAAU;IACV,YAAY;AACd;AAEO,MAAM,kBAAkB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE;IACzC,SAAS;IACT,YAAY;IACZ,gBAAgB;AAClB", "debugId": null}}, {"offset": {"line": 1286, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/enum/index.ts"], "sourcesContent": ["export * from './tableComponent.enum';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableCellGenerator/TableCellGenerator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { ReactElement, useEffect, useRef, useState } from \"react\";\r\nimport Image from \"next/legacy/image\";\r\nimport { Typography, Tooltip } from \"@mui/material\";\r\nimport { faCalendarAlt } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { DateTime } from \"luxon\";\r\n\r\nimport {\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  DateCellWrapper,\r\n  TableImage,\r\n  TableTextBox,\r\n} from \"./tableCellGenerator.style\";\r\nimport { TableComponentEnum } from \"../enum\";\r\nimport { IHeader } from \"../interface\";\r\n\r\ninterface ITableCellGeneratorProps {\r\n  header: IHeader;\r\n  value: unknown;\r\n  index: number;\r\n  onClick?: (value: string) => void;\r\n  onChange?: (value: string, checked: boolean) => void;\r\n  isSelected?: (value: string) => boolean;\r\n  [key: string]: any; // Allow any other props\r\n}\r\n\r\n/**\r\n * Generate Table Cell\r\n * @param {IHeader} header\r\n * @param {unknown} value\r\n * @param {number}index\r\n * @return {ReactElement}\r\n */\r\nexport function TableCellGenerator({\r\n  header,\r\n  value,\r\n  index,\r\n  onClick,\r\n  onChange,\r\n  isSelected,\r\n  ...rest\r\n}: ITableCellGeneratorProps): ReactElement {\r\n  const [isOverflowed, setIsOverflow] = useState<boolean>(false);\r\n  const textElementRef = useRef<HTMLDivElement | null>(null);\r\n  useEffect(() => {\r\n    if (textElementRef.current) {\r\n      setIsOverflow(\r\n        textElementRef.current.scrollWidth > textElementRef.current.clientWidth\r\n      );\r\n    }\r\n  }, []);\r\n\r\n  /**\r\n   * Generate Table Cell Data\r\n   * @return {ReactElement}\r\n   */\r\n  const generate = (): ReactElement => {\r\n    switch (header?.type) {\r\n      case TableComponentEnum.DATE:\r\n        if (!value) {\r\n          return <>-</>;\r\n        }\r\n\r\n        if (value === \"-\") {\r\n          return <>-</>;\r\n        }\r\n        return (\r\n          <DateCellWrapper>\r\n            <CalenderIcon icon={faCalendarAlt} color=\"default\" />\r\n            <Typography variant=\"body2\">\r\n              {DateTime.fromJSDate(new Date(value as string))\r\n                .setLocale(\"en-US\")\r\n                .toLocaleString({\r\n                  ...DateTime.DATETIME_SHORT,\r\n                  hour12: true,\r\n                })}\r\n            </Typography>\r\n          </DateCellWrapper>\r\n        );\r\n\r\n      case TableComponentEnum.COMPONENT:\r\n        if (header.id === 'checkbox' && typeof value === 'string') {\r\n          return <header.component\r\n            value={value}\r\n            index={index}\r\n            onChange={onChange}\r\n            isSelected={isSelected && isSelected(value)}\r\n            {...rest}\r\n          />;\r\n        }\r\n        return <header.component value={value} index={index} onClick={onClick} {...rest} />;\r\n      case TableComponentEnum.TAG:\r\n        return <header.component value={value} />;\r\n      case TableComponentEnum.IMAGE:\r\n        return (\r\n          <TableImage>\r\n            <Image src={value as string} layout=\"fill\" />\r\n          </TableImage>\r\n        );\r\n      case TableComponentEnum.NUMBER:\r\n      default:\r\n        if (!value) {\r\n          return <> - </>;\r\n        }\r\n        if (typeof value === \"object\") {\r\n          return <Typography>{value.toString()}</Typography>;\r\n        }\r\n        return (\r\n          <Tooltip\r\n            title={value as string}\r\n            arrow\r\n            followCursor\r\n            disableHoverListener={!isOverflowed}\r\n          >\r\n            <TableTextBox\r\n              ref={textElementRef}\r\n              style={{\r\n                whiteSpace: \"nowrap\",\r\n                overflow: \"hidden\",\r\n                textOverflow: \"ellipsis\",\r\n              }}\r\n            >\r\n              {(value as string) || \"\"}\r\n            </TableTextBox>\r\n          </Tooltip>\r\n        );\r\n    }\r\n  };\r\n\r\n  return generate();\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAEA;AAMA;AAAA;;;AAdA;;;;;;;;AAkCO,SAAS,mBAAmB,EACjC,MAAM,EACN,KAAK,EACL,KAAK,EACL,OAAO,EACP,QAAQ,EACR,UAAU,EACV,GAAG,MACsB;;IACzB,MAAM,CAAC,cAAc,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACrD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,cACE,eAAe,OAAO,CAAC,WAAW,GAAG,eAAe,OAAO,CAAC,WAAW;YAE3E;QACF;uCAAG,EAAE;IAEL;;;GAGC,GACD,MAAM,WAAW;QACf,OAAQ,QAAQ;YACd,KAAK,yKAAA,CAAA,qBAAkB,CAAC,IAAI;gBAC1B,IAAI,CAAC,OAAO;oBACV,qBAAO;kCAAE;;gBACX;gBAEA,IAAI,UAAU,KAAK;oBACjB,qBAAO;kCAAE;;gBACX;gBACA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;;sCACd,6LAAC,4LAAA,CAAA,eAAY;4BAAC,MAAM,2KAAA,CAAA,gBAAa;4BAAE,OAAM;;;;;;sCACzC,6LAAC,yMAAA,CAAA,aAAU;4BAAC,SAAQ;sCACjB,kLAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,QAC3B,SAAS,CAAC,SACV,cAAc,CAAC;gCACd,GAAG,kLAAA,CAAA,WAAQ,CAAC,cAAc;gCAC1B,QAAQ;4BACV;;;;;;;;;;;;YAKV,KAAK,yKAAA,CAAA,qBAAkB,CAAC,SAAS;gBAC/B,IAAI,OAAO,EAAE,KAAK,cAAc,OAAO,UAAU,UAAU;oBACzD,qBAAO,6LAAC,OAAO,SAAS;wBACtB,OAAO;wBACP,OAAO;wBACP,UAAU;wBACV,YAAY,cAAc,WAAW;wBACpC,GAAG,IAAI;;;;;;gBAEZ;gBACA,qBAAO,6LAAC,OAAO,SAAS;oBAAC,OAAO;oBAAO,OAAO;oBAAO,SAAS;oBAAU,GAAG,IAAI;;;;;;YACjF,KAAK,yKAAA,CAAA,qBAAkB,CAAC,GAAG;gBACzB,qBAAO,6LAAC,OAAO,SAAS;oBAAC,OAAO;;;;;;YAClC,KAAK,yKAAA,CAAA,qBAAkB,CAAC,KAAK;gBAC3B,qBACE,6LAAC,4LAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,0IAAA,CAAA,UAAK;wBAAC,KAAK;wBAAiB,QAAO;;;;;;;;;;;YAG1C,KAAK,yKAAA,CAAA,qBAAkB,CAAC,MAAM;YAC9B;gBACE,IAAI,CAAC,OAAO;oBACV,qBAAO;kCAAE;;gBACX;gBACA,IAAI,OAAO,UAAU,UAAU;oBAC7B,qBAAO,6LAAC,yMAAA,CAAA,aAAU;kCAAE,MAAM,QAAQ;;;;;;gBACpC;gBACA,qBACE,6LAAC,gMAAA,CAAA,UAAO;oBACN,OAAO;oBACP,KAAK;oBACL,YAAY;oBACZ,sBAAsB,CAAC;8BAEvB,cAAA,6LAAC,4LAAA,CAAA,eAAY;wBACX,KAAK;wBACL,OAAO;4BACL,YAAY;4BACZ,UAAU;4BACV,cAAc;wBAChB;kCAEC,AAAC,SAAoB;;;;;;;;;;;QAIhC;IACF;IAEA,OAAO;AACT;GAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableCellGenerator/index.ts"], "sourcesContent": ["export * from './TableCellGenerator';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1509, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableBody/customTableBody.tsx"], "sourcesContent": ["// Any is used in case of Array values which need to be Shown on the Table\r\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport React, { ReactElement } from 'react';\r\nimport { Box, Skeleton, TableBody, TableCell, TableRow } from '@mui/material';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>, StringHelper } from '@/helpers';\r\n\r\nimport { TableCellGenerator } from '../tableCellGenerator';\r\nimport { IHeader, ICustomTablePagination } from '../interface';\r\nimport { TableComponentEnum } from '../enum';\r\n\r\ninterface ICustomTableBodyProps<T> {\r\n  headerField: IHeader[];\r\n  paginationData: ICustomTablePagination;\r\n  isLoading: boolean;\r\n  tableBody: T[];\r\n  onRowClick: (index: number) => void;\r\n  visibleColumns?: string[];\r\n  componentProps?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * Custom table body component\r\n * @param {ICustomTableBodyProps<T>} props\r\n * @return {ReactElement}\r\n */\r\nexport function CustomTableBody<T>(\r\n  props: ICustomTableBodyProps<T>,\r\n): ReactElement {\r\n  const { headerField, tableBody, isLoading, paginationData, onRowClick, componentProps } =\r\n    props;\r\n\r\n  /**\r\n   * Arrange the Table in Form as Header\r\n   * @param {unknown[]} responseArray\r\n   * @param {IHeader[]} header\r\n   * @return {T[]}\r\n   */\r\n  function generateRowsInOrder(\r\n    responseArray: unknown[],\r\n    header: IHeader[],\r\n  ): T[] {\r\n    // Any type because this both can have data of any type\r\n    const finalRowArray: any = [];\r\n    let objectHeaderOrder: any = {};\r\n\r\n    for (let index = 0; index < header.length; index += 1) {\r\n      const headerItem: IHeader = header[index];\r\n      objectHeaderOrder = { [headerItem.id]: null, ...objectHeaderOrder };\r\n    }\r\n\r\n    for (let i = 0; i < responseArray.length; i += 1) {\r\n      const shadowHeaderOrder = { ...objectHeaderOrder };\r\n      const responseElement = responseArray[i];\r\n      const addObjectResource = Object.assign(\r\n        shadowHeaderOrder,\r\n        responseElement,\r\n      );\r\n      finalRowArray.push({ ...addObjectResource });\r\n    }\r\n\r\n    return finalRowArray as T[];\r\n  }\r\n\r\n  /**\r\n   * Arrange the Table in Form as SubHeader\r\n   * @param {unknown[]} responseArray\r\n   * @param {IHeader[]} header\r\n   * @return {T[]}\r\n   */\r\n  function generateSubArrayInOrder(\r\n    responseArray: unknown[],\r\n    header: IHeader[],\r\n  ): T[] {\r\n    const finalRowArray: { [x: string]: string }[] = [];\r\n    let objectOrder = {};\r\n    for (let index = 0; index < header.length; index += 1) {\r\n      const headerItem: IHeader = header[index];\r\n      objectOrder = { [headerItem.id]: null, ...objectOrder };\r\n    }\r\n    Object.keys(objectOrder).forEach((key) => {\r\n      // any-the key will be any type.\r\n      const filterData: any = responseArray.find((res: any) => res.key === key);\r\n      if (filterData && key === filterData.key) {\r\n        finalRowArray.push({\r\n          [key]: filterData.value,\r\n        });\r\n      } else {\r\n        finalRowArray.push({\r\n          [key]: '-',\r\n        });\r\n      }\r\n    });\r\n\r\n    return finalRowArray as unknown as T[];\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <TableBody key={uuidv4().toString()}>\r\n        {Array.from(Array(paginationData.limit).keys()).map(() => (\r\n          <TableRow key={uuidv4().toString()}>\r\n            {headerField.map(\r\n              (item) =>\r\n                !item.hidden &&\r\n                item.type !== TableComponentEnum.ARRAY && (\r\n                  <TableCell key={uuidv4().toString()}>\r\n                    <Skeleton\r\n                      sx={{ width: `${MathHelper.generateRandom(25, 50)}%` }}\r\n                      variant=\"text\"\r\n                      animation=\"wave\"\r\n                    />\r\n                  </TableCell>\r\n                ),\r\n            )}\r\n            {headerField.map(\r\n              (item) =>\r\n                item.subArray &&\r\n                item.subArray.map(\r\n                  (items: IHeader) =>\r\n                    !items.hidden && (\r\n                      <TableCell key={uuidv4().toString()}>\r\n                        <Skeleton\r\n                          sx={{ width: '100%' }}\r\n                          variant=\"text\"\r\n                          animation=\"wave\"\r\n                        />\r\n                      </TableCell>\r\n                    ),\r\n                ),\r\n            )}\r\n          </TableRow>\r\n        ))}\r\n      </TableBody>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <TableBody>\r\n      {generateRowsInOrder(tableBody, headerField).map(\r\n        (data: unknown, rowIndex: number) => {\r\n          // Get the keys from the data object\r\n          const dataKeys = Object.keys(data as object);\r\n\r\n          return (\r\n            <TableRow\r\n              key={StringHelper.generateUID('tableRow', rowIndex)}\r\n              onClick={() => onRowClick(rowIndex)}\r\n            >\r\n              {headerField.map((header, headerIndex) => {\r\n                // Get the value for this header from the data\r\n                const value = (data as any)[header.id];\r\n\r\n                if (header.hidden) {\r\n                  return null;\r\n                }\r\n\r\n                if (\r\n                  Array.isArray(value) &&\r\n                  header.type === TableComponentEnum.ARRAY\r\n                ) {\r\n                  const subArray = header.subArray || [];\r\n\r\n                  return (\r\n                    generateSubArrayInOrder(value, subArray)\r\n                      .reverse()\r\n                      // any subheader will be any type\r\n                      .map((res: any, subIndex: number) => (\r\n                        <TableCell key={uuidv4().toString()}>\r\n                          <Box\r\n                            sx={\r\n                              subArray[subIndex]?.width\r\n                                ? {\r\n                                    width: `${subArray[subIndex].width}px`,\r\n                                    textOverflow: 'ellipsis',\r\n                                  }\r\n                                : {}\r\n                            }\r\n                          >\r\n                            {subArray[subIndex] && (\r\n                              <TableCellGenerator\r\n                                header={subArray[subIndex]}\r\n                                value={res[subArray[subIndex].id]}\r\n                                index={rowIndex}\r\n                              />\r\n                            )}\r\n                          </Box>\r\n                        </TableCell>\r\n                      ))\r\n                  );\r\n                }\r\n\r\n                return (\r\n                  <TableCell key={uuidv4().toString()}>\r\n                    <Box\r\n                      sx={\r\n                        header.width\r\n                          ? {\r\n                              width: `${header.width}px`,\r\n                              textOverflow: 'ellipsis',\r\n                            }\r\n                          : {}\r\n                      }\r\n                    >\r\n                      <TableCellGenerator\r\n                        header={header}\r\n                        value={value}\r\n                        index={rowIndex}\r\n                        onClick={componentProps && componentProps[header.id] ? componentProps[header.id].onClick : undefined}\r\n                        {...(componentProps && componentProps[header.id] ? componentProps[header.id] : {})}\r\n                      />\r\n                    </Box>\r\n                  </TableCell>\r\n                );\r\n              })}\r\n            </TableRow>\r\n          );\r\n        }\r\n      )}\r\n    </TableBody>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0EAA0E;AAC1E,qDAAqD;;;;AAErD;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAEA;AAAA;AAEA;AAAA;;;;;;;AAiBO,SAAS,gBACd,KAA+B;IAE/B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,GACrF;IAEF;;;;;GAKC,GACD,SAAS,oBACP,aAAwB,EACxB,MAAiB;QAEjB,uDAAuD;QACvD,MAAM,gBAAqB,EAAE;QAC7B,IAAI,oBAAyB,CAAC;QAE9B,IAAK,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,EAAE,SAAS,EAAG;YACrD,MAAM,aAAsB,MAAM,CAAC,MAAM;YACzC,oBAAoB;gBAAE,CAAC,WAAW,EAAE,CAAC,EAAE;gBAAM,GAAG,iBAAiB;YAAC;QACpE;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;YAChD,MAAM,oBAAoB;gBAAE,GAAG,iBAAiB;YAAC;YACjD,MAAM,kBAAkB,aAAa,CAAC,EAAE;YACxC,MAAM,oBAAoB,OAAO,MAAM,CACrC,mBACA;YAEF,cAAc,IAAI,CAAC;gBAAE,GAAG,iBAAiB;YAAC;QAC5C;QAEA,OAAO;IACT;IAEA;;;;;GAKC,GACD,SAAS,wBACP,aAAwB,EACxB,MAAiB;QAEjB,MAAM,gBAA2C,EAAE;QACnD,IAAI,cAAc,CAAC;QACnB,IAAK,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,EAAE,SAAS,EAAG;YACrD,MAAM,aAAsB,MAAM,CAAC,MAAM;YACzC,cAAc;gBAAE,CAAC,WAAW,EAAE,CAAC,EAAE;gBAAM,GAAG,WAAW;YAAC;QACxD;QACA,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAC;YAChC,gCAAgC;YAChC,MAAM,aAAkB,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,GAAG,KAAK;YACrE,IAAI,cAAc,QAAQ,WAAW,GAAG,EAAE;gBACxC,cAAc,IAAI,CAAC;oBACjB,CAAC,IAAI,EAAE,WAAW,KAAK;gBACzB;YACF,OAAO;gBACL,cAAc,IAAI,CAAC;oBACjB,CAAC,IAAI,EAAE;gBACT;YACF;QACF;QAEA,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC,sMAAA,CAAA,YAAS;sBACP,MAAM,IAAI,CAAC,MAAM,eAAe,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,kBAClD,6LAAC,mMAAA,CAAA,WAAQ;;wBACN,YAAY,GAAG,CACd,CAAC,OACC,CAAC,KAAK,MAAM,IACZ,KAAK,IAAI,KAAK,yKAAA,CAAA,qBAAkB,CAAC,KAAK,kBACpC,6LAAC,sMAAA,CAAA,YAAS;0CACR,cAAA,6LAAC,mMAAA,CAAA,WAAQ;oCACP,IAAI;wCAAE,OAAO,GAAG,mIAAA,CAAA,aAAU,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,CAAC;oCAAC;oCACrD,SAAQ;oCACR,WAAU;;;;;;+BAJE,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,IAAI,QAAQ;;;;;wBAStC,YAAY,GAAG,CACd,CAAC,OACC,KAAK,QAAQ,IACb,KAAK,QAAQ,CAAC,GAAG,CACf,CAAC,QACC,CAAC,MAAM,MAAM,kBACX,6LAAC,sMAAA,CAAA,YAAS;8CACR,cAAA,6LAAC,mMAAA,CAAA,WAAQ;wCACP,IAAI;4CAAE,OAAO;wCAAO;wCACpB,SAAQ;wCACR,WAAU;;;;;;mCAJE,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,IAAI,QAAQ;;;;;;mBApB9B,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,IAAI,QAAQ;;;;;WAFpB,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,IAAI,QAAQ;;;;;IAoCrC;IAEA,qBACE,6LAAC,sMAAA,CAAA,YAAS;kBACP,oBAAoB,WAAW,aAAa,GAAG,CAC9C,CAAC,MAAe;YACd,oCAAoC;YACpC,MAAM,WAAW,OAAO,IAAI,CAAC;YAE7B,qBACE,6LAAC,mMAAA,CAAA,WAAQ;gBAEP,SAAS,IAAM,WAAW;0BAEzB,YAAY,GAAG,CAAC,CAAC,QAAQ;oBACxB,8CAA8C;oBAC9C,MAAM,QAAQ,AAAC,IAAY,CAAC,OAAO,EAAE,CAAC;oBAEtC,IAAI,OAAO,MAAM,EAAE;wBACjB,OAAO;oBACT;oBAEA,IACE,MAAM,OAAO,CAAC,UACd,OAAO,IAAI,KAAK,yKAAA,CAAA,qBAAkB,CAAC,KAAK,EACxC;wBACA,MAAM,WAAW,OAAO,QAAQ,IAAI,EAAE;wBAEtC,OACE,wBAAwB,OAAO,UAC5B,OAAO,EACR,iCAAiC;yBAChC,GAAG,CAAC,CAAC,KAAU,yBACd,6LAAC,sMAAA,CAAA,YAAS;0CACR,cAAA,6LAAC,oLAAA,CAAA,MAAG;oCACF,IACE,QAAQ,CAAC,SAAS,EAAE,QAChB;wCACE,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;wCACtC,cAAc;oCAChB,IACA,CAAC;8CAGN,QAAQ,CAAC,SAAS,kBACjB,6LAAC,oLAAA,CAAA,qBAAkB;wCACjB,QAAQ,QAAQ,CAAC,SAAS;wCAC1B,OAAO,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;wCACjC,OAAO;;;;;;;;;;;+BAfC,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,IAAI,QAAQ;;;;;oBAsBzC;oBAEA,qBACE,6LAAC,sMAAA,CAAA,YAAS;kCACR,cAAA,6LAAC,oLAAA,CAAA,MAAG;4BACF,IACE,OAAO,KAAK,GACR;gCACE,OAAO,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC;gCAC1B,cAAc;4BAChB,IACA,CAAC;sCAGP,cAAA,6LAAC,oLAAA,CAAA,qBAAkB;gCACjB,QAAQ;gCACR,OAAO;gCACP,OAAO;gCACP,SAAS,kBAAkB,cAAc,CAAC,OAAO,EAAE,CAAC,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,GAAG;gCAC1F,GAAI,kBAAkB,cAAc,CAAC,OAAO,EAAE,CAAC,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;uBAhBvE,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD,IAAI,QAAQ;;;;;gBAqBrC;eApEK,qIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,YAAY;;;;;QAuEhD;;;;;;AAIR;KAnMgB", "debugId": null}}, {"offset": {"line": 1736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableHead/customTableHead.tsx"], "sourcesContent": ["import React, { ReactElement } from \"react\";\r\nimport { Box, TableHead, TableRow, Tooltip, TableCell } from \"@mui/material\";\r\n\r\nimport { IHeader } from \"../interface\";\r\nimport { StringHelper } from \"@/helpers\";\r\n\r\ninterface ICustomTableHeadProps {\r\n  headerField: IHeader[];\r\n}\r\n\r\n/**\r\n * Custom table head component\r\n * @param {ICustomTableHeadProps} props\r\n * @return {ReactElement}\r\n */\r\nexport function CustomTableHead(props: ICustomTableHeadProps): ReactElement {\r\n  const { headerField } = props;\r\n\r\n  return (\r\n    <TableHead>\r\n      <TableRow>\r\n        {headerField.map((field: IHeader, index: number) => {\r\n          if (field.hidden) {\r\n            return null;\r\n          }\r\n          if (field.subArray) {\r\n            return field.subArray.map((subField: IHeader) => (\r\n              <TableCell key={subField.id}>{subField.name}</TableCell>\r\n            ));\r\n          }\r\n\r\n          return (\r\n            <TableCell key={StringHelper.generateUID(\"tableCell\", index)}>\r\n              <Tooltip title={field.tooltip} arrow followCursor>\r\n                <Box sx={field.width ? { width: `${field.width}px` } : {}}>\r\n                  {field.headerComponent ? (\r\n                    <field.headerComponent {...field.headerComponentProps} />\r\n                  ) : (\r\n                    field.name\r\n                  )}\r\n                </Box>\r\n              </Tooltip>\r\n            </TableCell>\r\n          );\r\n        })}\r\n      </TableRow>\r\n    </TableHead>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AAAA;;;;AAWO,SAAS,gBAAgB,KAA4B;IAC1D,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,6LAAC,sMAAA,CAAA,YAAS;kBACR,cAAA,6LAAC,mMAAA,CAAA,WAAQ;sBACN,YAAY,GAAG,CAAC,CAAC,OAAgB;gBAChC,IAAI,MAAM,MAAM,EAAE;oBAChB,OAAO;gBACT;gBACA,IAAI,MAAM,QAAQ,EAAE;oBAClB,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,yBACzB,6LAAC,sMAAA,CAAA,YAAS;sCAAoB,SAAS,IAAI;2BAA3B,SAAS,EAAE;;;;;gBAE/B;gBAEA,qBACE,6LAAC,sMAAA,CAAA,YAAS;8BACR,cAAA,6LAAC,gMAAA,CAAA,UAAO;wBAAC,OAAO,MAAM,OAAO;wBAAE,KAAK;wBAAC,YAAY;kCAC/C,cAAA,6LAAC,oLAAA,CAAA,MAAG;4BAAC,IAAI,MAAM,KAAK,GAAG;gCAAE,OAAO,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC;4BAAC,IAAI,CAAC;sCACrD,MAAM,eAAe,iBACpB,6LAAC,MAAM,eAAe;gCAAE,GAAG,MAAM,oBAAoB;;;;;uCAErD,MAAM,IAAI;;;;;;;;;;;mBANF,qIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,aAAa;;;;;YAY1D;;;;;;;;;;;AAIR;KAjCgB", "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/constants/pagination.constant.ts"], "sourcesContent": ["export const ROWS_PER_PAGE_OPTIONS = [10, 25, 50];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,wBAAwB;IAAC;IAAI;IAAI;CAAG", "debugId": null}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/constants/index.ts"], "sourcesContent": ["export * from './pagination.constant';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tablePagination/customTablePagination.style.ts"], "sourcesContent": ["import { styled, TablePagination } from '@mui/material';\r\n\r\nexport const TablePaginationWrapper = styled(TablePagination)(({ theme }) => ({\r\n  maxWidth: '100%',\r\n  backgroundColor: theme.palette.background.paper,\r\n  width: '100vw',\r\n  overflow: 'none',\r\n  borderTop: `1px solid ${theme.palette.divider}`,\r\n  '.MuiToolbar-root': { overflow: 'auto' },\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEO,MAAM,yBAAyB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,wNAAA,CAAA,kBAAe,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC5E,UAAU;QACV,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,OAAO;QACP,UAAU;QACV,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QAC/C,oBAAoB;YAAE,UAAU;QAAO;IACzC,CAAC", "debugId": null}}, {"offset": {"line": 1885, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tablePagination/customTablePagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { JSX } from \"react\";\r\n\r\nimport { ROWS_PER_PAGE_OPTIONS } from \"../constants\";\r\nimport { ICustomTablePagination } from \"../interface\";\r\nimport { TablePaginationWrapper } from \"./customTablePagination.style\";\r\n\r\n/**\r\n * Custom table Pagination Component\r\n * @param {ICustomTablePagination} props\r\n * @return {JSX.Element}\r\n */\r\nexport function CustomTablePagination(\r\n  props: ICustomTablePagination\r\n): JSX.Element {\r\n  const { page, limit, total, onPageChange, onRowsPerPageChange } = props;\r\n\r\n  return (\r\n    <TablePaginationWrapper\r\n      count={total}\r\n      onPageChange={(_event, newPage: number) => onPageChange(newPage + 1)}\r\n      onRowsPerPageChange={(event) => {\r\n        onRowsPerPageChange(parseInt(event.target.value, 10));\r\n      }}\r\n      page={page - 1}\r\n      rowsPerPage={limit}\r\n      rowsPerPageOptions={ROWS_PER_PAGE_OPTIONS}\r\n      showFirstButton\r\n      showLastButton\r\n      labelRowsPerPage=\"Rows Per Page\"\r\n      labelDisplayedRows={({ from, to, count }) => `${from}-${to} of ${count}`}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAEA;AANA;;;;AAaO,SAAS,sBACd,KAA6B;IAE7B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,mBAAmB,EAAE,GAAG;IAElE,qBACE,6LAAC,4LAAA,CAAA,yBAAsB;QACrB,OAAO;QACP,cAAc,CAAC,QAAQ,UAAoB,aAAa,UAAU;QAClE,qBAAqB,CAAC;YACpB,oBAAoB,SAAS,MAAM,MAAM,CAAC,KAAK,EAAE;QACnD;QACA,MAAM,OAAO;QACb,aAAa;QACb,oBAAoB,8KAAA,CAAA,wBAAqB;QACzC,eAAe;QACf,cAAc;QACd,kBAAiB;QACjB,oBAAoB,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI,EAAE,OAAO;;;;;;AAG9E;KArBgB", "debugId": null}}, {"offset": {"line": 1929, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableComponent.style.ts"], "sourcesContent": ["import {\r\n  Box,\r\n  styled,\r\n  TableContainer as MuiTableContainer,\r\n} from '@mui/material';\r\n\r\nexport const TableWrapper = styled(Box)(({ theme }) => ({\r\n  overflow: 'auto',\r\n  thead: {\r\n    background: theme.palette.primary.main,\r\n    color: theme.palette.primary.contrastText,\r\n    th: {\r\n      color: 'inherit',\r\n      [theme.breakpoints.down('md')]: {\r\n        whiteSpace: 'nowrap',\r\n      },\r\n    },\r\n  },\r\n  tbody: {\r\n    td: {\r\n      [theme.breakpoints.down('md')]: {\r\n        whiteSpace: 'nowrap',\r\n      },\r\n      a: {\r\n        color: theme.palette.primary.dark,\r\n        textDecoration: 'none',\r\n        fontWeight: theme.typography.fontWeightMedium,\r\n      },\r\n    },\r\n  },\r\n}));\r\n\r\nexport const TableContainer = styled(MuiTableContainer, {\r\n  shouldForwardProp: (props) => props !== 'newHeight',\r\n})<{ newHeight: number }>(({ newHeight, theme }) => ({\r\n  overflow: 'auto',\r\n  width: '100%',\r\n  maxHeight: newHeight ? `calc(100vh - ${newHeight}px)` : 'calc(100vh - 245px)',\r\n  backgroundColor: theme.palette.background.paper,\r\n  \"& .MuiTableBody-root\": {\r\n    \"& .MuiTableRow-root\": {\r\n      \"&:nth-of-type(odd)\": {\r\n        backgroundColor:\r\n          theme.palette.mode === \"light\"\r\n            ? \"rgba(0, 0, 0, 0.02)\"\r\n            : \"rgba(255, 255, 255, 0.02)\",\r\n      },\r\n      \"&:hover\": {\r\n        backgroundColor:\r\n          theme.palette.mode === \"light\"\r\n            ? \"rgba(0, 0, 0, 0.04)\"\r\n            : \"rgba(255, 255, 255, 0.04)\",\r\n      },\r\n    },\r\n    \"& .MuiTableCell-body\": {\r\n      padding: \"12px 16px\",\r\n    },\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;AAMO,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACtD,UAAU;QACV,OAAO;YACL,YAAY,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACtC,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY;YACzC,IAAI;gBACF,OAAO;gBACP,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;oBAC9B,YAAY;gBACd;YACF;QACF;QACA,OAAO;YACL,IAAI;gBACF,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;oBAC9B,YAAY;gBACd;gBACA,GAAG;oBACD,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;oBACjC,gBAAgB;oBAChB,YAAY,MAAM,UAAU,CAAC,gBAAgB;gBAC/C;YACF;QACF;IACF,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,qNAAA,CAAA,iBAAiB,EAAE;IACtD,mBAAmB,CAAC,QAAU,UAAU;AAC1C,GAA0B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAK,CAAC;QACnD,UAAU;QACV,OAAO;QACP,WAAW,YAAY,CAAC,aAAa,EAAE,UAAU,GAAG,CAAC,GAAG;QACxD,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,wBAAwB;YACtB,uBAAuB;gBACrB,sBAAsB;oBACpB,iBACE,MAAM,OAAO,CAAC,IAAI,KAAK,UACnB,wBACA;gBACR;gBACA,WAAW;oBACT,iBACE,MAAM,OAAO,CAAC,IAAI,KAAK,UACnB,wBACA;gBACR;YACF;YACA,wBAAwB;gBACtB,SAAS;YACX;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1992, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableComponent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  JSX,\r\n  ReactNode,\r\n  useCallback,\r\n  useEffect,\r\n  useMemo,\r\n  useRef,\r\n} from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nimport {\r\n  NoDataFound,\r\n  TableHeightEnum,\r\n  CustomTableBody,\r\n  CustomTableHead,\r\n  CustomTablePagination,\r\n  IHeader,\r\n  ICustomTablePagination,\r\n} from \"@/components/common\";\r\nimport { TableContainer } from \"./tableComponent.style\";\r\n\r\nconst Table = dynamic(() => import(\"@mui/material/Table\"));\r\nconst TableRow = dynamic(() => import(\"@mui/material/TableRow\"));\r\nconst TableFooter = dynamic(() => import(\"@mui/material/TableFooter\"));\r\n\r\ninterface ITableProps<T> {\r\n  headerField: IHeader[];\r\n  tableBody: T[];\r\n  paginationData: ICustomTablePagination;\r\n  isLoading: boolean;\r\n  translation: {\r\n    noDataTitle: string;\r\n    noDataCaption?: string;\r\n  };\r\n  maxHeight?: number;\r\n  onRowClick?: (index: number) => void;\r\n  disablePagination?: boolean;\r\n  componentProps?: Record<string, any>;\r\n  visibleColumns?: string[];\r\n}\r\n\r\n/**\r\n * Table component\r\n * @param {ITableProps} tableProps\r\n * @return {JSX.Element}\r\n */\r\nexport function TableComponent<T>(tableProps: ITableProps<T>): JSX.Element {\r\n  const {\r\n    headerField,\r\n    translation,\r\n    tableBody,\r\n    isLoading,\r\n    maxHeight = 0,\r\n    visibleColumns,\r\n  } = tableProps;\r\n  const newHeight = maxHeight + TableHeightEnum.HEADERHEIGHT;\r\n  const tableContainerRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (tableContainerRef.current) {\r\n      tableContainerRef.current.scrollTop = 0;\r\n    }\r\n  }, [isLoading]);\r\n\r\n  /**\r\n   * No Data was Found\r\n   * @return {ReactNode}\r\n   */\r\n  const noData = useCallback((): ReactNode => {\r\n    if (tableBody.length <= 0 && !isLoading) {\r\n      return <NoDataFound title={translation.noDataTitle} />;\r\n    }\r\n    return null;\r\n  }, [isLoading, tableBody.length, translation.noDataTitle]);\r\n\r\n  // Filter headers based on visible columns if provided\r\n  const filteredHeaderField = useMemo(() => {\r\n    if (!visibleColumns || visibleColumns.length === 0) {\r\n      return headerField;\r\n    }\r\n    return headerField.filter(header => visibleColumns.includes(header.id));\r\n  }, [headerField, visibleColumns]);\r\n\r\n  const header = useMemo(\r\n    () => <CustomTableHead headerField={filteredHeaderField} />,\r\n    [filteredHeaderField]\r\n  );\r\n\r\n  const body = useMemo(\r\n    () => (\r\n      <CustomTableBody<T>\r\n        {...tableProps}\r\n        headerField={filteredHeaderField}\r\n        onRowClick={tableProps.onRowClick || (() => {})}\r\n      />\r\n    ),\r\n    [tableProps, filteredHeaderField]\r\n  );\r\n  const { paginationData } = tableProps;\r\n\r\n  return (\r\n    <>\r\n      <TableContainer newHeight={newHeight} ref={tableContainerRef}>\r\n        <Table stickyHeader aria-label=\"sticky table\">\r\n          {header}\r\n          {body}\r\n        </Table>\r\n        {noData()}\r\n      </TableContainer>\r\n      {!tableProps.disablePagination && filteredHeaderField.length > 0 && tableBody.length > 0 && (\r\n        <Table>\r\n          <TableFooter>\r\n            <TableRow>\r\n              <CustomTablePagination {...paginationData} />\r\n            </TableRow>\r\n          </TableFooter>\r\n        </Table>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\nexport default React.memo(TableComponent);\r\n"], "names": [], "mappings": ";;;;;AAEA;AAQA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;;;;;;AArBA;;;;;AAuBA,MAAM,QAAQ,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;KAAhB;AACN,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;MAAnB;AACN,MAAM,cAAc,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;MAAtB;AAuBC,SAAS,eAAkB,UAA0B;;IAC1D,MAAM,EACJ,WAAW,EACX,WAAW,EACX,SAAS,EACT,SAAS,EACT,YAAY,CAAC,EACb,cAAc,EACf,GAAG;IACJ,MAAM,YAAY,YAAY,yKAAA,CAAA,kBAAe,CAAC,YAAY;IAC1D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,kBAAkB,OAAO,CAAC,SAAS,GAAG;YACxC;QACF;mCAAG;QAAC;KAAU;IAEd;;;GAGC,GACD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YACzB,IAAI,UAAU,MAAM,IAAI,KAAK,CAAC,WAAW;gBACvC,qBAAO,6LAAC,sKAAA,CAAA,cAAW;oBAAC,OAAO,YAAY,WAAW;;;;;;YACpD;YACA,OAAO;QACT;6CAAG;QAAC;QAAW,UAAU,MAAM;QAAE,YAAY,WAAW;KAAC;IAEzD,sDAAsD;IACtD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE;YAClC,IAAI,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;gBAClD,OAAO;YACT;YACA,OAAO,YAAY,MAAM;+DAAC,CAAA,SAAU,eAAe,QAAQ,CAAC,OAAO,EAAE;;QACvE;sDAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CACnB,kBAAM,6LAAC,wKAAA,CAAA,kBAAe;gBAAC,aAAa;;;;;;yCACpC;QAAC;KAAoB;IAGvB,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCACjB,kBACE,6LAAC,wKAAA,CAAA,kBAAe;gBACb,GAAG,UAAU;gBACd,aAAa;gBACb,YAAY,WAAW,UAAU,IAAI;oDAAC,KAAO;iBAAC;;;;;;uCAGlD;QAAC;QAAY;KAAoB;IAEnC,MAAM,EAAE,cAAc,EAAE,GAAG;IAE3B,qBACE;;0BACE,6LAAC,kKAAA,CAAA,iBAAc;gBAAC,WAAW;gBAAW,KAAK;;kCACzC,6LAAC;wBAAM,YAAY;wBAAC,cAAW;;4BAC5B;4BACA;;;;;;;oBAEF;;;;;;;YAEF,CAAC,WAAW,iBAAiB,IAAI,oBAAoB,MAAM,GAAG,KAAK,UAAU,MAAM,GAAG,mBACrF,6LAAC;0BACC,cAAA,6LAAC;8BACC,cAAA,6LAAC;kCACC,cAAA,6LAAC,oLAAA,CAAA,wBAAqB;4BAAE,GAAG,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA1EgB;MAAA;2DA4ED,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/interface/table.interface.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { TableComponentEnum } from '../enum';\r\n\r\nexport interface IHeader {\r\n  id: string; // Must Match Data from Table\r\n  name: string;\r\n  hidden: boolean;\r\n  type: TableComponentEnum;\r\n  subArray?: IHeader[];\r\n  // Component will be any type of\r\n  component?: any;\r\n  headerComponent?: any;\r\n  headerComponentProps?: any;\r\n  tooltip?: string;\r\n  width?: number;\r\n}\r\n\r\nexport interface IMetaData {\r\n  isNextPage: boolean;\r\n  totalCount: number;\r\n}\r\n\r\nexport interface ICustomTablePagination {\r\n  page: number;\r\n  limit: number;\r\n  total: number;\r\n  onPageChange: (page: number) => void;\r\n  onRowsPerPageChange: (rows: number) => void;\r\n}\r\n"], "names": [], "mappings": "AAAA,qDAAqD", "debugId": null}}, {"offset": {"line": 2199, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/interface/index.ts"], "sourcesContent": ["export * from './table.interface';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2220, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableHead/index.ts"], "sourcesContent": ["export * from './customTableHead';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tablePagination/index.ts"], "sourcesContent": ["export * from './customTablePagination';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2262, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/tableBody/index.ts"], "sourcesContent": ["export * from './customTableBody';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2283, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/commonTableStyle.ts"], "sourcesContent": ["import { styled } from '@mui/material/styles';\nimport { Paper } from '@mui/material';\n\nexport const CommonTableStyle = styled(Paper)(({ theme }) => ({\n  borderRadius: theme.shape.borderRadius,\n  overflow: 'hidden',\n  boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.05)',\n\n  '& .MuiTableHead-root': {\n    backgroundColor: '#3A52A6',\n    '& .MuiTableCell-head': {\n      color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',\n      fontWeight: 600,\n      padding: '12px 16px',\n    }\n  },\n\n  '& .MuiTableBody-root': {\n    '& .MuiTableRow-root': {\n      '&:nth-of-type(odd)': {\n        backgroundColor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',\n      },\n      '&:hover': {\n        backgroundColor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.04)' : 'rgba(255, 255, 255, 0.04)',\n      },\n    },\n    '& .MuiTableCell-body': {\n      padding: '12px 16px',\n      fontSize: '0.875rem',\n    }\n  },\n\n  '& .MuiTablePagination-root': {\n    borderTop: `1px solid ${theme.palette.divider}`,\n  },\n\n  // Scrollbar styling\n  '& ::-webkit-scrollbar': {\n    height: '8px',\n    width: '8px',\n  },\n  '& ::-webkit-scrollbar-track': {\n    backgroundColor: 'transparent',\n  },\n  '& ::-webkit-scrollbar-thumb': {\n    backgroundColor: theme.palette.mode === 'light'\n      ? 'rgba(0, 0, 0, 0.2)'\n      : 'rgba(255, 255, 255, 0.2)',\n    borderRadius: '4px',\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,0LAAA,CAAA,QAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC5D,cAAc,MAAM,KAAK,CAAC,YAAY;QACtC,UAAU;QACV,WAAW;QAEX,wBAAwB;YACtB,iBAAiB;YACjB,wBAAwB;gBACtB,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,YAAY;gBACnD,YAAY;gBACZ,SAAS;YACX;QACF;QAEA,wBAAwB;YACtB,uBAAuB;gBACrB,sBAAsB;oBACpB,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,wBAAwB;gBAC5E;gBACA,WAAW;oBACT,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,wBAAwB;gBAC5E;YACF;YACA,wBAAwB;gBACtB,SAAS;gBACT,UAAU;YACZ;QACF;QAEA,8BAA8B;YAC5B,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QACjD;QAEA,oBAAoB;QACpB,yBAAyB;YACvB,QAAQ;YACR,OAAO;QACT;QACA,+BAA+B;YAC7B,iBAAiB;QACnB;QACA,+BAA+B;YAC7B,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UACpC,uBACA;YACJ,cAAc;QAChB;IACF,CAAC", "debugId": null}}, {"offset": {"line": 2341, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/table/index.ts"], "sourcesContent": ["import { TableComponent } from './tableComponent';\r\nimport { CommonTableStyle } from './commonTableStyle';\r\n\r\nexport * from './tableComponent';\r\nexport * from './enum';\r\nexport * from './interface';\r\nexport * from './tableHead';\r\nexport * from './tablePagination';\r\nexport * from './tableBody';\r\nexport * from './constants';\r\nexport * from './commonTableStyle';\r\n\r\nexport default TableComponent;\r\n"], "names": [], "mappings": ";;;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;uCAEe,0JAAA,CAAA,iBAAc", "debugId": null}}, {"offset": {"line": 2387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/avatarMenu/avatarMenu.style.ts"], "sourcesContent": ["import { Avatar, Box, ListItemAvatar, styled, Typography } from \"@mui/material\";\r\n\r\nexport const ActionWrapper = styled(Box)(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignContent: \"center\",\r\n  alignItems: \"center\",\r\n  svg: {\r\n    width: theme.spacing(1.625),\r\n    height: \"13px\",\r\n  },\r\n}));\r\n\r\nexport const NamePointer = styled(Box)({\r\n  cursor: \"pointer\",\r\n});\r\nexport const ListAvatar = styled(ListItemAvatar)(({ theme }) => ({\r\n  minWidth: theme.spacing(5),\r\n}));\r\n\r\nexport const UserPrefix = styled(Avatar)(() => ({\r\n  width: \"24px\",\r\n  height: \"24px\",\r\n  fontSize: \"0.75rem\", // Smaller font size for the avatar letter\r\n  fontWeight: 500,\r\n}));\r\n\r\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AAAA;AAAA;;AAEO,MAAM,gBAAgB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,SAAS;QACT,cAAc;QACd,YAAY;QACZ,KAAK;YACH,OAAO,MAAM,OAAO,CAAC;YACrB,QAAQ;QACV;IACF,CAAC;AAEM,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE;IACrC,QAAQ;AACV;AACO,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,qNAAA,CAAA,iBAAc,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/D,UAAU,MAAM,OAAO,CAAC;IAC1B,CAAC;AAEM,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,SAAM,EAAE,IAAM,CAAC;QAC9C,OAAO;QACP,QAAQ;QACR,UAAU;QACV,YAAY;IACd,CAAC", "debugId": null}}, {"offset": {"line": 2428, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/avatarMenu/avatarMenu.tsx"], "sourcesContent": ["import React, { JSX } from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\nimport { ActionWrapper, ListAvatar, UserPrefix } from \"./avatarMenu.style\";\r\n\r\nconst Typography = dynamic(() => import(\"@mui/material/Typography\"));\r\n\r\ninterface IProps\r\n  extends Readonly<{\r\n    value: string;\r\n    index: number;\r\n  }> {}\r\n\r\n/**\r\n * @component {AvatarMenu} - Contact Table Name Action\r\n * @param {IProps} props\r\n * @return {JSX.Element}\r\n */\r\nexport const AvatarMenu = (props: IProps): JSX.Element => {\r\n  const { value, index } = props;\r\n\r\n  return (\r\n    <ActionWrapper key={index}>\r\n      <ListAvatar>\r\n        <UserPrefix>\r\n          {value ? value.trimStart().substring(0, 1).toUpperCase() : \"U\"}\r\n        </UserPrefix>\r\n      </ListAvatar>\r\n      <Typography variant=\"body2\">{value}</Typography>\r\n    </ActionWrapper>\r\n  );\r\n};\r\n\r\nexport default React.memo(AvatarMenu);\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;;AAEA,MAAM,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;;KAArB;AAaC,MAAM,aAAa,CAAC;IACzB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG;IAEzB,qBACE,6LAAC,mKAAA,CAAA,gBAAa;;0BACZ,6LAAC,mKAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mKAAA,CAAA,aAAU;8BACR,QAAQ,MAAM,SAAS,GAAG,SAAS,CAAC,GAAG,GAAG,WAAW,KAAK;;;;;;;;;;;0BAG/D,6LAAC;gBAAW,SAAQ;0BAAS;;;;;;;OANX;;;;;AASxB;MAba;2DAeE,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC", "debugId": null}}, {"offset": {"line": 2496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/avatarMenu/index.ts"], "sourcesContent": ["import AvatarMenu from './avatarMenu';\r\n\r\nexport * from './avatarMenu';\r\n\r\nexport default AvatarMenu;\r\n"], "names": [], "mappings": ";;;AAAA;;;uCAIe,2JAAA,CAAA,UAAU", "debugId": null}}, {"offset": {"line": 2521, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/severitySlider/severitySlider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Box, Slider, Typography, styled } from \"@mui/material\";\r\n\r\ninterface SeveritySliderProps {\r\n  value: number;\r\n  onChange: (value: number) => void;\r\n  label: string;\r\n  description?: string;\r\n}\r\n\r\nconst SliderContainer = styled(Box)(({ theme }) => ({\r\n  width: \"100%\",\r\n  marginBottom: theme.spacing(2),\r\n}));\r\n\r\nconst StyledSlider = styled(Slider)(({ theme }) => ({\r\n  height: 8,\r\n  \"& .MuiSlider-rail\": {\r\n    background: `linear-gradient(90deg, ${theme.palette.success.light} 0%, ${theme.palette.warning.light} 50%, ${theme.palette.error.light} 100%)`,\r\n    opacity: 1,\r\n  },\r\n  \"& .MuiSlider-track\": {\r\n    border: \"none\",\r\n    background: \"transparent\",\r\n  },\r\n  \"& .MuiSlider-thumb\": {\r\n    height: 20,\r\n    width: 20,\r\n    backgroundColor: \"#fff\",\r\n    border: `2px solid ${theme.palette.primary.main}`,\r\n  },\r\n  \"& .MuiSlider-mark\": {\r\n    backgroundColor: theme.palette.background.paper,\r\n    height: 8,\r\n    width: 1,\r\n    \"&.MuiSlider-markActive\": {\r\n      opacity: 1,\r\n      backgroundColor: theme.palette.background.paper,\r\n    },\r\n  },\r\n  \"& .MuiSlider-markLabel\": {\r\n    fontSize: \"0.75rem\",\r\n    marginTop: 5,\r\n    color: theme.palette.text.secondary,\r\n  },\r\n}));\r\n\r\nconst SliderLabelContainer = styled(Box)(({ theme }) => ({\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n  marginBottom: theme.spacing(1),\r\n}));\r\n\r\nconst SliderValueContainer = styled(Box)(({ theme }) => ({\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n  marginTop: theme.spacing(1),\r\n}));\r\n\r\nconst marks = [\r\n  {\r\n    value: 0,\r\n    label: \"0\",\r\n  },\r\n  {\r\n    value: 2,\r\n    label: \"2\",\r\n  },\r\n  {\r\n    value: 4,\r\n    label: \"4\",\r\n  },\r\n  {\r\n    value: 6,\r\n    label: \"6\",\r\n  },\r\n  {\r\n    value: 8,\r\n    label: \"8\",\r\n  },\r\n  {\r\n    value: 10,\r\n    label: \"10\",\r\n  },\r\n];\r\n\r\nexport const SeveritySlider: React.FC<SeveritySliderProps> = ({\r\n  value,\r\n  onChange,\r\n  label,\r\n  description,\r\n}) => {\r\n  const handleChange = (_event: Event, newValue: number | number[]) => {\r\n    onChange(newValue as number);\r\n  };\r\n\r\n  return (\r\n    <SliderContainer>\r\n      <SliderLabelContainer>\r\n        <Typography variant=\"subtitle2\">{label}</Typography>\r\n        <Typography variant=\"body2\">\r\n          Value: {value}\r\n        </Typography>\r\n      </SliderLabelContainer>\r\n      {description && (\r\n        <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 2 }}>\r\n          {description}\r\n        </Typography>\r\n      )}\r\n      <StyledSlider\r\n        value={value}\r\n        onChange={handleChange}\r\n        step={1}\r\n        min={0}\r\n        max={10}\r\n        marks={marks}\r\n        valueLabelDisplay=\"auto\"\r\n      />\r\n      <SliderValueContainer>\r\n        <Typography variant=\"caption\" color=\"textSecondary\">\r\n          Low\r\n        </Typography>\r\n        <Typography variant=\"caption\" color=\"textSecondary\">\r\n          High\r\n        </Typography>\r\n      </SliderValueContainer>\r\n    </SliderContainer>\r\n  );\r\n};\r\n\r\nexport default SeveritySlider;\r\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAYA,MAAM,kBAAkB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAClD,OAAO;QACP,cAAc,MAAM,OAAO,CAAC;IAC9B,CAAC;KAHK;AAKN,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,SAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAClD,QAAQ;QACR,qBAAqB;YACnB,YAAY,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9I,SAAS;QACX;QACA,sBAAsB;YACpB,QAAQ;YACR,YAAY;QACd;QACA,sBAAsB;YACpB,QAAQ;YACR,OAAO;YACP,iBAAiB;YACjB,QAAQ,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;QACnD;QACA,qBAAqB;YACnB,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;YAC/C,QAAQ;YACR,OAAO;YACP,0BAA0B;gBACxB,SAAS;gBACT,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;YACjD;QACF;QACA,0BAA0B;YACxB,UAAU;YACV,WAAW;YACX,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QACrC;IACF,CAAC;MA9BK;AAgCN,MAAM,uBAAuB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,SAAS;QACT,gBAAgB;QAChB,cAAc,MAAM,OAAO,CAAC;IAC9B,CAAC;MAJK;AAMN,MAAM,uBAAuB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,SAAS;QACT,gBAAgB;QAChB,WAAW,MAAM,OAAO,CAAC;IAC3B,CAAC;MAJK;AAMN,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAEM,MAAM,iBAAgD,CAAC,EAC5D,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EACZ;IACC,MAAM,eAAe,CAAC,QAAe;QACnC,SAAS;IACX;IAEA,qBACE,6LAAC;;0BACC,6LAAC;;kCACC,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;kCAAa;;;;;;kCACjC,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;;4BAAQ;4BAClB;;;;;;;;;;;;;YAGX,6BACC,6LAAC,yMAAA,CAAA,aAAU;gBAAC,SAAQ;gBAAQ,OAAM;gBAAgB,IAAI;oBAAE,IAAI;gBAAE;0BAC3D;;;;;;0BAGL,6LAAC;gBACC,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,mBAAkB;;;;;;0BAEpB,6LAAC;;kCACC,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAU,OAAM;kCAAgB;;;;;;kCAGpD,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAU,OAAM;kCAAgB;;;;;;;;;;;;;;;;;;AAM5D;MA1Ca;uCA4CE", "debugId": null}}, {"offset": {"line": 2716, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/severitySlider/index.ts"], "sourcesContent": ["export * from './severitySlider';\r\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2737, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/charts/context/chart-context.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, {\r\n  createContext,\r\n  FC,\r\n  useCallback,\r\n  useContext,\r\n  useMemo,\r\n} from 'react';\r\n\r\nimport { IChartContext, IChartProvider, IApexChartExport } from '../interfaces';\r\n\r\nexport const ChartContext = createContext<IChartContext>({} as IChartContext);\r\n\r\n/**\r\n * Chart Provider\r\n * @param {IChartProvider}param\r\n * @return {ReactElement}\r\n */\r\nexport const ChartProvider: FC<IChartProvider> = ({ id, children }) => {\r\n  const downloadSVG = useCallback(() => {\r\n    // Dynamic import will only run on client side\r\n    import('apexcharts').then((ApexChartsModule) => {\r\n      const ApexCharts = ApexChartsModule.default;\r\n      const { ctx } = ApexCharts.getChartByID(id)\r\n        ?.exports as unknown as IApexChartExport;\r\n      ctx.exports.exportToSVG();\r\n    });\r\n  }, [id]);\r\n\r\n  const downloadPNG = useCallback(() => {\r\n    // Dynamic import will only run on client side\r\n    import('apexcharts').then((ApexChartsModule) => {\r\n      const ApexCharts = ApexChartsModule.default;\r\n      const { ctx } = ApexCharts.getChartByID(id)\r\n        ?.exports as unknown as IApexChartExport;\r\n      ctx.exports.exportToPng();\r\n    });\r\n  }, [id]);\r\n\r\n  const downloadCSV = useCallback(() => {\r\n    // Dynamic import will only run on client side\r\n    import('apexcharts').then((ApexChartsModule) => {\r\n      const ApexCharts = ApexChartsModule.default;\r\n      const { ctx } = ApexCharts.getChartByID(id)\r\n        ?.exports as unknown as IApexChartExport;\r\n      ctx.exportToCSV();\r\n    });\r\n  }, [id]);\r\n\r\n  const value = useMemo(\r\n    () => ({\r\n      id,\r\n      export: {\r\n        svg: downloadSVG,\r\n        png: downloadPNG,\r\n        csv: downloadCSV,\r\n      },\r\n    }),\r\n    [downloadCSV, downloadPNG, downloadSVG, id],\r\n  );\r\n\r\n  return (\r\n    <ChartContext.Provider value={value}>{children}</ChartContext.Provider>\r\n  );\r\n};\r\n\r\nexport default ChartProvider;\r\n\r\nexport const useChart = () => useContext(ChartContext);\r\n"], "names": [], "mappings": ";;;;;;;AAEA;;;AAFA;;AAYO,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiB,CAAC;AAOnD,MAAM,gBAAoC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE;;IAChE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,8CAA8C;YAC9C,qJAAqB,IAAI;0DAAC,CAAC;oBACzB,MAAM,aAAa,iBAAiB,OAAO;oBAC3C,MAAM,EAAE,GAAG,EAAE,GAAG,WAAW,YAAY,CAAC,KACpC;oBACJ,IAAI,OAAO,CAAC,WAAW;gBACzB;;QACF;iDAAG;QAAC;KAAG;IAEP,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,8CAA8C;YAC9C,qJAAqB,IAAI;0DAAC,CAAC;oBACzB,MAAM,aAAa,iBAAiB,OAAO;oBAC3C,MAAM,EAAE,GAAG,EAAE,GAAG,WAAW,YAAY,CAAC,KACpC;oBACJ,IAAI,OAAO,CAAC,WAAW;gBACzB;;QACF;iDAAG;QAAC;KAAG;IAEP,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC9B,8CAA8C;YAC9C,qJAAqB,IAAI;0DAAC,CAAC;oBACzB,MAAM,aAAa,iBAAiB,OAAO;oBAC3C,MAAM,EAAE,GAAG,EAAE,GAAG,WAAW,YAAY,CAAC,KACpC;oBACJ,IAAI,WAAW;gBACjB;;QACF;iDAAG;QAAC;KAAG;IAEP,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wCAClB,IAAM,CAAC;gBACL;gBACA,QAAQ;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;gBACP;YACF,CAAC;uCACD;QAAC;QAAa;QAAa;QAAa;KAAG;IAG7C,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAE1C;GA9Ca;KAAA;uCAgDE;AAER,MAAM,WAAW;;IAAM,OAAA,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;AAAY;IAAxC", "debugId": null}}, {"offset": {"line": 2837, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/charts/chart/chart.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport React from \"react\";\r\nimport _ from \"lodash\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { useChart } from \"../context/chart-context\";\r\nimport { Props } from \"react-apexcharts\";\r\n\r\n// Import ReactApexChart dynamically with ssr: false to prevent server-side rendering\r\nconst ReactApexChart = dynamic(\r\n  () => import('react-apexcharts'),\r\n  { ssr: false }\r\n);\r\n\r\nexport const Chart = ({ options, ...rest }: Props) => {\r\n  const { id } = useChart();\r\n  const newOptions = _.update(options || {}, \"chart.id\", () => id);\r\n\r\n  return <ReactApexChart {...rest} options={newOptions} />;\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;;AALA;;;;AAQA,qFAAqF;AACrF,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAC3B;;;;;;IACE,KAAK;;KAFH;AAKC,MAAM,QAAQ,CAAC,EAAE,OAAO,EAAE,GAAG,MAAa;;IAC/C,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,wKAAA,CAAA,WAAQ,AAAD;IACtB,MAAM,aAAa,mIAAA,CAAA,UAAC,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,YAAY,IAAM;IAE7D,qBAAO,6LAAC;QAAgB,GAAG,IAAI;QAAE,SAAS;;;;;;AAC5C;GALa;;QACI,wKAAA,CAAA,WAAQ;;;MADZ", "debugId": null}}, {"offset": {"line": 2892, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/charts/chartContainer/chartContainer.style.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Card, styled, Typography } from '@mui/material';\r\n\r\nexport const ChartCard = styled(Card, {\r\n  shouldForwardProp: (props) => props !== 'height',\r\n})<{ height?: string | number }>(({ theme, height }) => ({\r\n  height,\r\n  [theme.breakpoints.down('sm')]: {\r\n    minHeight: 'auto',\r\n  },\r\n}));\r\n\r\nexport const ChartTitle = styled(Typography)(({ theme }) => ({\r\n  fontSize: \"1.125rem\",\r\n  fontWeight: 600,\r\n  color: theme.palette.text.primary,\r\n}));\r\n\r\nexport const ChartDescription = styled(Typography)(({ theme }) => ({\r\n  fontSize: \"0.875rem\",\r\n  color: theme.palette.text.secondary,\r\n}));\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAFA;;AAIO,MAAM,YAAY,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,uLAAA,CAAA,OAAI,EAAE;IACpC,mBAAmB,CAAC,QAAU,UAAU;AAC1C,GAAiC,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;QACvD;QACA,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,WAAW;QACb;IACF,CAAC;AAEM,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,yMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC3D,UAAU;QACV,YAAY;QACZ,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;IACnC,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,yMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACjE,UAAU;QACV,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;IACrC,CAAC", "debugId": null}}, {"offset": {"line": 2928, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/charts/chartContainer/chartContainer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { JS<PERSON>, memo, MouseEvent, ReactNode, useState } from \"react\";\r\nimport { <PERSON><PERSON><PERSON><PERSON>, Divider, <PERSON>u, MenuItem } from \"@mui/material\";\r\nimport { faEllipsisVertical } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\nimport { Icon } from \"@/components/common/icon/icon\";\r\n\r\nimport { ChartCard, ChartTitle, ChartDescription } from \"./chartContainer.style\";\r\nimport { useChart } from \"../context/chart-context\";\r\n\r\nexport interface IChartsContainerProps {\r\n  title: string;\r\n  children: ReactNode;\r\n  description?: string;\r\n  enableDataDownload?: boolean;\r\n  height?: string | number;\r\n  onDataDownload?: () => void;\r\n  disableAction?: boolean;\r\n}\r\n\r\n/**\r\n * Common Chart Container\r\n * @param {IChartsContainerProps} props\r\n * @return {JSX.Element}\r\n */\r\nexport function ChartContainer(props: IChartsContainerProps): JSX.Element {\r\n  const {\r\n    title,\r\n    children,\r\n    description,\r\n    enableDataDownload,\r\n    height,\r\n    onDataDownload,\r\n    disableAction,\r\n  } = props;\r\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n  const isOpen = Boolean(anchorEl);\r\n  const value = useChart();\r\n\r\n  /**\r\n   * Returns a handler function for click events\r\n   * @return {(event: MouseEvent<HTMLElement>) => void} Event handler function\r\n   */\r\n  const handleClick = () => (event: MouseEvent<HTMLElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  /**\r\n   * handle close\r\n   */\r\n  const handleClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  return (\r\n    <ChartCard\r\n      height={height}\r\n      sx={{\r\n        overflow: \"visible\",\r\n      }}\r\n    >\r\n\r\n\r\n      <CardHeader\r\n        title={<ChartTitle>{title}</ChartTitle>}\r\n        subheader={description && <ChartDescription>{description}</ChartDescription>}\r\n        action={\r\n          <>\r\n            {/* {disableAction && (\r\n              <Icon\r\n                icon={faEllipsisVertical}\r\n                onClick={handleClick}\r\n                label=\"More Options\"\r\n              />\r\n            )}\r\n            <Menu\r\n              id=\"long-menu\"\r\n              anchorEl={anchorEl}\r\n              open={isOpen}\r\n              onClose={handleClose}\r\n            >\r\n              <MenuItem\r\n                onClick={() => {\r\n                  value.export.csv();\r\n                  handleClose();\r\n                }}\r\n              >\r\n                Download CSV\r\n              </MenuItem>\r\n              <MenuItem\r\n                onClick={() => {\r\n                  value.export.png();\r\n                  handleClose();\r\n                }}\r\n              >\r\n                Download PNG\r\n              </MenuItem>\r\n              <MenuItem\r\n                onClick={() => {\r\n                  value.export.svg();\r\n                  handleClose();\r\n                }}\r\n              >\r\n                Download SVG\r\n              </MenuItem>\r\n              {enableDataDownload && (\r\n                <MenuItem\r\n                  onClick={() => {\r\n                    if (onDataDownload) {\r\n                      onDataDownload();\r\n                      handleClose();\r\n                    }\r\n                  }}\r\n                >\r\n                  \"Download Data\",\r\n                </MenuItem>\r\n              )}\r\n            </Menu> */}\r\n          </>\r\n        }\r\n      />\r\n      <Divider />\r\n      {children}\r\n    </ChartCard>\r\n  );\r\n}\r\nexport default memo(ChartContainer);\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAKA;AACA;;;AATA;;;;;AA0BO,SAAS,eAAe,KAA4B;;IACzD,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,EACX,kBAAkB,EAClB,MAAM,EACN,cAAc,EACd,aAAa,EACd,GAAG;IACJ,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,SAAS,QAAQ;IACvB,MAAM,QAAQ,CAAA,GAAA,wKAAA,CAAA,WAAQ,AAAD;IAErB;;;GAGC,GACD,MAAM,cAAc,IAAM,CAAC;YACzB,YAAY,MAAM,aAAa;QACjC;IAEA;;GAEC,GACD,MAAM,cAAc;QAClB,YAAY;IACd;IAEA,qBACE,6LAAC,qLAAA,CAAA,YAAS;QACR,QAAQ;QACR,IAAI;YACF,UAAU;QACZ;;0BAIA,6LAAC,yMAAA,CAAA,aAAU;gBACT,qBAAO,6LAAC,qLAAA,CAAA,aAAU;8BAAE;;;;;;gBACpB,WAAW,6BAAe,6LAAC,qLAAA,CAAA,mBAAgB;8BAAE;;;;;;gBAC7C,sBACE;;;;;;0BAsDJ,6LAAC,gMAAA,CAAA,UAAO;;;;;YACP;;;;;;;AAGP;GApGgB;;QAYA,wKAAA,CAAA,WAAQ;;;KAZR;2DAqGD,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3021, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/charts/chartContainer/index.ts"], "sourcesContent": ["import ChartContainer from \"./chartContainer\";\r\n\r\nexport * from \"./chartContainer\";\r\n\r\nexport default ChartContainer;\r\n"], "names": [], "mappings": ";;;AAAA;;;uCAIe,6KAAA,CAAA,UAAc", "debugId": null}}, {"offset": {"line": 3046, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/charts/index.ts"], "sourcesContent": ["export * from './chart/chart';\r\nexport * from './chartContainer';\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 3070, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/pagination/CustomPagination.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n} from '@mui/material';\n\ninterface CustomPaginationProps {\n  page: number;\n  limit: number;\n  total: number;\n  totalPages?: number;\n  onPageChange: (page: number) => void;\n}\n\nexport const CustomPagination: React.FC<CustomPaginationProps> = ({\n  page,\n  limit,\n  total,\n  totalPages: propsTotalPages,\n  onPageChange,\n}) => {\n  const [inputPage, setInputPage] = useState((page + 1).toString());\n  \n  // Calculate total pages if not provided\n  const totalPages = propsTotalPages || Math.ceil(total / limit);\n  \n  // Calculate start and end indices\n  const startIndex = page * limit + 1;\n  const endIndex = Math.min((page + 1) * limit, total);\n\n  // Update input page when page prop changes\n  useEffect(() => {\n    setInputPage((page + 1).toString());\n  }, [page]);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setInputPage(e.target.value);\n  };\n\n  const handleInputBlur = () => {\n    const pageNumber = parseInt(inputPage, 10) - 1;\n    if (!isNaN(pageNumber) && pageNumber >= 0 && pageNumber < totalPages) {\n      onPageChange(pageNumber);\n    } else {\n      setInputPage((page + 1).toString());\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      handleInputBlur();\n    }\n  };\n\n  return (\n    <Box sx={{ \n      p: 1, \n      display: 'flex', \n      justifyContent: 'space-between', \n      borderTop: '1px solid #e0e0e0',\n      backgroundColor: 'background.paper',\n    }}>\n      <Typography variant=\"body2\" color=\"text.secondary\" sx={{ display: 'flex', alignItems: 'center' }}>\n        Showing {total > 0 ? startIndex : 0}-{endIndex} of {total} | Total Pages - {totalPages}\n      </Typography>\n      \n      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n        <Box\n          component=\"button\"\n          sx={{\n            width: 28,\n            height: 28,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '1px solid #e0e0e0',\n            borderRadius: '4px',\n            backgroundColor: 'transparent',\n            cursor: 'pointer',\n            '&:hover': {\n              backgroundColor: 'rgba(0, 0, 0, 0.04)',\n            },\n            '&:disabled': {\n              opacity: 0.5,\n              cursor: 'not-allowed',\n            }\n          }}\n          disabled={page === 0}\n          onClick={() => onPageChange(page - 1)}\n        >\n          &lt;\n        </Box>\n        \n        <Box\n          component=\"input\"\n          type=\"text\"\n          value={inputPage}\n          onChange={handleInputChange}\n          onBlur={handleInputBlur}\n          onKeyDown={handleKeyDown}\n          sx={{\n            width: 40,\n            height: 28,\n            textAlign: 'center',\n            border: '1px solid #e0e0e0',\n            borderRadius: '4px',\n            '&:focus': {\n              outline: 'none',\n              borderColor: '#3A52A6',\n            }\n          }}\n        />\n        \n        <Box\n          component=\"button\"\n          sx={{\n            width: 28,\n            height: 28,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '1px solid #e0e0e0',\n            borderRadius: '4px',\n            backgroundColor: 'transparent',\n            cursor: 'pointer',\n            '&:hover': {\n              backgroundColor: 'rgba(0, 0, 0, 0.04)',\n            },\n            '&:disabled': {\n              opacity: 0.5,\n              cursor: 'not-allowed',\n            }\n          }}\n          disabled={page >= totalPages - 1}\n          onClick={() => onPageChange(page + 1)}\n        >\n          &gt;\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n\nexport default CustomPagination;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;AAHA;;;AAiBO,MAAM,mBAAoD,CAAC,EAChE,IAAI,EACJ,KAAK,EACL,KAAK,EACL,YAAY,eAAe,EAC3B,YAAY,EACb;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,OAAO,CAAC,EAAE,QAAQ;IAE9D,wCAAwC;IACxC,MAAM,aAAa,mBAAmB,KAAK,IAAI,CAAC,QAAQ;IAExD,kCAAkC;IAClC,MAAM,aAAa,OAAO,QAAQ;IAClC,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,OAAO;IAE9C,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,aAAa,CAAC,OAAO,CAAC,EAAE,QAAQ;QAClC;qCAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,CAAC;QACzB,aAAa,EAAE,MAAM,CAAC,KAAK;IAC7B;IAEA,MAAM,kBAAkB;QACtB,MAAM,aAAa,SAAS,WAAW,MAAM;QAC7C,IAAI,CAAC,MAAM,eAAe,cAAc,KAAK,aAAa,YAAY;YACpE,aAAa;QACf,OAAO;YACL,aAAa,CAAC,OAAO,CAAC,EAAE,QAAQ;QAClC;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,qBACE,6LAAC,oLAAA,CAAA,MAAG;QAAC,IAAI;YACP,GAAG;YACH,SAAS;YACT,gBAAgB;YAChB,WAAW;YACX,iBAAiB;QACnB;;0BACE,6LAAC,yMAAA,CAAA,aAAU;gBAAC,SAAQ;gBAAQ,OAAM;gBAAiB,IAAI;oBAAE,SAAS;oBAAQ,YAAY;gBAAS;;oBAAG;oBACvF,QAAQ,IAAI,aAAa;oBAAE;oBAAE;oBAAS;oBAAK;oBAAM;oBAAkB;;;;;;;0BAG9E,6LAAC,oLAAA,CAAA,MAAG;gBAAC,IAAI;oBAAE,SAAS;oBAAQ,YAAY;oBAAU,KAAK;gBAAE;;kCACvD,6LAAC,oLAAA,CAAA,MAAG;wBACF,WAAU;wBACV,IAAI;4BACF,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,QAAQ;4BACR,cAAc;4BACd,iBAAiB;4BACjB,QAAQ;4BACR,WAAW;gCACT,iBAAiB;4BACnB;4BACA,cAAc;gCACZ,SAAS;gCACT,QAAQ;4BACV;wBACF;wBACA,UAAU,SAAS;wBACnB,SAAS,IAAM,aAAa,OAAO;kCACpC;;;;;;kCAID,6LAAC,oLAAA,CAAA,MAAG;wBACF,WAAU;wBACV,MAAK;wBACL,OAAO;wBACP,UAAU;wBACV,QAAQ;wBACR,WAAW;wBACX,IAAI;4BACF,OAAO;4BACP,QAAQ;4BACR,WAAW;4BACX,QAAQ;4BACR,cAAc;4BACd,WAAW;gCACT,SAAS;gCACT,aAAa;4BACf;wBACF;;;;;;kCAGF,6LAAC,oLAAA,CAAA,MAAG;wBACF,WAAU;wBACV,IAAI;4BACF,OAAO;4BACP,QAAQ;4BACR,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,QAAQ;4BACR,cAAc;4BACd,iBAAiB;4BACjB,QAAQ;4BACR,WAAW;gCACT,iBAAiB;4BACnB;4BACA,cAAc;gCACZ,SAAS;gCACT,QAAQ;4BACV;wBACF;wBACA,UAAU,QAAQ,aAAa;wBAC/B,SAAS,IAAM,aAAa,OAAO;kCACpC;;;;;;;;;;;;;;;;;;AAMT;GA/Ha;KAAA;uCAiIE", "debugId": null}}, {"offset": {"line": 3259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/pagination/index.ts"], "sourcesContent": ["export * from './CustomPagination';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/searchBar/searchBar.style.ts"], "sourcesContent": ["import { styled } from \"@mui/material/styles\";\r\nimport { Paper, InputBase, IconButton, Box } from \"@mui/material\";\r\n\r\nexport const SearchContainer = styled(\"div\")({\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    marginLeft: \"auto\",\r\n});\r\n\r\nexport const SearchPaper = styled(Paper)({\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    width: \"300px\",\r\n    padding: \"2px 4px\",\r\n    borderRadius: \"4px\",\r\n    boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\r\n});\r\n\r\nexport const SearchInputBase = styled(InputBase)({\r\n    flex: 1,\r\n    marginLeft: \"8px\",\r\n    fontSize: \"0.875rem\",\r\n});\r\n\r\nexport const SearchIconButton = styled(IconButton)(({ theme }) => ({\r\n    padding: \"8px\",\r\n    color: theme.palette.text.secondary,\r\n    \"&:hover\": {\r\n        color: theme.palette.primary.main,\r\n        backgroundColor: theme.palette.action.hover,\r\n    },\r\n}));\r\n\r\nexport const FilterIconButton = styled(IconButton)(({ theme }) => ({\r\n    padding: \"8px\",\r\n    color: theme.palette.text.secondary,\r\n    \"&:hover\": {\r\n        color: theme.palette.primary.main,\r\n        backgroundColor: theme.palette.action.hover,\r\n    },\r\n}));\r\n\r\nexport const FilterPopoverBox = styled(Box)({\r\n    padding: \"18px\",\r\n    minWidth: \"320px\",\r\n    maxHeight: \"350px\",\r\n    overflowY: \"auto\",\r\n});"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AAAA;AAAA;AAAA;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACzC,SAAS;IACT,YAAY;IACZ,YAAY;AAChB;AAEO,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,0LAAA,CAAA,QAAK,EAAE;IACrC,SAAS;IACT,YAAY;IACZ,OAAO;IACP,SAAS;IACT,cAAc;IACd,WAAW;AACf;AAEO,MAAM,kBAAkB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,sMAAA,CAAA,YAAS,EAAE;IAC7C,MAAM;IACN,YAAY;IACZ,UAAU;AACd;AAEO,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,yMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/D,SAAS;QACT,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QACnC,WAAW;YACP,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACjC,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;QAC/C;IACJ,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,yMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/D,SAAS;QACT,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QACnC,WAAW;YACP,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACjC,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;QAC/C;IACJ,CAAC;AAEM,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE;IACxC,SAAS;IACT,UAAU;IACV,WAAW;IACX,WAAW;AACf", "debugId": null}}, {"offset": {"line": 3344, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/searchBar/searchBar.tsx"], "sourcesContent": ["import React, { useState, KeyboardEvent, ChangeEvent, ReactNode } from \"react\";\nimport {\n  Popover,\n  FormControl,\n  FormGroup,\n  FormControlLabel,\n  Checkbox,\n  Box,\n} from \"@mui/material\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport FilterListIcon from \"@mui/icons-material/FilterList\";\nimport {\n  SearchContainer,\n  SearchPaper,\n  SearchInputBase,\n  SearchIconButton,\n  FilterIconButton,\n  FilterPopoverBox,\n} from \"@/components/common/searchBar/searchBar.style\";\n\ninterface ISearchBarProps {\n  onSearch?: (searchTerm: string, filters: string[]) => void;\n  placeholder?: string;\n  actionButton?: ReactNode;\n  filterOptions?: { label: string; value: string }[];\n  value?: string;\n  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;\n  onClear?: () => void;\n  sx?: any;\n}\n\nexport const SearchBar: React.FC<ISearchBarProps> = ({\n  onSearch = () => {},\n  placeholder = \"Search...\",\n  actionButton,\n  filterOptions = [],\n  value,\n  onChange,\n  onClear,\n  sx\n}) => {\n  const [searchTerm, setSearchTerm] = useState<string>(value || \"\");\n  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);\n  // Initialize with all filter options selected by default\n  const [selectedFilters, setSelectedFilters] = useState<string[]>(\n    filterOptions?.map(option => option.value) || []\n  );\n\n  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleFilterClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleFilterChange = (value: string) => {\n    setSelectedFilters((prev) => {\n      if (prev.includes(value)) {\n        return prev.filter((item) => item !== value);\n      } else {\n        return [...prev, value];\n      }\n    });\n  };\n\n  const handleSearchClick = () => {\n    onSearch(searchTerm, selectedFilters);\n  };\n\n  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === \"Enter\") {\n      handleSearchClick();\n    }\n  };\n\n  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {\n    const newValue = e.target.value;\n\n    if (onChange) {\n      // If onChange prop is provided, use it\n      onChange(e);\n    } else {\n      // Otherwise, use internal state\n      setSearchTerm(newValue);\n    }\n\n    // If the input is cleared (empty), trigger search to show all data\n    if (newValue === \"\") {\n      if (onClear) {\n        onClear();\n      } else {\n        onSearch(\"\", selectedFilters);\n      }\n    }\n  };\n\n  return (\n    <Box sx={{ display: \"flex\", alignItems: \"center\", gap: 2, ...sx }}>\n      <SearchContainer>\n        <SearchPaper\n          onSubmit={(e: React.FormEvent) => {\n            e.preventDefault();\n          }}\n        >\n          <SearchInputBase\n            placeholder={placeholder}\n            value={value !== undefined ? value : searchTerm}\n            onChange={handleInputChange}\n            onKeyPress={handleKeyPress}\n          />\n          {filterOptions.length > 0 && (\n            <FilterIconButton onClick={handleFilterClick} size=\"small\">\n              <FilterListIcon />\n            </FilterIconButton>\n          )}\n          <SearchIconButton onClick={handleSearchClick} size=\"small\">\n            <SearchIcon />\n          </SearchIconButton>\n        </SearchPaper>\n\n        <Popover\n          open={Boolean(anchorEl)}\n          anchorEl={anchorEl}\n          onClose={handleFilterClose}\n          anchorOrigin={{\n            vertical: \"bottom\",\n            horizontal: \"right\",\n          }}\n          transformOrigin={{\n            vertical: \"top\",\n            horizontal: \"right\",\n          }}\n        >\n          <FilterPopoverBox>\n            <FormControl component=\"fieldset\">\n              <FormGroup>\n                {filterOptions.map(\n                  (option: {\n                    value: React.Key | null | undefined;\n                    label:\n                     string\n                  }) => (\n                    <FormControlLabel\n                      key={option.value}\n                      control={\n                        <Checkbox\n                          checked={selectedFilters.includes(option.value as string)}\n                          onChange={() => handleFilterChange(option.value as string)}\n                          size=\"small\"\n                        />\n                      }\n                      label={option.label}\n                    />\n                  )\n                )}\n              </FormGroup>\n            </FormControl>\n          </FilterPopoverBox>\n        </Popover>\n      </SearchContainer>\n      {actionButton}\n    </Box>\n  );\n};\n\nexport default SearchBar;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;;;;;;;;AAoBO,MAAM,YAAuC,CAAC,EACnD,WAAW,KAAO,CAAC,EACnB,cAAc,WAAW,EACzB,YAAY,EACZ,gBAAgB,EAAE,EAClB,KAAK,EACL,QAAQ,EACR,OAAO,EACP,EAAE,EACH;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,SAAS;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,yDAAyD;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACnD,eAAe;8BAAI,CAAA,SAAU,OAAO,KAAK;gCAAK,EAAE;IAGlD,MAAM,oBAAoB,CAAC;QACzB,YAAY,MAAM,aAAa;IACjC;IAEA,MAAM,oBAAoB;QACxB,YAAY;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB,CAAC;YAClB,IAAI,KAAK,QAAQ,CAAC,QAAQ;gBACxB,OAAO,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS;YACxC,OAAO;gBACL,OAAO;uBAAI;oBAAM;iBAAM;YACzB;QACF;IACF;IAEA,MAAM,oBAAoB;QACxB,SAAS,YAAY;IACvB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAE/B,IAAI,UAAU;YACZ,uCAAuC;YACvC,SAAS;QACX,OAAO;YACL,gCAAgC;YAChC,cAAc;QAChB;QAEA,mEAAmE;QACnE,IAAI,aAAa,IAAI;YACnB,IAAI,SAAS;gBACX;YACF,OAAO;gBACL,SAAS,IAAI;YACf;QACF;IACF;IAEA,qBACE,6LAAC,oLAAA,CAAA,MAAG;QAAC,IAAI;YAAE,SAAS;YAAQ,YAAY;YAAU,KAAK;YAAG,GAAG,EAAE;QAAC;;0BAC9D,6LAAC,iKAAA,CAAA,kBAAe;;kCACd,6LAAC,iKAAA,CAAA,cAAW;wBACV,UAAU,CAAC;4BACT,EAAE,cAAc;wBAClB;;0CAEA,6LAAC,iKAAA,CAAA,kBAAe;gCACd,aAAa;gCACb,OAAO,UAAU,YAAY,QAAQ;gCACrC,UAAU;gCACV,YAAY;;;;;;4BAEb,cAAc,MAAM,GAAG,mBACtB,6LAAC,iKAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAmB,MAAK;0CACjD,cAAA,6LAAC,2JAAA,CAAA,UAAc;;;;;;;;;;0CAGnB,6LAAC,iKAAA,CAAA,mBAAgB;gCAAC,SAAS;gCAAmB,MAAK;0CACjD,cAAA,6LAAC,uJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;kCAIf,6LAAC,gMAAA,CAAA,UAAO;wBACN,MAAM,QAAQ;wBACd,UAAU;wBACV,SAAS;wBACT,cAAc;4BACZ,UAAU;4BACV,YAAY;wBACd;wBACA,iBAAiB;4BACf,UAAU;4BACV,YAAY;wBACd;kCAEA,cAAA,6LAAC,iKAAA,CAAA,mBAAgB;sCACf,cAAA,6LAAC,4MAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,sMAAA,CAAA,YAAS;8CACP,cAAc,GAAG,CAChB,CAAC,uBAKC,6LAAC,2NAAA,CAAA,mBAAgB;4CAEf,uBACE,6LAAC,mMAAA,CAAA,WAAQ;gDACP,SAAS,gBAAgB,QAAQ,CAAC,OAAO,KAAK;gDAC9C,UAAU,IAAM,mBAAmB,OAAO,KAAK;gDAC/C,MAAK;;;;;;4CAGT,OAAO,OAAO,KAAK;2CARd,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAiBhC;;;;;;;AAGP;GArIa;KAAA;uCAuIE", "debugId": null}}, {"offset": {"line": 3555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/searchBar/index.ts"], "sourcesContent": ["import { SearchBar } from \"./searchBar\";\r\n\r\nexport * from \"./searchBar\";\r\n\r\nexport default SearchBar;\r\n"], "names": [], "mappings": ";;;AAAA;;;uCAIe,yJAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 3580, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/columnVisibility/columnVisibilityMenu.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Menu,\n  MenuItem,\n  Checkbox,\n  ListItemText,\n  Popover,\n  FormControl,\n  FormGroup,\n  FormControlLabel,\n  Typography,\n  Divider,\n  Box\n} from '@mui/material';\n\ninterface ColumnOption {\n  value: string;\n  label: string;\n}\n\ninterface ColumnVisibilityMenuProps {\n  anchorEl: HTMLElement | null;\n  open: boolean;\n  onClose: () => void;\n  columnOptions: ColumnOption[];\n  visibleColumns: string[];\n  onColumnToggle: (value: string) => void;\n  variant?: 'menu' | 'popover';\n}\n\n/**\n * Component for displaying column visibility options\n */\nconst ColumnVisibilityMenu: React.FC<ColumnVisibilityMenuProps> = ({\n  anchorEl,\n  open,\n  onClose,\n  columnOptions,\n  visibleColumns,\n  onColumnToggle,\n  variant = 'menu'\n}) => {\n  if (variant === 'popover') {\n    return (\n      <Popover\n        open={open}\n        anchorEl={anchorEl}\n        onClose={onClose}\n        anchorOrigin={{\n          vertical: 'bottom',\n          horizontal: 'left',\n        }}\n        transformOrigin={{\n          vertical: 'top',\n          horizontal: 'left',\n        }}\n        PaperProps={{\n          sx: { \n            width: 250,\n            p: 1,\n            mt: 0.5,\n          }\n        }}\n      >\n        <Typography variant=\"subtitle2\" sx={{ p: 1, fontWeight: 600 }}>\n          Show columns:\n        </Typography>\n        <Divider />\n        <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n          <FormControl component=\"fieldset\">\n            <FormGroup>\n              {columnOptions.map((option) => (\n                <FormControlLabel\n                  key={option.value}\n                  control={\n                    <Checkbox\n                      checked={visibleColumns.includes(option.value)}\n                      onChange={() => onColumnToggle(option.value)}\n                      size=\"small\"\n                      sx={{ p: 0.5, mr: 1 }}\n                    />\n                  }\n                  label={option.label}\n                />\n              ))}\n            </FormGroup>\n          </FormControl>\n        </Box>\n      </Popover>\n    );\n  }\n\n  return (\n    <Menu\n      anchorEl={anchorEl}\n      open={open}\n      onClose={onClose}\n      PaperProps={{\n        sx: { width: 200, maxHeight: 300 },\n      }}\n    >\n      {columnOptions.map((option) => (\n        <MenuItem key={option.value} onClick={() => onColumnToggle(option.value)}>\n          <Checkbox checked={visibleColumns.includes(option.value)} />\n          <ListItemText primary={option.label} />\n        </MenuItem>\n      ))}\n    </Menu>\n  );\n};\n\nexport default ColumnVisibilityMenu;\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AA6BA;;CAEC,GACD,MAAM,uBAA4D,CAAC,EACjE,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,aAAa,EACb,cAAc,EACd,cAAc,EACd,UAAU,MAAM,EACjB;IACC,IAAI,YAAY,WAAW;QACzB,qBACE,6LAAC,gMAAA,CAAA,UAAO;YACN,MAAM;YACN,UAAU;YACV,SAAS;YACT,cAAc;gBACZ,UAAU;gBACV,YAAY;YACd;YACA,iBAAiB;gBACf,UAAU;gBACV,YAAY;YACd;YACA,YAAY;gBACV,IAAI;oBACF,OAAO;oBACP,GAAG;oBACH,IAAI;gBACN;YACF;;8BAEA,6LAAC,yMAAA,CAAA,aAAU;oBAAC,SAAQ;oBAAY,IAAI;wBAAE,GAAG;wBAAG,YAAY;oBAAI;8BAAG;;;;;;8BAG/D,6LAAC,gMAAA,CAAA,UAAO;;;;;8BACR,6LAAC,oLAAA,CAAA,MAAG;oBAAC,IAAI;wBAAE,WAAW;wBAAK,UAAU;oBAAO;8BAC1C,cAAA,6LAAC,4MAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,sMAAA,CAAA,YAAS;sCACP,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,2NAAA,CAAA,mBAAgB;oCAEf,uBACE,6LAAC,mMAAA,CAAA,WAAQ;wCACP,SAAS,eAAe,QAAQ,CAAC,OAAO,KAAK;wCAC7C,UAAU,IAAM,eAAe,OAAO,KAAK;wCAC3C,MAAK;wCACL,IAAI;4CAAE,GAAG;4CAAK,IAAI;wCAAE;;;;;;oCAGxB,OAAO,OAAO,KAAK;mCATd,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;IAiBjC;IAEA,qBACE,6LAAC,uLAAA,CAAA,OAAI;QACH,UAAU;QACV,MAAM;QACN,SAAS;QACT,YAAY;YACV,IAAI;gBAAE,OAAO;gBAAK,WAAW;YAAI;QACnC;kBAEC,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,mMAAA,CAAA,WAAQ;gBAAoB,SAAS,IAAM,eAAe,OAAO,KAAK;;kCACrE,6LAAC,mMAAA,CAAA,WAAQ;wBAAC,SAAS,eAAe,QAAQ,CAAC,OAAO,KAAK;;;;;;kCACvD,6LAAC,+MAAA,CAAA,eAAY;wBAAC,SAAS,OAAO,KAAK;;;;;;;eAFtB,OAAO,KAAK;;;;;;;;;;AAOnC;KA5EM;uCA8ES", "debugId": null}}, {"offset": {"line": 3740, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/columnVisibility/index.ts"], "sourcesContent": ["export { default as ColumnVisibilityMenu } from './columnVisibilityMenu';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/activeColumn/activeColumn.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Button,\n  Menu,\n  MenuItem,\n  Checkbox,\n  ListItemText,\n  Popover,\n  FormControl,\n  FormGroup,\n  FormControlLabel,\n  Typography,\n  Divider,\n  Box\n} from '@mui/material';\nimport ViewColumnIcon from '@mui/icons-material/ViewColumn';\n\ninterface ColumnOption {\n  value: string;\n  label: string;\n}\n\ninterface ActiveColumnProps {\n  columnOptions: ColumnOption[];\n  visibleColumns: string[];\n  onColumnVisibilityChange: (visibleColumns: string[]) => void;\n  variant?: 'menu' | 'popover';\n}\n\n/**\n * A common component for managing column visibility in tables\n * This component can be used across all pages to provide consistent column visibility functionality\n */\nexport const ActiveColumn: React.FC<ActiveColumnProps> = ({\n  columnOptions,\n  visibleColumns,\n  onColumnVisibilityChange,\n  variant = 'popover'\n}) => {\n  const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);\n\n  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleClose = () => {\n    setAnchorEl(null);\n  };\n\n  const handleColumnToggle = (value: string) => {\n    const newVisibleColumns = visibleColumns.includes(value)\n      ? visibleColumns.filter(col => col !== value)\n      : [...visibleColumns, value];\n\n    onColumnVisibilityChange(newVisibleColumns);\n  };\n\n  return (\n    <>\n      <Button\n        variant=\"outlined\"\n        startIcon={<ViewColumnIcon />}\n        onClick={handleClick}\n        size=\"small\"\n        sx={{\n          textTransform: 'none',\n          borderColor: '#e0e0e0',\n          color: 'text.primary',\n          '&:hover': {\n            backgroundColor: 'rgba(0, 0, 0, 0.04)',\n            borderColor: '#d5d5d5'\n          }\n        }}\n      >\n        Columns\n      </Button>\n\n      {variant === 'popover' ? (\n        <Popover\n          open={Boolean(anchorEl)}\n          anchorEl={anchorEl}\n          onClose={handleClose}\n          anchorOrigin={{\n            vertical: 'bottom',\n            horizontal: 'left',\n          }}\n          transformOrigin={{\n            vertical: 'top',\n            horizontal: 'left',\n          }}\n          PaperProps={{\n            sx: {\n              width: 320,\n              p: 1.5,\n              mt: 0.5,\n            }\n          }}\n        >\n          <Typography variant=\"subtitle2\" sx={{ p: 1.5, fontWeight: 600, fontSize: '0.95rem' }}>\n            Show columns:\n          </Typography>\n          <Divider />\n          <Box sx={{ maxHeight: 350, overflow: 'auto' }}>\n            <FormControl component=\"fieldset\">\n              <FormGroup sx={{ padding: '6px 10px' }}>\n                {columnOptions.map((option) => (\n                  <FormControlLabel\n                    key={option.value}\n                    control={\n                      <Checkbox\n                        checked={visibleColumns.includes(option.value)}\n                        onChange={() => handleColumnToggle(option.value)}\n                        size=\"small\"\n                        sx={{ p: 0.75, mr: 1.5 }}\n                      />\n                    }\n                    label={option.label}\n                    sx={{\n                      width: '100%',\n                      margin: '4px 0',\n                      '& .MuiFormControlLabel-label': {\n                        fontSize: '0.9rem'\n                      }\n                    }}\n                  />\n                ))}\n              </FormGroup>\n            </FormControl>\n          </Box>\n        </Popover>\n      ) : (\n        <Menu\n          anchorEl={anchorEl}\n          open={Boolean(anchorEl)}\n          onClose={handleClose}\n          PaperProps={{\n            sx: { width: 320, maxHeight: 300, p: 1 },\n          }}\n        >\n          {columnOptions.map((option) => (\n            <MenuItem key={option.value} onClick={() => handleColumnToggle(option.value)}>\n              <Checkbox checked={visibleColumns.includes(option.value)} />\n              <ListItemText primary={option.label} />\n            </MenuItem>\n          ))}\n        </Menu>\n      )}\n    </>\n  );\n};\n\nexport default ActiveColumn;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;;;;AAkBO,MAAM,eAA4C,CAAC,EACxD,aAAa,EACb,cAAc,EACd,wBAAwB,EACxB,UAAU,SAAS,EACpB;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAqB;IAEnE,MAAM,cAAc,CAAC;QACnB,YAAY,MAAM,aAAa;IACjC;IAEA,MAAM,cAAc;QAClB,YAAY;IACd;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,oBAAoB,eAAe,QAAQ,CAAC,SAC9C,eAAe,MAAM,CAAC,CAAA,MAAO,QAAQ,SACrC;eAAI;YAAgB;SAAM;QAE9B,yBAAyB;IAC3B;IAEA,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,yBAAW,6LAAC,2JAAA,CAAA,UAAc;;;;;gBAC1B,SAAS;gBACT,MAAK;gBACL,IAAI;oBACF,eAAe;oBACf,aAAa;oBACb,OAAO;oBACP,WAAW;wBACT,iBAAiB;wBACjB,aAAa;oBACf;gBACF;0BACD;;;;;;YAIA,YAAY,0BACX,6LAAC,gMAAA,CAAA,UAAO;gBACN,MAAM,QAAQ;gBACd,UAAU;gBACV,SAAS;gBACT,cAAc;oBACZ,UAAU;oBACV,YAAY;gBACd;gBACA,iBAAiB;oBACf,UAAU;oBACV,YAAY;gBACd;gBACA,YAAY;oBACV,IAAI;wBACF,OAAO;wBACP,GAAG;wBACH,IAAI;oBACN;gBACF;;kCAEA,6LAAC,yMAAA,CAAA,aAAU;wBAAC,SAAQ;wBAAY,IAAI;4BAAE,GAAG;4BAAK,YAAY;4BAAK,UAAU;wBAAU;kCAAG;;;;;;kCAGtF,6LAAC,gMAAA,CAAA,UAAO;;;;;kCACR,6LAAC,oLAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,WAAW;4BAAK,UAAU;wBAAO;kCAC1C,cAAA,6LAAC,4MAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC,sMAAA,CAAA,YAAS;gCAAC,IAAI;oCAAE,SAAS;gCAAW;0CAClC,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,2NAAA,CAAA,mBAAgB;wCAEf,uBACE,6LAAC,mMAAA,CAAA,WAAQ;4CACP,SAAS,eAAe,QAAQ,CAAC,OAAO,KAAK;4CAC7C,UAAU,IAAM,mBAAmB,OAAO,KAAK;4CAC/C,MAAK;4CACL,IAAI;gDAAE,GAAG;gDAAM,IAAI;4CAAI;;;;;;wCAG3B,OAAO,OAAO,KAAK;wCACnB,IAAI;4CACF,OAAO;4CACP,QAAQ;4CACR,gCAAgC;gDAC9B,UAAU;4CACZ;wCACF;uCAhBK,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;qCAwB7B,6LAAC,uLAAA,CAAA,OAAI;gBACH,UAAU;gBACV,MAAM,QAAQ;gBACd,SAAS;gBACT,YAAY;oBACV,IAAI;wBAAE,OAAO;wBAAK,WAAW;wBAAK,GAAG;oBAAE;gBACzC;0BAEC,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,mMAAA,CAAA,WAAQ;wBAAoB,SAAS,IAAM,mBAAmB,OAAO,KAAK;;0CACzE,6LAAC,mMAAA,CAAA,WAAQ;gCAAC,SAAS,eAAe,QAAQ,CAAC,OAAO,KAAK;;;;;;0CACvD,6LAAC,+MAAA,CAAA,eAAY;gCAAC,SAAS,OAAO,KAAK;;;;;;;uBAFtB,OAAO,KAAK;;;;;;;;;;;;AASvC;GApHa;KAAA;uCAsHE", "debugId": null}}, {"offset": {"line": 3979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/activeColumn/index.ts"], "sourcesContent": ["export { default as ActiveColumn } from './activeColumn';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 4000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/tableFilter/tableFilterButton.style.ts"], "sourcesContent": ["import { styled, keyframes } from '@mui/material/styles';\nimport { Box, Drawer, TextField, Button, IconButton } from '@mui/material';\n\n// Animation for drawer content\nconst fadeIn = keyframes`\n  from {\n    opacity: 0;\n    transform: translateX(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n`;\n\n// Styled Drawer component\nexport const FilterDrawer = styled(Drawer)(({ theme }) => ({\n  '& .MuiDrawer-paper': {\n    width: 350,\n    height: '100%',\n    backgroundColor: theme.palette.mode === 'dark' \n      ? 'rgba(30, 30, 30, 0.95)' \n      : 'rgba(255, 255, 255, 0.98)',\n    backdropFilter: 'blur(10px)',\n    boxShadow: theme.palette.mode === 'dark'\n      ? '0 8px 32px rgba(0, 0, 0, 0.5)'\n      : '0 8px 32px rgba(0, 0, 0, 0.15)',\n    border: 'none',\n    borderRadius: '12px 0 0 12px',\n    overflowX: 'hidden',\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    transition: 'all 0.3s ease-in-out',\n  },\n}));\n\n// Container for the entire drawer content\nexport const FilterDrawerContainer = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  flexDirection: 'column',\n  height: '100%',\n  '& > *': {\n    animation: `${fadeIn} 0.3s ease-out forwards`,\n  },\n}));\n\n// Header of the filter drawer\nexport const FilterDrawerHeader = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  justifyContent: 'space-between',\n  alignItems: 'center',\n  padding: '16px 20px',\n  background: 'linear-gradient(90deg, #3A52A6 0%, #4A62B6 100%)',\n  color: 'white',\n  borderBottom: theme.palette.mode === 'dark' \n    ? '1px solid rgba(255, 255, 255, 0.1)' \n    : 'none',\n  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n}));\n\n// Close button in the header\nexport const CloseButton = styled(IconButton)(({ theme }) => ({\n  color: 'white',\n  padding: '8px',\n  borderRadius: '50%',\n  '&:hover': {\n    backgroundColor: 'rgba(255, 255, 255, 0.2)',\n  },\n  transition: 'all 0.2s ease',\n}));\n\n// Description section\nexport const FilterDescription = styled(Box)(({ theme }) => ({\n  padding: '16px 20px 8px',\n  borderBottom: theme.palette.mode === 'dark' \n    ? '1px solid rgba(255, 255, 255, 0.05)' \n    : '1px solid rgba(0, 0, 0, 0.05)',\n}));\n\n// Content area for filter fields\nexport const FilterContent = styled(Box)(({ theme }) => ({\n  padding: '16px 20px',\n  overflowY: 'auto',\n  flexGrow: 1,\n  '&::-webkit-scrollbar': {\n    width: '6px',\n  },\n  '&::-webkit-scrollbar-track': {\n    background: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',\n    borderRadius: '10px',\n  },\n  '&::-webkit-scrollbar-thumb': {\n    background: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',\n    borderRadius: '10px',\n    '&:hover': {\n      background: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',\n    },\n  },\n}));\n\n// Individual filter field container\nexport const FilterFieldContainer = styled(Box)(({ theme }) => ({\n  marginBottom: '20px',\n  padding: '12px',\n  borderRadius: '8px',\n  backgroundColor: theme.palette.mode === 'dark' \n    ? 'rgba(255, 255, 255, 0.03)' \n    : 'rgba(0, 0, 0, 0.02)',\n  transition: 'all 0.2s ease',\n  '&:hover': {\n    backgroundColor: theme.palette.mode === 'dark' \n      ? 'rgba(255, 255, 255, 0.05)' \n      : 'rgba(0, 0, 0, 0.03)',\n  },\n}));\n\n// Styled text field for inputs\nexport const StyledTextField = styled(TextField)(({ theme }) => ({\n  '& .MuiOutlinedInput-root': {\n    backgroundColor: theme.palette.mode === 'dark' \n      ? 'rgba(255, 255, 255, 0.05)' \n      : 'rgba(255, 255, 255, 0.8)',\n    borderRadius: '6px',\n    transition: 'all 0.2s ease',\n    '&:hover': {\n      backgroundColor: theme.palette.mode === 'dark' \n        ? 'rgba(255, 255, 255, 0.08)' \n        : 'rgba(255, 255, 255, 0.9)',\n    },\n    '&.Mui-focused': {\n      backgroundColor: theme.palette.mode === 'dark' \n        ? 'rgba(255, 255, 255, 0.1)' \n        : 'rgba(255, 255, 255, 1)',\n    },\n  },\n  '& .MuiInputLabel-root': {\n    color: theme.palette.mode === 'dark' \n      ? 'rgba(255, 255, 255, 0.7)' \n      : 'rgba(0, 0, 0, 0.7)',\n  },\n}));\n\n// Footer with action buttons\nexport const FilterFooter = styled(Box)(({ theme }) => ({\n  padding: '16px 20px',\n  borderTop: `1px solid ${theme.palette.mode === 'dark' \n    ? 'rgba(255, 255, 255, 0.1)' \n    : 'rgba(0, 0, 0, 0.1)'}`,\n  display: 'flex',\n  justifyContent: 'space-between',\n  backgroundColor: theme.palette.mode === 'dark' \n    ? 'rgba(20, 20, 20, 0.5)' \n    : 'rgba(245, 245, 245, 0.8)',\n}));\n\n// Clear button\nexport const ClearButton = styled(Button)(({ theme }) => ({\n  color: theme.palette.mode === 'dark' ? '#fff' : 'rgba(0, 0, 0, 0.7)',\n  borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.23)',\n  borderRadius: '6px',\n  padding: '6px 16px',\n  transition: 'all 0.2s ease',\n  '&:hover': {\n    borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)',\n    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',\n  },\n}));\n\n// Apply button\nexport const ApplyButton = styled(Button)(({ theme }) => ({\n  backgroundColor: '#3A52A6',\n  color: 'white',\n  borderRadius: '6px',\n  padding: '6px 20px',\n  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',\n  transition: 'all 0.2s ease',\n  '&:hover': {\n    backgroundColor: '#2A3F8F',\n    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',\n  },\n}));\n\n// Filter button that opens the drawer\nexport const FilterButton = styled(Button)(({ theme }) => ({\n  textTransform: 'none',\n  borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.2)' : '#e0e0e0',\n  color: theme.palette.text.primary,\n  borderRadius: '6px',\n  padding: '6px 12px',\n  transition: 'all 0.2s ease',\n  '&:hover': {\n    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',\n    borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : '#d5d5d5',\n  },\n}));\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAEA,+BAA+B;AAC/B,MAAM,SAAS,kNAAA,CAAA,YAAS,CAAC;;;;;;;;;AASzB,CAAC;AAGM,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,SAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,sBAAsB;YACpB,OAAO;YACP,QAAQ;YACR,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,2BACA;YACJ,gBAAgB;YAChB,WAAW,MAAM,OAAO,CAAC,IAAI,KAAK,SAC9B,kCACA;YACJ,QAAQ;YACR,cAAc;YACd,WAAW;YACX,UAAU;YACV,KAAK;YACL,OAAO;YACP,YAAY;QACd;IACF,CAAC;AAGM,MAAM,wBAAwB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/D,SAAS;QACT,eAAe;QACf,QAAQ;QACR,SAAS;YACP,WAAW,GAAG,OAAO,uBAAuB,CAAC;QAC/C;IACF,CAAC;AAGM,MAAM,qBAAqB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC5D,SAAS;QACT,gBAAgB;QAChB,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,OAAO;QACP,cAAc,MAAM,OAAO,CAAC,IAAI,KAAK,SACjC,uCACA;QACJ,WAAW;IACb,CAAC;AAGM,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,yMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC5D,OAAO;QACP,SAAS;QACT,cAAc;QACd,WAAW;YACT,iBAAiB;QACnB;QACA,YAAY;IACd,CAAC;AAGM,MAAM,oBAAoB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC3D,SAAS;QACT,cAAc,MAAM,OAAO,CAAC,IAAI,KAAK,SACjC,wCACA;IACN,CAAC;AAGM,MAAM,gBAAgB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,SAAS;QACT,WAAW;QACX,UAAU;QACV,wBAAwB;YACtB,OAAO;QACT;QACA,8BAA8B;YAC5B,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,8BAA8B;YAC1E,cAAc;QAChB;QACA,8BAA8B;YAC5B,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,6BAA6B;YACzE,cAAc;YACd,WAAW;gBACT,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,6BAA6B;YAC3E;QACF;IACF,CAAC;AAGM,MAAM,uBAAuB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC9D,cAAc;QACd,SAAS;QACT,cAAc;QACd,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,8BACA;QACJ,YAAY;QACZ,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,8BACA;QACN;IACF,CAAC;AAGM,MAAM,kBAAkB,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,sMAAA,CAAA,YAAS,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC/D,4BAA4B;YAC1B,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,8BACA;YACJ,cAAc;YACd,YAAY;YACZ,WAAW;gBACT,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,8BACA;YACN;YACA,iBAAiB;gBACf,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,6BACA;YACN;QACF;QACA,yBAAyB;YACvB,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,SAC1B,6BACA;QACN;IACF,CAAC;AAGM,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACtD,SAAS;QACT,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,SAC3C,6BACA,sBAAsB;QAC1B,SAAS;QACT,gBAAgB;QAChB,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,0BACA;IACN,CAAC;AAGM,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,SAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,SAAS;QAChD,aAAa,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,6BAA6B;QAC1E,cAAc;QACd,SAAS;QACT,YAAY;QACZ,WAAW;YACT,aAAa,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,6BAA6B;YAC1E,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,8BAA8B;QACjF;IACF,CAAC;AAGM,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,SAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,iBAAiB;QACjB,OAAO;QACP,cAAc;QACd,SAAS;QACT,WAAW;QACX,YAAY;QACZ,WAAW;YACT,iBAAiB;YACjB,WAAW;QACb;IACF,CAAC;AAGM,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,SAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,eAAe;QACf,aAAa,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,6BAA6B;QAC1E,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,cAAc;QACd,SAAS;QACT,YAAY;QACZ,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,8BAA8B;YAC/E,aAAa,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,6BAA6B;QAC5E;IACF,CAAC", "debugId": null}}, {"offset": {"line": 4177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/tableFilter/tableFilterButton.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Typo<PERSON>,\n  Grid,\n  useTheme,\n  <PERSON>ack,\n  Zoom,\n  Badge\n} from '@mui/material';\nimport FilterListIcon from '@mui/icons-material/FilterList';\nimport CloseIcon from '@mui/icons-material/Close';\nimport TuneIcon from '@mui/icons-material/Tune';\nimport {\n  FilterDrawer,\n  FilterDrawerContainer,\n  FilterDrawerHeader,\n  CloseButton,\n  FilterDescription,\n  FilterContent,\n  FilterFieldContainer,\n  StyledTextField,\n  FilterFooter,\n  ClearButton,\n  ApplyButton,\n  FilterButton\n} from './tableFilterButton.style';\n\ninterface FilterField {\n  id: string;\n  label: string;\n  type: 'text' | 'date' | 'email' | 'number';\n  value: string;\n}\n\ninterface TableFilterButtonProps {\n  filterOptions: { label: string; value: string; type?: 'text' | 'date' | 'email' | 'number' }[];\n  onApplyFilters: (filters: string[]) => void;\n  buttonLabel?: string;\n  title?: string;\n}\n\nconst TableFilterButton: React.FC<TableFilterButtonProps> = ({\n  filterOptions,\n  onApplyFilters,\n  buttonLabel = \"Filter\",\n  title = \"Filter Options\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const theme = useTheme();\n  const isDarkMode = theme.palette.mode === 'dark';\n  const [activeFiltersCount, setActiveFiltersCount] = useState(0);\n\n  // Initialize filter fields with empty values\n  const [filterFields, setFilterFields] = useState<FilterField[]>(\n    filterOptions.map(field => ({\n      id: field.value,\n      label: field.label,\n      type: field.type || 'text',\n      value: '',\n    }))\n  );\n\n  // Update active filters count\n  useEffect(() => {\n    const count = filterFields.filter(field => field.value && field.value.trim() !== '').length;\n    setActiveFiltersCount(count);\n  }, [filterFields]);\n\n  const handleOpen = () => {\n    setIsOpen(true);\n  };\n\n  const handleClose = () => {\n    setIsOpen(false);\n  };\n\n  // Handle input change for filter fields\n  const handleInputChange = (id: string, value: string) => {\n    setFilterFields(prevFields =>\n      prevFields.map(field =>\n        field.id === id ? { ...field, value } : field\n      )\n    );\n  };\n\n  // Handle date change for start date\n  const handleStartDateChange = (id: string, dateStr: string) => {\n    setFilterFields(prevFields =>\n      prevFields.map(field => {\n        if (field.id === id) {\n          const parts = field.value.split('|');\n          return { ...field, value: `${dateStr}|${parts[1] || ''}` };\n        }\n        return field;\n      })\n    );\n  };\n\n  // Handle date change for end date\n  const handleEndDateChange = (id: string, dateStr: string) => {\n    setFilterFields(prevFields =>\n      prevFields.map(field => {\n        if (field.id === id) {\n          const parts = field.value.split('|');\n          return { ...field, value: `${parts[0] || ''}|${dateStr}` };\n        }\n        return field;\n      })\n    );\n  };\n\n  // Handle apply button click\n  const handleApply = () => {\n    const filters: Record<string, string> = {};\n\n    filterFields.forEach(field => {\n      if (field.value) {\n        filters[field.id] = field.value;\n      }\n    });\n\n    // Convert the filters object to an array of selected filter keys\n    const activeFilterKeys = Object.keys(filters).length > 0\n      ? Object.keys(filters)\n      : filterOptions.map(option => option.value);\n\n    onApplyFilters(activeFilterKeys);\n    setIsOpen(false);\n  };\n\n  // Handle clear all filters\n  const handleClear = () => {\n    setFilterFields(prevFields =>\n      prevFields.map(field => ({ ...field, value: '' }))\n    );\n  };\n\n  return (\n    <>\n      <Zoom in={true}>\n        <Badge\n          badgeContent={activeFiltersCount}\n          color=\"primary\"\n          invisible={activeFiltersCount === 0}\n          sx={{ '& .MuiBadge-badge': { fontSize: '0.7rem', height: '18px', minWidth: '18px' } }}\n        >\n          <FilterButton\n            variant=\"outlined\"\n            startIcon={activeFiltersCount > 0 ? <TuneIcon /> : <FilterListIcon />}\n            onClick={handleOpen}\n            size=\"small\"\n          >\n            {buttonLabel}\n          </FilterButton>\n        </Badge>\n      </Zoom>\n\n      <FilterDrawer\n        anchor=\"right\"\n        open={isOpen}\n        onClose={handleClose}\n        variant=\"temporary\"\n        elevation={16}\n        ModalProps={{\n          keepMounted: true,\n          style: { zIndex: 1400 }\n        }}\n        transitionDuration={{\n          enter: 400,\n          exit: 300\n        }}\n      >\n        <FilterDrawerContainer>\n          {/* Header */}\n          <FilterDrawerHeader>\n            <Typography variant=\"h6\" component=\"div\" sx={{ fontWeight: 500 }}>\n              {title}\n            </Typography>\n            <CloseButton\n              aria-label=\"close\"\n              onClick={handleClose}\n              size=\"small\"\n            >\n              <CloseIcon fontSize=\"small\" />\n            </CloseButton>\n          </FilterDrawerHeader>\n\n          {/* Description */}\n          <FilterDescription>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Apply filters to refine your search results\n            </Typography>\n          </FilterDescription>\n\n          {/* Filter Fields */}\n          <FilterContent>\n            <Stack spacing={2}>\n              {filterFields.map((field, index) => (\n                <FilterFieldContainer key={field.id} sx={{ animationDelay: `${index * 0.05}s` }}>\n                  <Typography\n                    variant=\"subtitle2\"\n                    sx={{\n                      mb: 1,\n                      fontWeight: 600,\n                      color: isDarkMode ? theme.palette.text.primary : 'black',\n                    }}\n                  >\n                    {field.label}\n                  </Typography>\n\n                  {field.type === 'date' ? (\n                    <Grid container spacing={2}>\n                      <Grid item xs={6}>\n                        <StyledTextField\n                          fullWidth\n                          type=\"date\"\n                          label=\"Start Date\"\n                          InputLabelProps={{ shrink: true }}\n                          value={field.value.split('|')[0] || ''}\n                          onChange={(e) => handleStartDateChange(field.id, e.target.value)}\n                          size=\"small\"\n                        />\n                      </Grid>\n                      <Grid item xs={6}>\n                        <StyledTextField\n                          fullWidth\n                          type=\"date\"\n                          label=\"End Date\"\n                          InputLabelProps={{ shrink: true }}\n                          value={field.value.split('|')[1] || ''}\n                          onChange={(e) => handleEndDateChange(field.id, e.target.value)}\n                          size=\"small\"\n                        />\n                      </Grid>\n                    </Grid>\n                  ) : (\n                    <StyledTextField\n                      fullWidth\n                      placeholder={`Filter by ${field.label.toLowerCase()}`}\n                      value={field.value}\n                      onChange={(e) => handleInputChange(field.id, e.target.value)}\n                      size=\"small\"\n                      type={field.type}\n                    />\n                  )}\n                </FilterFieldContainer>\n              ))}\n            </Stack>\n          </FilterContent>\n\n          {/* Action Buttons */}\n          <FilterFooter>\n            <ClearButton\n              onClick={handleClear}\n              variant=\"outlined\"\n            >\n              Clear\n            </ClearButton>\n            <ApplyButton\n              onClick={handleApply}\n              variant=\"contained\"\n            >\n              Apply\n            </ApplyButton>\n          </FilterFooter>\n        </FilterDrawerContainer>\n      </FilterDrawer>\n    </>\n  );\n};\n\nexport default TableFilterButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;;;AAdA;;;;;;;AA2CA,MAAM,oBAAsD,CAAC,EAC3D,aAAa,EACb,cAAc,EACd,cAAc,QAAQ,EACtB,QAAQ,gBAAgB,EACzB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,QAAQ,CAAA,GAAA,iMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,aAAa,MAAM,OAAO,CAAC,IAAI,KAAK;IAC1C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,6CAA6C;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7C,cAAc,GAAG;sCAAC,CAAA,QAAS,CAAC;gBAC1B,IAAI,MAAM,KAAK;gBACf,OAAO,MAAM,KAAK;gBAClB,MAAM,MAAM,IAAI,IAAI;gBACpB,OAAO;YACT,CAAC;;IAGH,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,QAAQ,aAAa,MAAM;+CAAC,CAAA,QAAS,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,OAAO;8CAAI,MAAM;YAC3F,sBAAsB;QACxB;sCAAG;QAAC;KAAa;IAEjB,MAAM,aAAa;QACjB,UAAU;IACZ;IAEA,MAAM,cAAc;QAClB,UAAU;IACZ;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,CAAC,IAAY;QACrC,gBAAgB,CAAA,aACd,WAAW,GAAG,CAAC,CAAA,QACb,MAAM,EAAE,KAAK,KAAK;oBAAE,GAAG,KAAK;oBAAE;gBAAM,IAAI;IAG9C;IAEA,oCAAoC;IACpC,MAAM,wBAAwB,CAAC,IAAY;QACzC,gBAAgB,CAAA,aACd,WAAW,GAAG,CAAC,CAAA;gBACb,IAAI,MAAM,EAAE,KAAK,IAAI;oBACnB,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAK,CAAC;oBAChC,OAAO;wBAAE,GAAG,KAAK;wBAAE,OAAO,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,IAAI;oBAAC;gBAC3D;gBACA,OAAO;YACT;IAEJ;IAEA,kCAAkC;IAClC,MAAM,sBAAsB,CAAC,IAAY;QACvC,gBAAgB,CAAA,aACd,WAAW,GAAG,CAAC,CAAA;gBACb,IAAI,MAAM,EAAE,KAAK,IAAI;oBACnB,MAAM,QAAQ,MAAM,KAAK,CAAC,KAAK,CAAC;oBAChC,OAAO;wBAAE,GAAG,KAAK;wBAAE,OAAO,GAAG,KAAK,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,SAAS;oBAAC;gBAC3D;gBACA,OAAO;YACT;IAEJ;IAEA,4BAA4B;IAC5B,MAAM,cAAc;QAClB,MAAM,UAAkC,CAAC;QAEzC,aAAa,OAAO,CAAC,CAAA;YACnB,IAAI,MAAM,KAAK,EAAE;gBACf,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,KAAK;YACjC;QACF;QAEA,iEAAiE;QACjE,MAAM,mBAAmB,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,IACnD,OAAO,IAAI,CAAC,WACZ,cAAc,GAAG,CAAC,CAAA,SAAU,OAAO,KAAK;QAE5C,eAAe;QACf,UAAU;IACZ;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,gBAAgB,CAAA,aACd,WAAW,GAAG,CAAC,CAAA,QAAS,CAAC;oBAAE,GAAG,KAAK;oBAAE,OAAO;gBAAG,CAAC;IAEpD;IAEA,qBACE;;0BACE,6LAAC,uLAAA,CAAA,OAAI;gBAAC,IAAI;0BACR,cAAA,6LAAC,0LAAA,CAAA,QAAK;oBACJ,cAAc;oBACd,OAAM;oBACN,WAAW,uBAAuB;oBAClC,IAAI;wBAAE,qBAAqB;4BAAE,UAAU;4BAAU,QAAQ;4BAAQ,UAAU;wBAAO;oBAAE;8BAEpF,cAAA,6LAAC,2KAAA,CAAA,eAAY;wBACX,SAAQ;wBACR,WAAW,qBAAqB,kBAAI,6LAAC,qJAAA,CAAA,UAAQ;;;;mDAAM,6LAAC,2JAAA,CAAA,UAAc;;;;;wBAClE,SAAS;wBACT,MAAK;kCAEJ;;;;;;;;;;;;;;;;0BAKP,6LAAC,2KAAA,CAAA,eAAY;gBACX,QAAO;gBACP,MAAM;gBACN,SAAS;gBACT,SAAQ;gBACR,WAAW;gBACX,YAAY;oBACV,aAAa;oBACb,OAAO;wBAAE,QAAQ;oBAAK;gBACxB;gBACA,oBAAoB;oBAClB,OAAO;oBACP,MAAM;gBACR;0BAEA,cAAA,6LAAC,2KAAA,CAAA,wBAAqB;;sCAEpB,6LAAC,2KAAA,CAAA,qBAAkB;;8CACjB,6LAAC,yMAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAK,WAAU;oCAAM,IAAI;wCAAE,YAAY;oCAAI;8CAC5D;;;;;;8CAEH,6LAAC,2KAAA,CAAA,cAAW;oCACV,cAAW;oCACX,SAAS;oCACT,MAAK;8CAEL,cAAA,6LAAC,sJAAA,CAAA,UAAS;wCAAC,UAAS;;;;;;;;;;;;;;;;;sCAKxB,6LAAC,2KAAA,CAAA,oBAAiB;sCAChB,cAAA,6LAAC,yMAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAQ,OAAM;0CAAiB;;;;;;;;;;;sCAMrD,6LAAC,2KAAA,CAAA,gBAAa;sCACZ,cAAA,6LAAC,0LAAA,CAAA,QAAK;gCAAC,SAAS;0CACb,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,6LAAC,2KAAA,CAAA,uBAAoB;wCAAgB,IAAI;4CAAE,gBAAgB,GAAG,QAAQ,KAAK,CAAC,CAAC;wCAAC;;0DAC5E,6LAAC,yMAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,IAAI;oDACF,IAAI;oDACJ,YAAY;oDACZ,OAAO,aAAa,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG;gDACnD;0DAEC,MAAM,KAAK;;;;;;4CAGb,MAAM,IAAI,KAAK,uBACd,6LAAC,uLAAA,CAAA,OAAI;gDAAC,SAAS;gDAAC,SAAS;;kEACvB,6LAAC,uLAAA,CAAA,OAAI;wDAAC,IAAI;wDAAC,IAAI;kEACb,cAAA,6LAAC,2KAAA,CAAA,kBAAe;4DACd,SAAS;4DACT,MAAK;4DACL,OAAM;4DACN,iBAAiB;gEAAE,QAAQ;4DAAK;4DAChC,OAAO,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;4DACpC,UAAU,CAAC,IAAM,sBAAsB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4DAC/D,MAAK;;;;;;;;;;;kEAGT,6LAAC,uLAAA,CAAA,OAAI;wDAAC,IAAI;wDAAC,IAAI;kEACb,cAAA,6LAAC,2KAAA,CAAA,kBAAe;4DACd,SAAS;4DACT,MAAK;4DACL,OAAM;4DACN,iBAAiB;gEAAE,QAAQ;4DAAK;4DAChC,OAAO,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;4DACpC,UAAU,CAAC,IAAM,oBAAoB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;4DAC7D,MAAK;;;;;;;;;;;;;;;;qEAKX,6LAAC,2KAAA,CAAA,kBAAe;gDACd,SAAS;gDACT,aAAa,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,WAAW,IAAI;gDACrD,OAAO,MAAM,KAAK;gDAClB,UAAU,CAAC,IAAM,kBAAkB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK;gDAC3D,MAAK;gDACL,MAAM,MAAM,IAAI;;;;;;;uCA5CK,MAAM,EAAE;;;;;;;;;;;;;;;sCAqDzC,6LAAC,2KAAA,CAAA,eAAY;;8CACX,6LAAC,2KAAA,CAAA,cAAW;oCACV,SAAS;oCACT,SAAQ;8CACT;;;;;;8CAGD,6LAAC,2KAAA,CAAA,cAAW;oCACV,SAAS;oCACT,SAAQ;8CACT;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GApOM;;QAOU,iMAAA,CAAA,WAAQ;;;KAPlB;uCAsOS", "debugId": null}}, {"offset": {"line": 4561, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/tableFilter/index.ts"], "sourcesContent": ["export { default as TableFilterButton } from './tableFilterButton';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 4582, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/index.ts"], "sourcesContent": ["export * from './breadcrumb'\r\nexport * from './error'\r\nexport * from './icon'\r\nexport * from './pageHeader'\r\nexport * from './spacing'\r\nexport * from './textInputField'\r\nexport * from './table'\r\nexport * from './avatarMenu'\r\nexport * from './severitySlider'\r\nexport * from './charts'\r\nexport * from './pagination'\r\nexport * from './searchBar'\r\nexport * from './columnVisibility'\r\nexport * from './activeColumn'\r\nexport * from './tableFilter'\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 4655, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/suppressHydrationWarning.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from \"react\";\n\ninterface SuppressHydrationWarningProps {\n  children: React.ReactNode;\n}\n\n/**\n * A component that suppresses hydration warnings by only rendering children on the client side\n * This is useful for components that use browser-specific APIs or have browser extensions that modify the DOM\n */\nexport function SuppressHydrationWarning({ children }: SuppressHydrationWarningProps) {\n  const [isMounted, setIsMounted] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  // During server-side rendering and initial client render, return a placeholder\n  // that won't cause hydration mismatches\n  if (!isMounted) {\n    // Return an empty div with suppressHydrationWarning\n    return <div style={{ display: 'none' }} suppressHydrationWarning />;\n  }\n\n  // Once mounted on client, render the actual children\n  return <div suppressHydrationWarning>{children}</div>;\n}\n\nexport default SuppressHydrationWarning;\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAYO,SAAS,yBAAyB,EAAE,QAAQ,EAAiC;;IAClF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACR,aAAa;QACf;6CAAG,EAAE;IAEL,+EAA+E;IAC/E,wCAAwC;IACxC,IAAI,CAAC,WAAW;QACd,oDAAoD;QACpD,qBAAO,6LAAC;YAAI,OAAO;gBAAE,SAAS;YAAO;YAAG,wBAAwB;;;;;;IAClE;IAEA,qDAAqD;IACrD,qBAAO,6LAAC;QAAI,wBAAwB;kBAAE;;;;;;AACxC;GAhBgB;KAAA;uCAkBD", "debugId": null}}, {"offset": {"line": 4712, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/app/super-admin/users/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useMemo, useCallback } from \"react\";\nimport {\n  Box,\n  Paper,\n  TextField,\n  useTheme,\n  Autocomplete,\n  Chip\n} from \"@mui/material\";\nimport { PageHeader, SearchBar, TableComponent, ActiveColumn } from \"@/components/common\";\nimport { SuppressHydrationWarning } from \"@/components/common/suppressHydrationWarning\";\n\n// Empty data arrays - page cleared\nconst mockCustomers: any[] = [];\nconst mockUsers: any[] = [];\n\nconst UsersPage = () => {\n  const theme = useTheme();\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [page, setPage] = useState(1);\n  const [rowsPerPage, setRowsPerPage] = useState(5);\n  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);\n  const [visibleColumns, setVisibleColumns] = useState<string[]>([\n    \"sn\",\n    \"name\",\n    \"email\",\n    \"phone\",\n    \"role\",\n    \"customer\",\n    \"status\",\n  ]);\n\n  // Column options for the ActiveColumn component\n  const columnOptions = [\n    { value: \"sn\", label: \"SN\" },\n    { value: \"name\", label: \"Name\" },\n    { value: \"email\", label: \"Email\" },\n    { value: \"phone\", label: \"Phone\" },\n    { value: \"role\", label: \"Role\" },\n    { value: \"customer\", label: \"Customer\" },\n    { value: \"status\", label: \"Status\" },\n  ];\n\n  // Filter options for the SearchBar component\n  const filterOptions = [\n    { value: \"name\", label: \"Name\" },\n    { value: \"email\", label: \"Email\" },\n    { value: \"phone\", label: \"Phone\" },\n    { value: \"customer\", label: \"Customer\" },\n  ];\n\n  // Handle search\n  const handleSearch = useCallback((value: string, filters: string[] = []) => {\n    setSearchTerm(value);\n    setPage(1); // Reset to first page on search\n  }, []);\n\n  // Handle column visibility change\n  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {\n    setVisibleColumns(newVisibleColumns);\n  }, []);\n\n  // Handle customer selection change\n  const handleCustomerChange = useCallback((customer: any) => {\n    setSelectedCustomer(customer);\n    setPage(1); // Reset to first page on customer change\n  }, []);\n\n  // Handle page change\n  const handlePageChange = useCallback((newPage: number) => {\n    setPage(newPage);\n  }, []);\n\n  // Handle rows per page change\n  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {\n    setRowsPerPage(newRowsPerPage);\n    setPage(1); // Reset to first page when changing rows per page\n  }, []);\n\n  // Filter data based on search term and selected customer\n  const filteredData = useMemo(() => {\n    let filtered = mockUsers;\n\n    // Filter by search term\n    if (searchTerm) {\n      const searchLower = searchTerm.toLowerCase();\n      filtered = filtered.filter(user =>\n        user.name.toLowerCase().includes(searchLower) ||\n        user.email.toLowerCase().includes(searchLower) ||\n        user.phone.includes(searchLower) ||\n        user.customer.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Filter by selected customer\n    if (selectedCustomer) {\n      filtered = filtered.filter(user => user.customerId === selectedCustomer.id);\n    }\n\n    return filtered;\n  }, [mockUsers, searchTerm, selectedCustomer]);\n\n  // Calculate total pages\n  const totalCount = filteredData.length;\n\n  // Create table headers\n  const headers = useMemo(() => [\n    { id: \"sn\", label: \"SN\", sortable: true },\n    { id: \"name\", label: \"NAME\", sortable: true },\n    { id: \"email\", label: \"EMAIL\", sortable: true },\n    { id: \"phone\", label: \"PHONE\", sortable: true },\n    { id: \"role\", label: \"ROLE\", sortable: true,\n      renderCell: (row: any) => (\n        <Chip\n          label={row.role}\n          color={row.role === \"Admin\" ? \"primary\" : \"default\"}\n          size=\"small\"\n        />\n      )\n    },\n    { id: \"customer\", label: \"CUSTOMER\", sortable: true },\n    { id: \"status\", label: \"STATUS\", sortable: true,\n      renderCell: (row: any) => (\n        <Chip\n          label={row.status}\n          color={row.status === \"Active\" ? \"success\" : \"error\"}\n          size=\"small\"\n        />\n      )\n    },\n  ], []);\n\n  // Create header with search and column selector\n  const header = useMemo(() => (\n    <SuppressHydrationWarning>\n      <PageHeader\n        title=\"User Management\"\n        breadcrumbs={[\n          { name: \"Dashboard\", path: \"/dashboard\", forwardParam: false },\n          { name: \"Super Admin\", path: \"/super-admin/dashboard\", forwardParam: false },\n          { name: \"Users\", path: \"/super-admin/users\", forwardParam: false },\n        ]}\n        actions={\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <SearchBar\n              placeholder=\"Search users...\"\n              value={searchTerm}\n              onChange={(e) => handleSearch(e.target.value)}\n              onClear={() => handleSearch(\"\")}\n              sx={{ minWidth: 300 }}\n            />\n            <ActiveColumn\n              columnOptions={columnOptions}\n              visibleColumns={visibleColumns}\n              onColumnVisibilityChange={handleColumnVisibilityChange}\n              variant=\"popover\"\n            />\n            <Autocomplete\n              options={mockCustomers}\n              getOptionLabel={(option) => option.name}\n              renderInput={(params) => (\n                <TextField\n                  {...params}\n                  label=\"Select Customer\"\n                  variant=\"outlined\"\n                  size=\"small\"\n                />\n              )}\n              value={selectedCustomer}\n              onChange={(_, newValue) => handleCustomerChange(newValue)}\n              sx={{ minWidth: 200 }}\n            />\n          </Box>\n        }\n      />\n    </SuppressHydrationWarning>\n  ), [handleSearch, searchTerm, columnOptions, visibleColumns, handleColumnVisibilityChange, selectedCustomer, handleCustomerChange]);\n\n  // Create table component\n  const table = useMemo(() => (\n    <Paper elevation={0} sx={{ mt: 3, borderRadius: 1, overflow: 'hidden' }}>\n      <SuppressHydrationWarning>\n        <TableComponent<any>\n          isLoading={false}\n          headerField={headers}\n          tableBody={filteredData}\n          paginationData={{\n            onPageChange: handlePageChange,\n            onRowsPerPageChange: handleRowsPerPageChange,\n            total: totalCount,\n            page: page,\n            limit: rowsPerPage,\n          }}\n          translation={{\n            noDataTitle: \"No Users Found\",\n          }}\n          maxHeight={600}\n          onRowClick={() => {}}\n          visibleColumns={visibleColumns}\n        />\n      </SuppressHydrationWarning>\n    </Paper>\n  ), [\n    headers,\n    filteredData,\n    handlePageChange,\n    handleRowsPerPageChange,\n    totalCount,\n    page,\n    rowsPerPage,\n    visibleColumns,\n  ]);\n\n  return (\n    <div>\n      {header}\n      {table}\n    </div>\n  );\n};\n\nexport default UsersPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAZA;;;;;AAcA,mCAAmC;AACnC,MAAM,gBAAuB,EAAE;AAC/B,MAAM,YAAmB,EAAE;AAE3B,MAAM,YAAY;;IAChB,MAAM,QAAQ,CAAA,GAAA,iMAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC7D;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gDAAgD;IAChD,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAM,OAAO;QAAK;QAC3B;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAU,OAAO;QAAS;KACpC;IAED,6CAA6C;IAC7C,MAAM,gBAAgB;QACpB;YAAE,OAAO;YAAQ,OAAO;QAAO;QAC/B;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAS,OAAO;QAAQ;QACjC;YAAE,OAAO;YAAY,OAAO;QAAW;KACxC;IAED,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC,OAAe,UAAoB,EAAE;YACrE,cAAc;YACd,QAAQ,IAAI,gCAAgC;QAC9C;8CAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,+BAA+B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YAChD,kBAAkB;QACpB;8DAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACxC,oBAAoB;YACpB,QAAQ,IAAI,yCAAyC;QACvD;sDAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;YACpC,QAAQ;QACV;kDAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,CAAC;YAC3C,eAAe;YACf,QAAQ,IAAI,kDAAkD;QAChE;yDAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YAC3B,IAAI,WAAW;YAEf,wBAAwB;YACxB,IAAI,YAAY;gBACd,MAAM,cAAc,WAAW,WAAW;gBAC1C,WAAW,SAAS,MAAM;uDAAC,CAAA,OACzB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAClC,KAAK,KAAK,CAAC,QAAQ,CAAC,gBACpB,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;;YAEzC;YAEA,8BAA8B;YAC9B,IAAI,kBAAkB;gBACpB,WAAW,SAAS,MAAM;uDAAC,CAAA,OAAQ,KAAK,UAAU,KAAK,iBAAiB,EAAE;;YAC5E;YAEA,OAAO;QACT;0CAAG;QAAC;QAAW;QAAY;KAAiB;IAE5C,wBAAwB;IACxB,MAAM,aAAa,aAAa,MAAM;IAEtC,uBAAuB;IACvB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE,IAAM;gBAC5B;oBAAE,IAAI;oBAAM,OAAO;oBAAM,UAAU;gBAAK;gBACxC;oBAAE,IAAI;oBAAQ,OAAO;oBAAQ,UAAU;gBAAK;gBAC5C;oBAAE,IAAI;oBAAS,OAAO;oBAAS,UAAU;gBAAK;gBAC9C;oBAAE,IAAI;oBAAS,OAAO;oBAAS,UAAU;gBAAK;gBAC9C;oBAAE,IAAI;oBAAQ,OAAO;oBAAQ,UAAU;oBACrC,UAAU;sDAAE,CAAC,oBACX,6LAAC,uLAAA,CAAA,OAAI;gCACH,OAAO,IAAI,IAAI;gCACf,OAAO,IAAI,IAAI,KAAK,UAAU,YAAY;gCAC1C,MAAK;;;;;;;gBAGX;gBACA;oBAAE,IAAI;oBAAY,OAAO;oBAAY,UAAU;gBAAK;gBACpD;oBAAE,IAAI;oBAAU,OAAO;oBAAU,UAAU;oBACzC,UAAU;sDAAE,CAAC,oBACX,6LAAC,uLAAA,CAAA,OAAI;gCACH,OAAO,IAAI,MAAM;gCACjB,OAAO,IAAI,MAAM,KAAK,WAAW,YAAY;gCAC7C,MAAK;;;;;;;gBAGX;aACD;qCAAE,EAAE;IAEL,gDAAgD;IAChD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qCAAE,kBACrB,6LAAC,2JAAA,CAAA,2BAAwB;0BACvB,cAAA,6LAAC,2JAAA,CAAA,aAAU;oBACT,OAAM;oBACN,aAAa;wBACX;4BAAE,MAAM;4BAAa,MAAM;4BAAc,cAAc;wBAAM;wBAC7D;4BAAE,MAAM;4BAAe,MAAM;4BAA0B,cAAc;wBAAM;wBAC3E;4BAAE,MAAM;4BAAS,MAAM;4BAAsB,cAAc;wBAAM;qBAClE;oBACD,uBACE,6LAAC,oLAAA,CAAA,MAAG;wBAAC,IAAI;4BAAE,SAAS;4BAAQ,YAAY;4BAAU,KAAK;wBAAE;;0CACvD,6LAAC,yJAAA,CAAA,YAAS;gCACR,aAAY;gCACZ,OAAO;gCACP,QAAQ;iEAAE,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;;gCAC5C,OAAO;iEAAE,IAAM,aAAa;;gCAC5B,IAAI;oCAAE,UAAU;gCAAI;;;;;;0CAEtB,6LAAC,0MAAA,CAAA,eAAY;gCACX,eAAe;gCACf,gBAAgB;gCAChB,0BAA0B;gCAC1B,SAAQ;;;;;;0CAEV,6LAAC,+NAAA,CAAA,eAAY;gCACX,SAAS;gCACT,cAAc;iEAAE,CAAC,SAAW,OAAO,IAAI;;gCACvC,WAAW;iEAAE,CAAC,uBACZ,6LAAC,sMAAA,CAAA,YAAS;4CACP,GAAG,MAAM;4CACV,OAAM;4CACN,SAAQ;4CACR,MAAK;;;;;;;gCAGT,OAAO;gCACP,QAAQ;iEAAE,CAAC,GAAG,WAAa,qBAAqB;;gCAChD,IAAI;oCAAE,UAAU;gCAAI;;;;;;;;;;;;;;;;;;;;;;oCAM7B;QAAC;QAAc;QAAY;QAAe;QAAgB;QAA8B;QAAkB;KAAqB;IAElI,yBAAyB;IACzB,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE,kBACpB,6LAAC,0LAAA,CAAA,QAAK;gBAAC,WAAW;gBAAG,IAAI;oBAAE,IAAI;oBAAG,cAAc;oBAAG,UAAU;gBAAS;0BACpE,cAAA,6LAAC,2JAAA,CAAA,2BAAwB;8BACvB,cAAA,6LAAC,0JAAA,CAAA,iBAAc;wBACb,WAAW;wBACX,aAAa;wBACb,WAAW;wBACX,gBAAgB;4BACd,cAAc;4BACd,qBAAqB;4BACrB,OAAO;4BACP,MAAM;4BACN,OAAO;wBACT;wBACA,aAAa;4BACX,aAAa;wBACf;wBACA,WAAW;wBACX,UAAU;wDAAE,KAAO;;wBACnB,gBAAgB;;;;;;;;;;;;;;;;mCAIrB;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;;YACE;YACA;;;;;;;AAGP;GA3MM;;QACU,iMAAA,CAAA,WAAQ;;;KADlB;uCA6MS", "debugId": null}}]}