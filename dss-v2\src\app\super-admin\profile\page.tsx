"use client";

import React, { useState, useMemo, useCallback } from "react";
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Button,
  Divider,
  Paper,
  Tab,
  Tabs,
  CircularProgress,
  useTheme,
  InputAdornment,
  TextField,
  IconButton,
  Chip,
  Badge,
  LinearProgress,
  alpha,
  Switch,
  FormControlLabel,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
} from "@mui/material";
import {
  faUser,
  faLock,
  faEnvelope,
  faPhone,
  faBriefcase,
  faIdBadge,
  faEdit,
  faSave,
  faTimes,
  faEye,
  faEyeSlash,
  faCamera,
  faShield,
  faHistory,
  faChartLine,
  faCalendarAlt,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle,
  faBell,
  faSignOutAlt,
  faCog,
  faPalette,
  faSun,
  faMoon,
  faLaptop,
  faBuilding,
  faKey,
  faUserShield,
  faMapMarkerAlt,
  faGlobe,
  faUserCog,
  faFingerprint,
  faShieldAlt,
  faClipboardList,
  faServer,
  faNetworkWired,
  faDatabase,
  faFileAlt,
  faUserTie,
} from "@fortawesome/free-solid-svg-icons";
import { PageHeader } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import { Icon } from "@/components/common/icon";
import { useTheme as useAppTheme } from "@/context/ThemeContext";
import { DEFAULT_THEME_LIST } from "@/constants/theme";

interface TabPanelProps {
  children?: React.ReactNode;
  value: number;
  index: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `profile-tab-${index}`,
    "aria-controls": `profile-tabpanel-${index}`,
  };
}

interface IUserData {
  fullName: string;
  email: string;
  role: string;
  phoneNumber: string;
  department: string;
  location: string;
  joinDate: string;
  lastActive: string;
  bio: string;
  profileImage: string | null;
}

interface IPasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface INotificationSettings {
  emailAlerts: boolean;
  securityAlerts: boolean;
  systemUpdates: boolean;
  loginAttempts: boolean;
  newUsers: boolean;
  licenseExpiry: boolean;
}

interface IActivityLog {
  id: number;
  action: string;
  timestamp: string;
  details: string;
  icon: any;
  severity: "info" | "warning" | "error" | "success";
}

const SuperAdminProfilePage = () => {
  const theme = useTheme();
  const { mode, setTheme, actualTheme } = useAppTheme();
  const [tabValue, setTabValue] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Mock user data - in a real app, this would come from an API or Redux store
  const [userData, setUserData] = useState<IUserData>({
    fullName: "John Administrator",
    email: "<EMAIL>",
    role: "Super Administrator",
    phoneNumber: "+****************",
    department: "Security Operations",
    location: "New York, USA",
    joinDate: "January 15, 2023",
    lastActive: "Today at 10:45 AM",
    bio: "Experienced security professional with over 10 years in cybersecurity. Specializing in phishing prevention and email security infrastructure.",
    profileImage: null,
  });

  const [passwordData, setPasswordData] = useState<IPasswordData>({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [notificationSettings, setNotificationSettings] = useState<INotificationSettings>({
    emailAlerts: true,
    securityAlerts: true,
    systemUpdates: true,
    loginAttempts: true,
    newUsers: false,
    licenseExpiry: true,
  });

  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // Mock activity logs
  const activityLogs: IActivityLog[] = [
    {
      id: 1,
      action: "Login",
      timestamp: "Today at 09:30 AM",
      details: "Successful login from *************",
      icon: faSignOutAlt,
      severity: "success",
    },
    {
      id: 2,
      action: "License Management",
      timestamp: "Yesterday at 03:45 PM",
      details: "Added 5 new licenses for Oracle",
      icon: faKey,
      severity: "info",
    },
    {
      id: 3,
      action: "User Management",
      timestamp: "Yesterday at 02:15 PM",
      details: "Created new user <NAME_EMAIL>",
      icon: faUserCog,
      severity: "info",
    },
    {
      id: 4,
      action: "Security Alert",
      timestamp: "May 15, 2023 at 11:20 AM",
      details: "Failed login attempt from unknown IP address",
      icon: faExclamationTriangle,
      severity: "warning",
    },
    {
      id: 5,
      action: "System Update",
      timestamp: "May 10, 2023 at 09:00 AM",
      details: "System updated to version 2.4.0",
      icon: faServer,
      severity: "info",
    },
  ];

  // Breadcrumbs for the page
  const breadcrumbs = [
    { name: "Dashboard", path: "/dashboard", forwardParam: false },
    { name: "Super Admin", path: "/super-admin/dashboard", forwardParam: false },
    { name: "Profile", path: "/super-admin/profile", forwardParam: false },
  ];

  // Theme options from constants
  const themeOptions = DEFAULT_THEME_LIST;

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleEditToggle = useCallback(() => {
    setIsEditing((prev) => !prev);
  }, []);

  const handlePasswordChangeToggle = useCallback(() => {
    setIsChangingPassword((prev) => !prev);
    // Reset password fields when toggling
    if (!isChangingPassword) {
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    }
  }, [isChangingPassword]);

  const handleInputChange = useCallback(
    (field: keyof IUserData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setUserData((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));
    },
    []
  );

  const handlePasswordChange = useCallback(
    (field: keyof IPasswordData) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setPasswordData((prev) => ({
        ...prev,
        [field]: e.target.value,
      }));
    },
    []
  );

  const handleNotificationChange = useCallback(
    (setting: keyof INotificationSettings) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setNotificationSettings((prev) => ({
        ...prev,
        [setting]: e.target.checked,
      }));
    },
    []
  );

  const handleSaveProfile = useCallback(async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setIsLoading(false);
    setIsEditing(false);
    // In a real app, you would save the changes to the backend here
    console.log("Profile saved:", userData);
  }, [userData]);

  const handleSavePassword = useCallback(async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      // In a real app, you would show an error message here
      console.error("Passwords do not match");
      return;
    }

    setIsLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000));
    setIsLoading(false);
    setIsChangingPassword(false);
    // Reset password fields
    setPasswordData({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
    // In a real app, you would save the changes to the backend here
    console.log("Password changed");
  }, [passwordData]);

  const handleThemeChange = useCallback(
    (newTheme: string) => {
      setTheme(newTheme as "light" | "dark" | "system");
    },
    [setTheme]
  );

  const togglePasswordVisibility = useCallback(
    (field: keyof typeof showPassword) => {
      setShowPassword((prev) => ({
        ...prev,
        [field]: !prev[field],
      }));
    },
    []
  );

  // Create header with edit button
  const header = useMemo(
    () => (
      <SuppressHydrationWarning>
        <PageHeader
          title="My Profile"
          breadcrumbs={breadcrumbs}
          actions={
            <Button
              variant="contained"
              startIcon={<Icon icon={isEditing ? faSave : faEdit} size="small" onlyIcon />}
              onClick={isEditing ? handleSaveProfile : handleEditToggle}
              disabled={isLoading}
              sx={{ textTransform: "none" }}
            >
              {isLoading && isEditing ? (
                <CircularProgress size={24} color="inherit" />
              ) : isEditing ? (
                "Save Changes"
              ) : (
                "Edit Profile"
              )}
            </Button>
          }
        />
      </SuppressHydrationWarning>
    ),
    [breadcrumbs, isEditing, isLoading, handleEditToggle, handleSaveProfile]
  );

  return (
    <div>
      {header}
      <Box sx={{ mt: 3 }}>
        <Grid container spacing={3}>
          {/* Profile Summary Card */}
          <Grid item xs={12} md={4}>
            <Card elevation={0} sx={{ borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
              <CardContent sx={{ textAlign: "center", py: 4 }}>
                <Badge
                  overlap="circular"
                  anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
                  badgeContent={
                    <IconButton
                      sx={{
                        bgcolor: theme.palette.primary.main,
                        color: "white",
                        "&:hover": { bgcolor: theme.palette.primary.dark },
                        width: 32,
                        height: 32,
                      }}
                      disabled={!isEditing}
                    >
                      <Icon icon={faCamera} size="small" onlyIcon />
                    </IconButton>
                  }
                >
                  <Avatar
                    sx={{
                      width: 120,
                      height: 120,
                      mx: "auto",
                      bgcolor: theme.palette.primary.main,
                      fontSize: "3rem",
                      border: `4px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                    }}
                  >
                    {userData.fullName.trim().charAt(0)}
                  </Avatar>
                </Badge>
                <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 2 }}>
                  {userData.fullName}
                </Typography>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  {userData.email}
                </Typography>
                <Chip
                  icon={<Icon icon={faUserShield} size="small" onlyIcon />}
                  label={userData.role}
                  color="primary"
                  sx={{ mt: 1 }}
                />
                <Box sx={{ mt: 3, textAlign: "left" }}>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
                    <Box
                      sx={{
                        mr: 1.5,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 28,
                        height: 28,
                        borderRadius: "50%",
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                      }}
                    >
                      <Icon icon={faPhone} size="small" onlyIcon sx={{ color: theme.palette.primary.main }} />
                    </Box>
                    <Typography variant="body2">{userData.phoneNumber}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
                    <Box
                      sx={{
                        mr: 1.5,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 28,
                        height: 28,
                        borderRadius: "50%",
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                      }}
                    >
                      <Icon icon={faBriefcase} size="small" onlyIcon sx={{ color: theme.palette.primary.main }} />
                    </Box>
                    <Typography variant="body2">{userData.department}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
                    <Box
                      sx={{
                        mr: 1.5,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 28,
                        height: 28,
                        borderRadius: "50%",
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                      }}
                    >
                      <Icon icon={faMapMarkerAlt} size="small" onlyIcon sx={{ color: theme.palette.primary.main }} />
                    </Box>
                    <Typography variant="body2">{userData.location}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", mb: 1.5 }}>
                    <Box
                      sx={{
                        mr: 1.5,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 28,
                        height: 28,
                        borderRadius: "50%",
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                      }}
                    >
                      <Icon icon={faCalendarAlt} size="small" onlyIcon sx={{ color: theme.palette.primary.main }} />
                    </Box>
                    <Typography variant="body2">Joined: {userData.joinDate}</Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Box
                      sx={{
                        mr: 1.5,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        width: 28,
                        height: 28,
                        borderRadius: "50%",
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                      }}
                    >
                      <Icon icon={faHistory} size="small" onlyIcon sx={{ color: theme.palette.primary.main }} />
                    </Box>
                    <Typography variant="body2">Last active: {userData.lastActive}</Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>

            {/* Theme Settings Card */}
            <Card elevation={0} sx={{ mt: 3, borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
              <CardContent>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    mb: 3,
                    display: "flex",
                    alignItems: "center",
                    position: "relative",
                    "&::after": {
                      content: '""',
                      position: "absolute",
                      bottom: -8,
                      left: 0,
                      width: "40px",
                      height: "3px",
                      background: "linear-gradient(90deg, #3A52A6, #8A94C5)",
                      borderRadius: "3px",
                    },
                  }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: 32,
                      height: 32,
                      borderRadius: "50%",
                      bgcolor: alpha("#3A52A6", 0.1),
                      mr: 1.5,
                      boxShadow: "0 4px 8px rgba(58, 82, 166, 0.15)",
                    }}
                  >
                    <Icon icon={faPalette} size="small" onlyIcon sx={{ color: "#3A52A6" }} />
                  </Box>
                  Theme Settings
                </Typography>

                <List disablePadding>
                  {themeOptions.map((option) => (
                    <ListItem
                      key={option.value}
                      disablePadding
                      sx={{
                        py: 1,
                        borderRadius: 1,
                        mb: 1,
                        bgcolor: mode === option.value ? alpha(theme.palette.primary.main, 0.1) : "transparent",
                      }}
                    >
                      <ListItemIcon sx={{ minWidth: 40 }}>
                        <Icon
                          icon={
                            option.value === "light"
                              ? faSun
                              : option.value === "dark"
                              ? faMoon
                              : faLaptop
                          }
                          size="small"
                          onlyIcon
                          sx={{ color: mode === option.value ? theme.palette.primary.main : "text.secondary" }}
                        />
                      </ListItemIcon>
                      <ListItemText primary={option.label} />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleThemeChange(option.value)}
                          sx={{
                            color: mode === option.value ? theme.palette.primary.main : "text.secondary",
                          }}
                        >
                          {mode === option.value && <Icon icon={faCheckCircle} size="small" onlyIcon />}
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Profile Details and Settings */}
          <Grid item xs={12} md={8}>
            <Paper elevation={0} sx={{ borderRadius: 2, border: `1px solid ${theme.palette.divider}` }}>
              <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  aria-label="profile tabs"
                  sx={{ px: 2 }}
                  variant="scrollable"
                  scrollButtons="auto"
                >
                  <Tab
                    icon={
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 32,
                          height: 32,
                          borderRadius: "50%",
                          bgcolor: tabValue === 0 ? alpha("#3A52A6", 0.15) : "transparent",
                          transition: "all 0.3s ease",
                          mb: 0.5,
                        }}
                      >
                        <Icon
                          icon={faUser}
                          size="small"
                          onlyIcon
                          sx={{
                            color:
                              tabValue === 0
                                ? "#3A52A6"
                                : theme.palette.mode === "dark"
                                ? "rgba(255, 255, 255, 0.7)"
                                : "rgba(0, 0, 0, 0.7)",
                            transition: "all 0.3s ease",
                          }}
                        />
                      </Box>
                    }
                    label="Profile Details"
                    {...a11yProps(0)}
                    sx={{
                      textTransform: "none",
                      fontWeight: tabValue === 0 ? "bold" : "medium",
                      py: 2,
                      px: 3,
                      borderRadius: 2,
                      color: theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)",
                      "&.Mui-selected": {
                        color: "#3A52A6",
                        fontWeight: "bold",
                      },
                    }}
                  />
                  <Tab
                    icon={
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 32,
                          height: 32,
                          borderRadius: "50%",
                          bgcolor: tabValue === 1 ? alpha("#3A52A6", 0.15) : "transparent",
                          transition: "all 0.3s ease",
                          mb: 0.5,
                        }}
                      >
                        <Icon
                          icon={faLock}
                          size="small"
                          onlyIcon
                          sx={{
                            color:
                              tabValue === 1
                                ? "#3A52A6"
                                : theme.palette.mode === "dark"
                                ? "rgba(255, 255, 255, 0.7)"
                                : "rgba(0, 0, 0, 0.7)",
                            transition: "all 0.3s ease",
                          }}
                        />
                      </Box>
                    }
                    label="Security"
                    {...a11yProps(1)}
                    sx={{
                      textTransform: "none",
                      fontWeight: tabValue === 1 ? "bold" : "medium",
                      py: 2,
                      px: 3,
                      borderRadius: 2,
                      color: theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)",
                      "&.Mui-selected": {
                        color: "#3A52A6",
                        fontWeight: "bold",
                      },
                    }}
                  />
                  <Tab
                    icon={
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 32,
                          height: 32,
                          borderRadius: "50%",
                          bgcolor: tabValue === 2 ? alpha("#3A52A6", 0.15) : "transparent",
                          transition: "all 0.3s ease",
                          mb: 0.5,
                        }}
                      >
                        <Icon
                          icon={faBell}
                          size="small"
                          onlyIcon
                          sx={{
                            color:
                              tabValue === 2
                                ? "#3A52A6"
                                : theme.palette.mode === "dark"
                                ? "rgba(255, 255, 255, 0.7)"
                                : "rgba(0, 0, 0, 0.7)",
                            transition: "all 0.3s ease",
                          }}
                        />
                      </Box>
                    }
                    label="Notifications"
                    {...a11yProps(2)}
                    sx={{
                      textTransform: "none",
                      fontWeight: tabValue === 2 ? "bold" : "medium",
                      py: 2,
                      px: 3,
                      borderRadius: 2,
                      color: theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)",
                      "&.Mui-selected": {
                        color: "#3A52A6",
                        fontWeight: "bold",
                      },
                    }}
                  />
                  <Tab
                    icon={
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 32,
                          height: 32,
                          borderRadius: "50%",
                          bgcolor: tabValue === 3 ? alpha("#3A52A6", 0.15) : "transparent",
                          transition: "all 0.3s ease",
                          mb: 0.5,
                        }}
                      >
                        <Icon
                          icon={faHistory}
                          size="small"
                          onlyIcon
                          sx={{
                            color:
                              tabValue === 3
                                ? "#3A52A6"
                                : theme.palette.mode === "dark"
                                ? "rgba(255, 255, 255, 0.7)"
                                : "rgba(0, 0, 0, 0.7)",
                            transition: "all 0.3s ease",
                          }}
                        />
                      </Box>
                    }
                    label="Activity"
                    {...a11yProps(3)}
                    sx={{
                      textTransform: "none",
                      fontWeight: tabValue === 3 ? "bold" : "medium",
                      py: 2,
                      px: 3,
                      borderRadius: 2,
                      color: theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)",
                      "&.Mui-selected": {
                        color: "#3A52A6",
                        fontWeight: "bold",
                      },
                    }}
                  />
                  <Tab
                    icon={
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 32,
                          height: 32,
                          borderRadius: "50%",
                          bgcolor: tabValue === 4 ? alpha("#3A52A6", 0.15) : "transparent",
                          transition: "all 0.3s ease",
                          mb: 0.5,
                        }}
                      >
                        <Icon
                          icon={faUserShield}
                          size="small"
                          onlyIcon
                          sx={{
                            color:
                              tabValue === 4
                                ? "#3A52A6"
                                : theme.palette.mode === "dark"
                                ? "rgba(255, 255, 255, 0.7)"
                                : "rgba(0, 0, 0, 0.7)",
                            transition: "all 0.3s ease",
                          }}
                        />
                      </Box>
                    }
                    label="Admin Access"
                    {...a11yProps(4)}
                    sx={{
                      textTransform: "none",
                      fontWeight: tabValue === 4 ? "bold" : "medium",
                      py: 2,
                      px: 3,
                      borderRadius: 2,
                      color: theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.7)",
                      "&.Mui-selected": {
                        color: "#3A52A6",
                        fontWeight: "bold",
                      },
                    }}
                  />
                </Tabs>
              </Box>

              {/* Profile Details Tab */}
              <TabPanel value={tabValue} index={0}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Personal Information
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={userData.fullName}
                      onChange={handleInputChange("fullName")}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon icon={faUser} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      value={userData.email}
                      onChange={handleInputChange("email")}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon icon={faEnvelope} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      value={userData.phoneNumber}
                      onChange={handleInputChange("phoneNumber")}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon icon={faPhone} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Department"
                      value={userData.department}
                      onChange={handleInputChange("department")}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon icon={faBriefcase} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Location"
                      value={userData.location}
                      onChange={handleInputChange("location")}
                      disabled={!isEditing}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon icon={faMapMarkerAlt} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Role"
                      value={userData.role}
                      disabled={true}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Icon icon={faUserShield} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Bio"
                      value={userData.bio}
                      onChange={handleInputChange("bio")}
                      disabled={!isEditing}
                      multiline
                      rows={4}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start" sx={{ alignSelf: "flex-start", mt: 1.5 }}>
                            <Icon icon={faIdBadge} size="small" onlyIcon />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Grid>
                </Grid>
              </TabPanel>

              {/* Security Tab */}
              <TabPanel value={tabValue} index={1}>
                <Box>
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Password Management
                  </Typography>
                  <Divider sx={{ mb: 3 }} />

                  {!isChangingPassword ? (
                    <Box sx={{ textAlign: "center", py: 3 }}>
                      <Typography variant="body1" gutterBottom>
                        Change your password to keep your account secure
                      </Typography>
                      <Button
                        variant="contained"
                        startIcon={<Icon icon={faLock} size="small" onlyIcon />}
                        onClick={handlePasswordChangeToggle}
                        sx={{ mt: 2, textTransform: "none" }}
                      >
                        Change Password
                      </Button>
                    </Box>
                  ) : (
                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Current Password"
                          type={showPassword.current ? "text" : "password"}
                          value={passwordData.currentPassword}
                          onChange={handlePasswordChange("currentPassword")}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Icon icon={faLock} size="small" onlyIcon />
                              </InputAdornment>
                            ),
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton onClick={() => togglePasswordVisibility("current")} edge="end">
                                  <Icon
                                    icon={showPassword.current ? faEyeSlash : faEye}
                                    size="small"
                                    onlyIcon
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="New Password"
                          type={showPassword.new ? "text" : "password"}
                          value={passwordData.newPassword}
                          onChange={handlePasswordChange("newPassword")}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Icon icon={faLock} size="small" onlyIcon />
                              </InputAdornment>
                            ),
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton onClick={() => togglePasswordVisibility("new")} edge="end">
                                  <Icon
                                    icon={showPassword.new ? faEyeSlash : faEye}
                                    size="small"
                                    onlyIcon
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Confirm New Password"
                          type={showPassword.confirm ? "text" : "password"}
                          value={passwordData.confirmPassword}
                          onChange={handlePasswordChange("confirmPassword")}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <Icon icon={faLock} size="small" onlyIcon />
                              </InputAdornment>
                            ),
                            endAdornment: (
                              <InputAdornment position="end">
                                <IconButton onClick={() => togglePasswordVisibility("confirm")} edge="end">
                                  <Icon
                                    icon={showPassword.confirm ? faEyeSlash : faEye}
                                    size="small"
                                    onlyIcon
                                  />
                                </IconButton>
                              </InputAdornment>
                            ),
                          }}
                        />
                      </Grid>
                      <Grid item xs={12} sx={{ display: "flex", justifyContent: "space-between" }}>
                        <Button
                          variant="outlined"
                          onClick={handlePasswordChangeToggle}
                          startIcon={<Icon icon={faTimes} size="small" onlyIcon />}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="contained"
                          onClick={handleSavePassword}
                          startIcon={
                            isLoading ? (
                              <CircularProgress size={20} color="inherit" />
                            ) : (
                              <Icon icon={faSave} size="small" onlyIcon />
                            )
                          }
                          disabled={
                            isLoading ||
                            !passwordData.currentPassword ||
                            !passwordData.newPassword ||
                            !passwordData.confirmPassword ||
                            passwordData.newPassword !== passwordData.confirmPassword
                          }
                        >
                          Save New Password
                        </Button>
                      </Grid>
                    </Grid>
                  )}

                  <Box sx={{ mt: 4 }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      Two-Factor Authentication
                    </Typography>
                    <Divider sx={{ mb: 3 }} />
                    <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <Avatar sx={{ bgcolor: "primary.main", mr: 2 }}>
                          <Icon icon={faFingerprint} size="small" onlyIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="body1" fontWeight="medium">
                            Two-Factor Authentication
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Add an extra layer of security to your account
                          </Typography>
                        </Box>
                      </Box>
                      <Button variant="outlined" color="primary">
                        Setup 2FA
                      </Button>
                    </Box>
                  </Box>
                </Box>
              </TabPanel>

              {/* Notifications Tab */}
              <TabPanel value={tabValue} index={2}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Notification Preferences
                </Typography>
                <Divider sx={{ mb: 3 }} />
                <List disablePadding>
                  <ListItem sx={{ px: 0, py: 1.5 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Icon icon={faEnvelope} size="small" onlyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email Alerts"
                      secondary="Receive important alerts via email"
                      primaryTypographyProps={{ component: "span" }}
                      secondaryTypographyProps={{ component: "span" }}
                    />
                    <Switch
                      edge="end"
                      checked={notificationSettings.emailAlerts}
                      onChange={handleNotificationChange("emailAlerts")}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem sx={{ px: 0, py: 1.5 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Icon icon={faShieldAlt} size="small" onlyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Security Alerts"
                      secondary="Get notified about security-related events"
                      primaryTypographyProps={{ component: "span" }}
                      secondaryTypographyProps={{ component: "span" }}
                    />
                    <Switch
                      edge="end"
                      checked={notificationSettings.securityAlerts}
                      onChange={handleNotificationChange("securityAlerts")}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem sx={{ px: 0, py: 1.5 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Icon icon={faServer} size="small" onlyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="System Updates"
                      secondary="Notifications about system updates and maintenance"
                      primaryTypographyProps={{ component: "span" }}
                      secondaryTypographyProps={{ component: "span" }}
                    />
                    <Switch
                      edge="end"
                      checked={notificationSettings.systemUpdates}
                      onChange={handleNotificationChange("systemUpdates")}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem sx={{ px: 0, py: 1.5 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Icon icon={faSignOutAlt} size="small" onlyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Login Attempts"
                      secondary="Get notified about login attempts to your account"
                      primaryTypographyProps={{ component: "span" }}
                      secondaryTypographyProps={{ component: "span" }}
                    />
                    <Switch
                      edge="end"
                      checked={notificationSettings.loginAttempts}
                      onChange={handleNotificationChange("loginAttempts")}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem sx={{ px: 0, py: 1.5 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Icon icon={faUserCog} size="small" onlyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="New Users"
                      secondary="Notifications when new users are created"
                      primaryTypographyProps={{ component: "span" }}
                      secondaryTypographyProps={{ component: "span" }}
                    />
                    <Switch
                      edge="end"
                      checked={notificationSettings.newUsers}
                      onChange={handleNotificationChange("newUsers")}
                    />
                  </ListItem>
                  <Divider component="li" />
                  <ListItem sx={{ px: 0, py: 1.5 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      <Icon icon={faKey} size="small" onlyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="License Expiry"
                      secondary="Get notified before licenses expire"
                      primaryTypographyProps={{ component: "span" }}
                      secondaryTypographyProps={{ component: "span" }}
                    />
                    <Switch
                      edge="end"
                      checked={notificationSettings.licenseExpiry}
                      onChange={handleNotificationChange("licenseExpiry")}
                    />
                  </ListItem>
                </List>
              </TabPanel>

              {/* Activity Tab */}
              <TabPanel value={tabValue} index={3}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Recent Activity
                </Typography>
                <Divider sx={{ mb: 3 }} />
                <List disablePadding>
                  {activityLogs.map((log) => (
                    <React.Fragment key={log.id}>
                      <ListItem sx={{ px: 0, py: 2 }}>
                        <ListItemIcon>
                          <Avatar
                            sx={{
                              bgcolor:
                                log.severity === "success"
                                  ? "success.light"
                                  : log.severity === "warning"
                                  ? "warning.light"
                                  : log.severity === "error"
                                  ? "error.light"
                                  : "info.light",
                              color:
                                log.severity === "success"
                                  ? "success.dark"
                                  : log.severity === "warning"
                                  ? "warning.dark"
                                  : log.severity === "error"
                                  ? "error.dark"
                                  : "info.dark",
                            }}
                          >
                            <Icon icon={log.icon} size="small" onlyIcon />
                          </Avatar>
                        </ListItemIcon>
                        <ListItemText
                          primary={log.action}
                          primaryTypographyProps={{ variant: "body1", fontWeight: "medium" }}
                          secondary={
                            <Box component="span">
                              <Box component="span" sx={{ display: 'block' }}>
                                {log.details}
                              </Box>
                              <Box component="span" sx={{ display: 'block', fontSize: '0.75rem' }}>
                                {log.timestamp}
                              </Box>
                            </Box>
                          }
                        />
                      </ListItem>
                      {log.id !== activityLogs.length && <Divider component="li" />}
                    </React.Fragment>
                  ))}
                </List>
                <Box sx={{ display: "flex", justifyContent: "center", mt: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<Icon icon={faHistory} size="small" onlyIcon />}
                  >
                    View All Activity
                  </Button>
                </Box>
              </TabPanel>

              {/* Admin Access Tab */}
              <TabPanel value={tabValue} index={4}>
                <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                  Super Admin Privileges
                </Typography>
                <Divider sx={{ mb: 3 }} />

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Card elevation={0} sx={{ border: `1px solid ${theme.palette.divider}`, height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                            <Icon icon={faUserTie} size="small" onlyIcon />
                          </Avatar>
                          <Typography variant="h6">User Management</Typography>
                        </Box>
                        <Typography variant="body2" paragraph>
                          As a Super Admin, you have full access to manage all users in the system.
                        </Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Create and manage user accounts" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Assign and modify user roles" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Reset user passwords" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card elevation={0} sx={{ border: `1px solid ${theme.palette.divider}`, height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                            <Icon icon={faKey} size="small" onlyIcon />
                          </Avatar>
                          <Typography variant="h6">License Management</Typography>
                        </Box>
                        <Typography variant="body2" paragraph>
                          You can manage all aspects of the licensing system.
                        </Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Create and allocate licenses" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Revoke and modify existing licenses" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="View license usage reports" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card elevation={0} sx={{ border: `1px solid ${theme.palette.divider}`, height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                            <Icon icon={faDatabase} size="small" onlyIcon />
                          </Avatar>
                          <Typography variant="h6">System Configuration</Typography>
                        </Box>
                        <Typography variant="body2" paragraph>
                          You have access to configure system-wide settings.
                        </Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Manage system parameters" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Configure security settings" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Set up integration endpoints" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Card elevation={0} sx={{ border: `1px solid ${theme.palette.divider}`, height: '100%' }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                            <Icon icon={faClipboardList} size="small" onlyIcon />
                          </Avatar>
                          <Typography variant="h6">Audit & Reporting</Typography>
                        </Box>
                        <Typography variant="body2" paragraph>
                          Access comprehensive audit logs and system reports.
                        </Typography>
                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="View detailed system logs" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Generate custom reports" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Icon icon={faCheckCircle} size="small" onlyIcon sx={{ color: 'success.main' }} />
                            </ListItemIcon>
                            <ListItemText primary="Export system data" primaryTypographyProps={{ component: "span" }} />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </TabPanel>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </div>
  );
};

export default SuperAdminProfilePage;