import React from 'react';
import { Box, Typography, Paper, Tabs, Tab, Badge } from '@mui/material';

interface SystemOverviewItem {
  title: string;
  value: string | number;
  color?: string;
  badge?: number;
}

interface SystemOverviewCardProps {
  title: string;
  icon: React.ReactNode;
  color: string;
  items: SystemOverviewItem[];
  tabs?: string[];
}

const SystemOverviewCard: React.FC<SystemOverviewCardProps> = ({
  title,
  icon,
  color,
  items,
  tabs,
}) => {
  const [activeTab, setActiveTab] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        overflow: 'hidden',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '4px',
          backgroundColor: color,
        }
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2,
          borderBottom: tabs ? '1px solid #f0f0f0' : 'none',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 36,
              height: 36,
              borderRadius: '50%',
              backgroundColor: `${color}20`,
              color: color,
              mr: 1.5,
            }}
          >
            {icon}
          </Box>
          <Typography variant="subtitle1" fontWeight="medium">
            {title}
          </Typography>
        </Box>
      </Box>

      {tabs && (
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            minHeight: '40px',
            '& .MuiTabs-indicator': {
              backgroundColor: color,
            },
            '& .MuiTab-root': {
              minHeight: '40px',
              textTransform: 'none',
              fontSize: '0.875rem',
              fontWeight: 'medium',
              color: 'text.secondary',
              '&.Mui-selected': {
                color: color,
              },
            },
          }}
        >
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              label={tab}
              sx={{ py: 1 }}
            />
          ))}
        </Tabs>
      )}

      <Box sx={{ p: 2, flexGrow: 1 }}>
        {items.map((item, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              py: 1.2,
              borderBottom: index < items.length - 1 ? '1px solid #f0f0f0' : 'none',
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {item.title}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {item.badge !== undefined && (
                <Badge
                  badgeContent={item.badge}
                  color="error"
                  sx={{ mr: 1 }}
                />
              )}
              <Typography
                variant="body2"
                fontWeight="medium"
                color={item.color || 'text.primary'}
              >
                {item.value}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Paper>
  );
};

export default SystemOverviewCard;
