"use client";

import React, { JSX, useMemo } from "react";
import { Box, Grid, Paper, Typography, Chip, Divider, Tabs, Tab } from "@mui/material";
import SecurityIcon from '@mui/icons-material/Security';
import HistoryIcon from '@mui/icons-material/History';
import EmailIcon from '@mui/icons-material/Email';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';

import { EmailDetailsController } from "./email-details.controller";
import { PageHeader } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import { useThemeWithToggle } from '@/context/ThemeContext';
import TabPanel from "./components/tabPanel";

/**
 * @page {EmailDetails} - Display Email Details Information
 * @return {JSX.Element}
 */
export default function EmailDetails(): JSX.Element {
  const { getters, handlers, ref } = EmailDetailsController();
  const {
    breadcrumbs,
    emailData,
    tabValue,
  } = getters;
  const { handleTabChange } = handlers;
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';

  // Helper function for tab accessibility
  const a11yProps = (index: number) => {
    return {
      id: `email-tab-${index}`,
      'aria-controls': `email-tabpanel-${index}`,
    };
  };

  const header = useMemo(
    () => (
      <SuppressHydrationWarning>
        <div ref={ref}>
          <PageHeader
            title="Email Details"
            breadcrumbs={breadcrumbs}
            actions={
              <Chip
                label={emailData.securityStatus.final}
                color={emailData.securityStatus.final === "UNSAFE" ? "error" : "success"}
                size="small"
                sx={{ height: '24px', fontSize: '0.75rem' }}
              />
            }
          />
        </div>
      </SuppressHydrationWarning>
    ),
    [breadcrumbs, ref, emailData.securityStatus.final]
  );

  const content = useMemo(
    () => (
      <Paper elevation={0} sx={{ p: 3, borderRadius: 1 }}>
        <Box sx={{ width: '100%' }}>
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : '#f5f5f5',
          }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="email details tabs"
              variant="fullWidth"
              sx={{
                minHeight: '48px',
                '& .MuiTab-root': {
                  textTransform: 'none',
                  minHeight: '48px',
                  py: 1,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                  fontSize: '0.9rem',
                  transition: 'all 0.2s',
                },
                '& .Mui-selected': {
                  fontWeight: 'bold',
                  color: isDarkMode ? '#fff' : '#3A52A6',
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: isDarkMode ? '#8c9eff' : '#3A52A6',
                  height: 3
                }
              }}
            >
              <Tab
                icon={<SecurityIcon fontSize="small" />}
                iconPosition="start"
                label="Details & Security"
                {...a11yProps(0)}
              />
              <Tab
                icon={<HistoryIcon fontSize="small" />}
                iconPosition="start"
                label="Dispute History"
                {...a11yProps(1)}
              />
              <Tab
                icon={<EmailIcon fontSize="small" />}
                iconPosition="start"
                label="Email Body"
                {...a11yProps(2)}
              />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={2}>
              {/* Left Column */}
              <Grid item xs={12} md={6}>
                {/* Basic Information */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Basic Information
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Message ID:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{emailData.messageId}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Sender:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{emailData.sendersEmail}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Receiver:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{emailData.receiversEmail}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Subject:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{emailData.subject}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Date:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{emailData.createTime}</Typography>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Authentication */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Authentication
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">DKIM:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      {emailData.authentication.dkim ? (
                        <Chip
                          icon={<CheckCircleIcon fontSize="small" />}
                          label="Passed"
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      ) : (
                        <Chip
                          icon={<CancelIcon fontSize="small" />}
                          label="Failed"
                          size="small"
                          color="error"
                          variant="outlined"
                        />
                      )}
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">SPF:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      {emailData.authentication.spf ? (
                        <Chip
                          icon={<CheckCircleIcon fontSize="small" />}
                          label="Passed"
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      ) : (
                        <Chip
                          icon={<CancelIcon fontSize="small" />}
                          label="Failed"
                          size="small"
                          color="error"
                          variant="outlined"
                        />
                      )}
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">DMARC:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      {emailData.authentication.dmarc ? (
                        <Chip
                          icon={<CheckCircleIcon fontSize="small" />}
                          label="Passed"
                          size="small"
                          color="success"
                          variant="outlined"
                        />
                      ) : (
                        <Chip
                          icon={<CancelIcon fontSize="small" />}
                          label="Failed"
                          size="small"
                          color="error"
                          variant="outlined"
                        />
                      )}
                    </Grid>
                  </Grid>
                </Paper>
              </Grid>

              {/* Right Column */}
              <Grid item xs={12} md={6}>
                {/* Security Status */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${emailData.securityStatus.final === "UNSAFE" ? 
                      (isDarkMode ? '#f44336' : '#d32f2f') : 
                      (isDarkMode ? '#4caf50' : '#2e7d32')}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Security Status
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Status:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Chip
                        icon={emailData.securityStatus.final === "UNSAFE" ? 
                          <WarningAmberIcon fontSize="small" /> : 
                          <CheckCircleIcon fontSize="small" />}
                        label={emailData.securityStatus.final}
                        size="small"
                        color={emailData.securityStatus.final === "UNSAFE" ? "error" : "success"}
                      />
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="textSecondary">Reason:</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{emailData.securityStatus.reason}</Typography>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Content Analysis */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                    Content Analysis
                  </Typography>
                  
                  {/* URLs */}
                  <Typography variant="subtitle2" gutterBottom>
                    URLs
                  </Typography>
                  {emailData.contentAnalysis.urls.length > 0 ? (
                    emailData.contentAnalysis.urls.map((url, index) => (
                      <Box key={index} sx={{ mb: 1 }}>
                        <Grid container spacing={1}>
                          <Grid item xs={8}>
                            <Typography variant="body2" 
                              sx={{ 
                                overflow: 'hidden', 
                                textOverflow: 'ellipsis', 
                                whiteSpace: 'nowrap' 
                              }}
                            >
                              {url.url}
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Chip
                              label={url.status}
                              size="small"
                              color={url.status === "Malicious" ? "error" : 
                                    url.status === "Safe" ? "success" : "warning"}
                              variant="outlined"
                              sx={{ height: '20px', fontSize: '0.7rem' }}
                            />
                          </Grid>
                        </Grid>
                        {index < emailData.contentAnalysis.urls.length - 1 && (
                          <Divider sx={{ my: 0.5 }} />
                        )}
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      No URLs found
                    </Typography>
                  )}
                  
                  <Divider sx={{ my: 1 }} />
                  
                  {/* Attachments */}
                  <Typography variant="subtitle2" gutterBottom>
                    Attachments
                  </Typography>
                  {emailData.contentAnalysis.attachments.length > 0 ? (
                    emailData.contentAnalysis.attachments.map((attachment, index) => (
                      <Box key={index} sx={{ mb: 1 }}>
                        <Grid container spacing={1}>
                          <Grid item xs={8}>
                            <Typography variant="body2" 
                              sx={{ 
                                overflow: 'hidden', 
                                textOverflow: 'ellipsis', 
                                whiteSpace: 'nowrap' 
                              }}
                            >
                              {attachment.name}
                            </Typography>
                          </Grid>
                          <Grid item xs={4}>
                            <Chip
                              label={attachment.status}
                              size="small"
                              color={attachment.status === "Malicious" ? "error" : 
                                    attachment.status === "Safe" ? "success" : "warning"}
                              variant="outlined"
                              sx={{ height: '20px', fontSize: '0.7rem' }}
                            />
                          </Grid>
                        </Grid>
                        {index < emailData.contentAnalysis.attachments.length - 1 && (
                          <Divider sx={{ my: 0.5 }} />
                        )}
                      </Box>
                    ))
                  ) : (
                    <Typography variant="body2" color="textSecondary">
                      No attachments found
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {emailData.disputeHistory.length > 0 ? (
              emailData.disputeHistory.map((dispute, index) => (
                <Paper
                  key={index}
                  elevation={0}
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderRadius: '4px'
                  }}
                >
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" fontWeight="bold">
                        Date: {dispute.date}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        User Comment:
                      </Typography>
                      <Typography variant="body2">{dispute.userComment}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="body2" color="textSecondary" gutterBottom>
                        Admin Response:
                      </Typography>
                      <Typography variant="body2">{dispute.adminResponse}</Typography>
                      <Box sx={{ mt: 1 }}>
                        <Chip
                          label={dispute.adminStatus}
                          size="small"
                          color={dispute.adminStatus === "Approved" ? "success" : 
                                dispute.adminStatus === "Rejected" ? "error" : "default"}
                          variant="outlined"
                        />
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              ))
            ) : (
              <Typography variant="body1" sx={{ p: 2 }}>
                No dispute history available for this email.
              </Typography>
            )}
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: '4px',
                whiteSpace: 'pre-wrap'
              }}
            >
              <Typography variant="body2">
                {emailData.emailBody || "No email body content available."}
              </Typography>
            </Paper>
          </TabPanel>
        </Box>
      </Paper>
    ),
    [tabValue, handleTabChange, emailData, isDarkMode]
  );

  return (
    <>
      {header}
      {content}
    </>
  );
}
