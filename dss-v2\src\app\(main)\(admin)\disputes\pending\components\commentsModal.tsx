"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  Paper,
  InputAdornment,
  Divider,
  Popover,
  Grid,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SendIcon from "@mui/icons-material/Send";
import EmojiEmotionsIcon from "@mui/icons-material/EmojiEmotions";
import { useThemeWithToggle } from '@/context/ThemeContext';
import { IComment } from "@/interfaces/comment.interface";
import { format } from 'date-fns';

interface CommentsModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (comment: string) => void;
  comments: IComment[];
  itemId: string;
  pendingStatus?: string | null;
  currentStatus?: string;
}

// Common emojis array
const commonEmojis = [
  '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
  '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
  '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
  '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞',
  '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '❣️', '💕',
];

const CommentsModal: React.FC<CommentsModalProps> = ({
  open,
  onClose,
  onSubmit,
  comments,
  itemId,
  pendingStatus,
  currentStatus,
}) => {
  const [newComment, setNewComment] = useState("");
  const [emojiPickerAnchorEl, setEmojiPickerAnchorEl] = useState<HTMLButtonElement | null>(null);
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when comments change or modal opens
  useEffect(() => {
    if (open && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [comments, open]);

  // Emoji picker handlers
  const handleEmojiPickerOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setEmojiPickerAnchorEl(event.currentTarget);
  };

  const handleEmojiPickerClose = () => {
    setEmojiPickerAnchorEl(null);
  };

  const handleEmojiClick = (emoji: string) => {
    setNewComment(prev => prev + emoji);
    // Optional: close the picker after selection
    // handleEmojiPickerClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      onSubmit(newComment);
      setNewComment("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Group comments by date
  const groupCommentsByDate = () => {
    const groups: { [date: string]: IComment[] } = {};

    comments.forEach(comment => {
      const date = comment.timestamp.split(' ')[0]; // Extract date part
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(comment);
    });

    return groups;
  };

  const commentGroups = groupCommentsByDate();

  // Format date for display
  const formatDateHeader = (dateStr: string) => {
    // Check if it's today, yesterday, or a specific date
    const today = format(new Date(), 'MM-dd-yyyy');
    const yesterday = format(new Date(Date.now() - 86400000), 'MM-dd-yyyy');

    if (dateStr === today) return 'Today';
    if (dateStr === yesterday) return 'Yesterday';

    // Try to parse the date string
    try {
      const [month, day, year] = dateStr.split('-');
      return `${month}/${day}/${year}`;
    } catch (e) {
      return dateStr;
    }
  };

  // Helper function to get color for status
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'safe':
        return '#2E7D32'; // Darker, less bright green
      case 'unsafe':
        return '#C62828'; // Darker, less bright red
      default:
        return '#9e9e9e';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 1,
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
          height: "80vh",
          display: "flex",
          flexDirection: "column",
        }
      }}
    >
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
        pb: 1
      }}>
        <Box>
          <Typography variant="h6" component="div" sx={{
            fontWeight: 600,
            color: isDarkMode ? "#fff" : "#000"
          }}>
            Comments
          </Typography>
          {pendingStatus && currentStatus && (
            <Typography variant="caption" sx={{
              display: 'block',
              mt: 0.5,
              color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.6)"
            }}>
              Changing status from <strong style={{ color: getStatusColor(currentStatus) }}>{currentStatus}</strong> to <strong style={{ color: getStatusColor(pendingStatus) }}>{pendingStatus}</strong>
            </Typography>
          )}
        </Box>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{
        pt: 2,
        display: 'flex',
        flexDirection: 'column',
        flexGrow: 1,
        overflow: 'auto'
      }}>
        {comments.length === 0 ? (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%'
          }}>
            <Typography variant="body1" color="text.secondary">
              No comments yet. Be the first to comment!
            </Typography>
          </Box>
        ) : (
          <Box sx={{ flexGrow: 1 }}>
            {Object.entries(commentGroups).map(([date, dateComments]) => (
              <Box key={date} sx={{ mb: 3 }}>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  mb: 2
                }}>
                  <Typography
                    variant="body2"
                    sx={{
                      bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
                      px: 2,
                      py: 0.5,
                      borderRadius: 4,
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'
                    }}
                  >
                    {formatDateHeader(date)}
                  </Typography>
                </Box>

                {dateComments.map((comment, index) => (
                  <Box
                    key={comment.id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      mb: 1.5
                    }}
                  >
                    <Box sx={{
                      maxWidth: '80%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-end'
                    }}>
                      <Typography
                        variant="caption"
                        sx={{
                          mb: 0.5,
                          color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)'
                        }}
                      >
                        {comment.user} Comment
                      </Typography>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          borderRadius: 2,
                          bgcolor: isDarkMode ? 'rgba(58, 82, 166, 0.2)' : 'rgba(226, 240, 217, 0.7)',
                          color: isDarkMode ? '#fff' : '#000'
                        }}
                      >
                        <Typography variant="body2">
                          {comment.text}
                        </Typography>
                      </Paper>
                      <Typography
                        variant="caption"
                        sx={{
                          mt: 0.5,
                          color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)'
                        }}
                      >
                        {comment.timestamp.split(' ')[1]} • {comment.status}
                      </Typography>
                    </Box>
                  </Box>
                ))}
              </Box>
            ))}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{
        p: 2,
        borderTop: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
        display: 'flex',
        alignItems: 'center'
      }}>
        <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%', display: 'flex' }}>
          <TextField
            autoFocus
            fullWidth
            id="comment"
            placeholder={pendingStatus
              ? `Type your comment for changing status to ${pendingStatus}...`
              : "Type your comment..."}
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            onKeyDown={handleKeyDown}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton
                    size="small"
                    onClick={handleEmojiPickerOpen}
                    aria-label="emoji picker"
                  >
                    <EmojiEmotionsIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    onClick={handleSubmit}
                    disabled={!newComment.trim()}
                    sx={{
                      color: newComment.trim() ? '#3A52A6' : isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                    }}
                  >
                    <SendIcon />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                borderRadius: 4,
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)',
              }
            }}
          />
        </Box>
      </DialogActions>

      {/* Emoji Picker Popover */}
      <Popover
        open={Boolean(emojiPickerAnchorEl)}
        anchorEl={emojiPickerAnchorEl}
        onClose={handleEmojiPickerClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'start',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'start',
        }}
        sx={{
          '& .MuiPopover-paper': {
            padding: 1,
            backgroundColor: isDarkMode ? '#333' : '#fff',
            maxWidth: 320
          }
        }}
      >
        <Grid container spacing={0.5}>
          {commonEmojis.map((emoji, index) => (
            <Grid item key={index}>
              <IconButton
                onClick={() => handleEmojiClick(emoji)}
                size="small"
                sx={{
                  minWidth: '36px',
                  height: '36px',
                  fontSize: '1.2rem',
                  '&:hover': {
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                  }
                }}
              >
                {emoji}
              </IconButton>
            </Grid>
          ))}
        </Grid>
      </Popover>
    </Dialog>
  );
};

export default CommentsModal;
