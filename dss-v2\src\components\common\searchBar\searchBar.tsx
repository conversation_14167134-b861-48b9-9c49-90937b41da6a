import React, { useState, KeyboardEvent, ChangeEvent, ReactNode } from "react";
import {
  Popover,
  FormControl,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Box,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import {
  SearchContainer,
  SearchPaper,
  SearchInputBase,
  SearchIconButton,
  FilterIconButton,
  FilterPopoverBox,
} from "@/components/common/searchBar/searchBar.style";

interface ISearchBarProps {
  onSearch?: (searchTerm: string, filters: string[]) => void;
  placeholder?: string;
  actionButton?: ReactNode;
  filterOptions?: { label: string; value: string }[];
  value?: string;
  onChange?: (e: ChangeEvent<HTMLInputElement>) => void;
  onClear?: () => void;
  sx?: any;
}

export const SearchBar: React.FC<ISearchBarProps> = ({
  onSearch = () => {},
  placeholder = "Search...",
  actionButton,
  filterOptions = [],
  value,
  onChange,
  onClear,
  sx
}) => {
  const [searchTerm, setSearchTerm] = useState<string>(value || "");
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  // Initialize with all filter options selected by default
  const [selectedFilters, setSelectedFilters] = useState<string[]>(
    filterOptions?.map(option => option.value) || []
  );

  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setAnchorEl(null);
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilters((prev) => {
      if (prev.includes(value)) {
        return prev.filter((item) => item !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  const handleSearchClick = () => {
    onSearch(searchTerm, selectedFilters);
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearchClick();
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    if (onChange) {
      // If onChange prop is provided, use it
      onChange(e);
    } else {
      // Otherwise, use internal state
      setSearchTerm(newValue);
    }

    // If the input is cleared (empty), trigger search to show all data
    if (newValue === "") {
      if (onClear) {
        onClear();
      } else {
        onSearch("", selectedFilters);
      }
    }
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 2, ...sx }}>
      <SearchContainer>
        <SearchPaper
          onSubmit={(e: React.FormEvent) => {
            e.preventDefault();
          }}
        >
          <SearchInputBase
            placeholder={placeholder}
            value={value !== undefined ? value : searchTerm}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
          />
          {filterOptions.length > 0 && (
            <FilterIconButton onClick={handleFilterClick} size="small">
              <FilterListIcon />
            </FilterIconButton>
          )}
          <SearchIconButton onClick={handleSearchClick} size="small">
            <SearchIcon />
          </SearchIconButton>
        </SearchPaper>

        <Popover
          open={Boolean(anchorEl)}
          anchorEl={anchorEl}
          onClose={handleFilterClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          <FilterPopoverBox>
            <FormControl component="fieldset">
              <FormGroup>
                {filterOptions.map(
                  (option: {
                    value: React.Key | null | undefined;
                    label:
                     string
                  }) => (
                    <FormControlLabel
                      key={option.value}
                      control={
                        <Checkbox
                          checked={selectedFilters.includes(option.value as string)}
                          onChange={() => handleFilterChange(option.value as string)}
                          size="small"
                        />
                      }
                      label={option.label}
                    />
                  )
                )}
              </FormGroup>
            </FormControl>
          </FilterPopoverBox>
        </Popover>
      </SearchContainer>
      {actionButton}
    </Box>
  );
};

export default SearchBar;
