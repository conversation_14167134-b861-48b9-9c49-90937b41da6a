import React, { useState, useEffect } from 'react';
import {
  Select,
  MenuItem,
  FormControl,
  SelectChangeEvent,
  IconButton,
  Box,
  Tooltip
} from '@mui/material';
import CommentsModal from './commentsModal';
import { LocalStorageEnum } from '@/enum';
import { StorageHelper } from '@/utills';
import { IComment } from '@/interfaces/comment.interface';
import ChatIcon from '@mui/icons-material/Chat';
import { v4 as uuidv4 } from 'uuid';

interface AdminStatusDropdownProps {
  status?: string;
  onStatusChange: (status: string, comment?: string) => void;
  value?: string;
  index?: number;
  itemId?: string; // Unique identifier for the item
}

export const AdminStatusDropdown: React.FC<AdminStatusDropdownProps> = ({
  status,
  onStatusChange,
  value,
  index,
  itemId = 'unknown'
}) => {
  // Use the provided status or value, or default to 'safe'
  const initialStatus = status || value || 'safe';
  const [currentStatus, setCurrentStatus] = useState<string>(initialStatus);
  const [pendingStatus, setPendingStatus] = useState<string | null>(null);
  const [commentsHistoryModalOpen, setCommentsHistoryModalOpen] = useState(false);
  const [comments, setComments] = useState<IComment[]>([]);

  // Load comments from localStorage
  useEffect(() => {
    loadComments();
  }, [itemId]);

  // Update when status/value props change
  useEffect(() => {
    if (status || value) {
      setCurrentStatus(status || value || 'safe');
    }
  }, [status, value]);

  const loadComments = () => {
    const savedCommentsStr = StorageHelper.getLocalStorage(LocalStorageEnum.COMMENT_HISTORY);
    if (savedCommentsStr) {
      try {
        const allComments = JSON.parse(savedCommentsStr);
        if (allComments[itemId]) {
          setComments(allComments[itemId]);
        } else {
          setComments([]);
        }
      } catch (e) {
        console.error('Error parsing saved comments:', e);
        setComments([]);
      }
    } else {
      setComments([]);
    }
  };

  const saveComment = (text: string, status: string) => {
    const now = new Date();
    const formattedDate = `${now.getMonth() + 1}-${now.getDate()}-${now.getFullYear()}`;
    const formattedTime = now.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    const newComment: IComment = {
      id: uuidv4(),
      text,
      timestamp: `${formattedDate} ${formattedTime}`,
      status,
      user: 'Admin'
    };

    const updatedComments = [...comments, newComment];
    setComments(updatedComments);

    // Save to localStorage
    const savedCommentsStr = StorageHelper.getLocalStorage(LocalStorageEnum.COMMENT_HISTORY);
    let allComments = {};

    if (savedCommentsStr) {
      try {
        allComments = JSON.parse(savedCommentsStr);
      } catch (e) {
        console.error('Error parsing saved comments:', e);
      }
    }

    allComments = {
      ...allComments,
      [itemId]: updatedComments
    };

    StorageHelper.setLocalStorage(
      LocalStorageEnum.COMMENT_HISTORY,
      JSON.stringify(allComments)
    );
  };

  const handleChange = (event: SelectChangeEvent<string>) => {
    const newStatus = event.target.value;

    // If status is changing, open comments history modal with pending status
    if (newStatus !== currentStatus) {
      setPendingStatus(newStatus);
      setCommentsHistoryModalOpen(true);
    }
  };

  const handleCommentSubmit = (comment: string) => {
    // If there's a pending status change, apply it with the comment
    if (pendingStatus && comment.trim()) {
      setCurrentStatus(pendingStatus);

      // Save the comment to history with the new status
      saveComment(comment, pendingStatus);

      // Call the parent component's callback - the parent will handle saving to localStorage
      onStatusChange(pendingStatus, comment);

      setPendingStatus(null);
    } else if (comment.trim()) {
      // Just a regular comment without status change
      saveComment(comment, currentStatus);
    }
  };

  const handleCommentsHistoryOpen = () => {
    loadComments();
    setCommentsHistoryModalOpen(true);
  };

  const handleCommentsHistoryClose = () => {
    setCommentsHistoryModalOpen(false);
  };

  const handleNewCommentSubmit = (comment: string) => {
    if (comment.trim()) {
      saveComment(comment, currentStatus);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'safe':
        return '#2E7D32'; // Darker, less bright green
      case 'unsafe':
        return '#C62828'; // Darker, less bright red
      default:
        return '#9e9e9e';
    }
  };

  const statusColor = getStatusColor(currentStatus);

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <FormControl size="small" sx={{ minWidth: 100 }}>
          <Select
            value={currentStatus}
            onChange={handleChange}
            displayEmpty
            variant="outlined"
            sx={{
              height: '28px',
              fontSize: '0.75rem',
              color: 'white',
              backgroundColor: statusColor,
              borderRadius: '4px',
              '.MuiOutlinedInput-notchedOutline': {
                borderColor: statusColor,
                borderWidth: 0,
              },
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: statusColor,
                borderWidth: 0,
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: statusColor,
                borderWidth: 0,
              },
              '& .MuiSelect-select': {
                paddingTop: '2px',
                paddingBottom: '2px',
                paddingLeft: '10px',
                paddingRight: '24px',
                textTransform: 'none',
              },
              '& .MuiSvgIcon-root': {
                color: 'white',
                right: '4px',
              },
              '&:hover': {
                backgroundColor: statusColor,
                opacity: 0.9,
              }
            }}
            MenuProps={{
              PaperProps: {
                sx: {
                  maxHeight: 200,
                  mt: 0.5,
                  '& .MuiMenuItem-root': {
                    fontSize: '0.75rem',
                    minHeight: '32px',
                  },
                  '& .MuiMenuItem-root.Mui-selected': {
                    backgroundColor: '#f0f0f0',
                  },
                  '& .MuiMenuItem-root:hover': {
                    backgroundColor: '#f5f5f5',
                  },
                },
              },
            }}
          >
            <MenuItem
              value="safe"
              sx={{
                fontSize: '0.75rem',
                '&.Mui-selected': {
                  backgroundColor: '#e8f5e9',
                },
                '&:hover': {
                  backgroundColor: '#e8f5e9',
                  opacity: 0.8,
                },
              }}
            >
              Safe
            </MenuItem>
            <MenuItem
              value="unsafe"
              sx={{
                fontSize: '0.75rem',
                '&.Mui-selected': {
                  backgroundColor: '#ffebee',
                },
                '&:hover': {
                  backgroundColor: '#ffebee',
                  opacity: 0.8,
                },
              }}
            >
              Unsafe
            </MenuItem>
          </Select>
        </FormControl>

        {/* Comments History Button */}
        <Tooltip title="View Comments History">
          <IconButton
            size="small"
            onClick={handleCommentsHistoryOpen}
            sx={{
              ml: 1,
              color: comments.length > 0 ? '#3A52A6' : 'rgba(0, 0, 0, 0.54)',
              '&:hover': {
                backgroundColor: 'rgba(58, 82, 166, 0.04)'
              }
            }}
          >
            <ChatIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Comments History Modal */}
      <CommentsModal
        open={commentsHistoryModalOpen}
        onClose={handleCommentsHistoryClose}
        onSubmit={handleCommentSubmit}
        comments={comments}
        itemId={itemId}
        pendingStatus={pendingStatus}
        currentStatus={currentStatus}
      />
    </>
  );
};

export default AdminStatusDropdown;
