"use client";

import { useState, useEffect } from "react";
import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay } from "@/components/common/breadcrumb";

// Mock data for the error details
const mockErrorData = {
  id: '1',
  user: 'Anonymous',
  path: '/media/uploads/customerLogo/IAF_logo_yGO4k7F.jpg',
  method: 'GET',
  errorType: 'Http404',
  errorMessage: 'Not Found',
  timestamp: '14-05-2025 11:55 AM',
  traceback: `HTTP Error

HTTP 404 Not Found

The requested resource was not found on the server.

django.http.Http404: "/media/uploads/customerLogo/IAF_logo_yGO4k7F.jpg" does not exist`
};

interface IErrorDetailsController {
  getters: {
    loading: boolean;
    errorData: any;
    breadcrumbs: IBreadcrumbDisplay[];
  };
  handlers: {};
}

/**
 * Error Details Controller
 * @param {string} errorId - The ID of the error to display
 * @return {IErrorDetailsController} Controller with getters and handlers
 */
export const ErrorDetailsController = (errorId: string): IErrorDetailsController => {
  const [loading, setLoading] = useState(true);
  const [errorData, setErrorData] = useState<any>(null);

  // In a real application, you would fetch the error data based on the errorId
  useEffect(() => {
    // Simulate API call
    const fetchErrorData = async () => {
      setLoading(true);
      try {
        // In a real app, you would fetch data from an API
        // const response = await fetch(`/api/errors/${errorId}`);
        // const data = await response.json();

        // For now, we'll use mock data
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update the mock data with the provided ID
        const data = { ...mockErrorData, id: errorId };
        setErrorData(data);
      } catch (error) {
        console.error("Error fetching error data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchErrorData();
  }, [errorId]);

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Logs",
      path: "", // No direct path for Logs category
      forwardParam: false,
    },
    {
      name: "Error Logs",
      path: RoutePathEnum.ERROR_LOGS,
      forwardParam: false,
      clickable: true,
    },
    {
      name: `Error Details #${errorId}`,
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  return {
    getters: {
      loading,
      errorData,
      breadcrumbs,
    },
    handlers: {},
  };
};

export default ErrorDetailsController;
