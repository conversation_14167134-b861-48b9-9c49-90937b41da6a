import React from 'react';
import { Box, Stack } from '@mui/material';
import { SearchBar as CommonSearchBar } from '@/components/common';
import { ActiveColumn } from '@/components/common/activeColumn/activeColumn';

interface SearchBarProps {
  onSearch: (query: string, filters: string[]) => void;
  columnOptions: { label: string; value: string }[];
  visibleColumns: string[];
  onColumnVisibilityChange: (visibleColumns: string[]) => void;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  columnOptions,
  visibleColumns,
  onColumnVisibilityChange
}) => {
  const FILTER_OPTIONS = [
    { label: 'User', value: 'user' },
    { label: 'Path', value: 'path' },
    { label: 'Method', value: 'method' },
    { label: 'Exception Type', value: 'exceptionType' },
    { label: 'Exception Message', value: 'exceptionMessage' },
    { label: 'Timestamp', value: 'timestamp' },
  ];

  return (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <CommonSearchBar
        onSearch={onSearch}
        placeholder="Search exception logs..."
        filterOptions={FILTER_OPTIONS}
        actionButton={
          <Stack direction="row" spacing={1} alignItems="center">
            <ActiveColumn
              columnOptions={columnOptions}
              visibleColumns={visibleColumns}
              onColumnVisibilityChange={onColumnVisibilityChange}
              variant="popover"
            />
          </Stack>
        }
      />
    </Box>
  );
};

export default SearchBar;
