"use client";

import React, { useState } from "react";
import { Box, Grid, Typography, IconButton, Paper, useTheme } from "@mui/material";
import { PageHeader } from "@/components/common/pageHeader/pageHeader";
import { DashboardController } from "./dashboard.controller";
import StatCard from "./components/StatCard";
import ChartCard from "./components/ChartCard";
import EmailBarChart from "./components/EmailBarChart";
import CircularProgressCard from "./components/CircularProgressCard";
import EnhancedSystemOverviewCard from "./components/EnhancedSystemOverviewCard";
import ActivityTimelineCard from "./components/ActivityTimelineCard";
import FilterOptionsModal from "./components/FilterOptionsModal";
import { DateTime } from "luxon";

// Icons
import EmailIcon from "@mui/icons-material/Email";
import SecurityIcon from "@mui/icons-material/Security";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import WarningIcon from "@mui/icons-material/Warning";
import FlagIcon from "@mui/icons-material/Flag";
import FilterListIcon from "@mui/icons-material/FilterList";
import ErrorIcon from "@mui/icons-material/Error";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import CloseIcon from "@mui/icons-material/Close";
import QuarantineIcon from "@mui/icons-material/Coronavirus";
import BugReportIcon from "@mui/icons-material/BugReport";
import DatabaseIcon from "@mui/icons-material/Storage";
import ShieldIcon from "@mui/icons-material/Shield";
import PeopleIcon from "@mui/icons-material/People";

const Page = () => {
  const { getters } = DashboardController();
  const { breadcrumbs } = getters;
  const theme = useTheme();

  // State for filter modal
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [startDate, setStartDate] = useState<DateTime | null>(null);
  const [endDate, setEndDate] = useState<DateTime | null>(null);

  // State for dashboard data
  const [isDataRefreshing, setIsDataRefreshing] = useState(false);
  const [emailsData, setEmailsData] = useState([
    { label: 'Mon', value: 50 },
    { label: 'Tue', value: 35 },
    { label: 'Wed', value: 217 },
    { label: 'Thu', value: 78 },
    { label: 'Fri', value: 52 },
    { label: 'Sat', value: 22 },
    { label: 'Sun', value: 146 },
  ]);
  const [chartLabels] = useState(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul']);
  const [sandboxData, setSandboxData] = useState([900, 1500, 2200, 1800, 1600, 1700, 1800]);
  const [cibCompletedData, setCibCompletedData] = useState([500, 700, 900, 800, 600, 700, 750]);
  const [pendingCibData, setPendingCibData] = useState([800, 1000, 1200, 1100, 900, 1000, 1100]);
  const [phishingData, setPhishingData] = useState([900, 1100, 1300, 1200, 1000, 1100, 1200]);

  // Stats card data
  const [totalAttachmentEmails, setTotalAttachmentEmails] = useState(740);
  const [sandboxTesting, setSandboxTesting] = useState(2993);
  const [cibCompleted, setCibCompleted] = useState(751);
  const [pendingCibSubmissions, setPendingCibSubmissions] = useState(1215);

  // Agent status and license data
  const [agentUpToDate, setAgentUpToDate] = useState(4);
  const [agentOutdated, setAgentOutdated] = useState(2);
  const [licenseAvailable, setLicenseAvailable] = useState(528);
  const [licenseAllocated, setLicenseAllocated] = useState(27);

  // System overview data
  const [phishingMailsTotal, setPhishingMailsTotal] = useState(702);
  const [phishingMailsMalicious, setPhishingMailsMalicious] = useState(124);
  const [disputesPending, setDisputesPending] = useState(304);
  const [disputesResolved, setDisputesResolved] = useState(874);
  const [quarantineTotal, setQuarantineTotal] = useState(156);
  const [quarantineClean, setQuarantineClean] = useState(102);
  const [errorLogs, setErrorLogs] = useState(1568);
  const [exceptionLogs, setExceptionLogs] = useState(1024);

  // Helper function to generate random data
  const generateRandomData = () => {
    // Generate random values for email data
    const newEmailsData = [
      { label: 'Mon', value: Math.floor(Math.random() * 200) + 20 },
      { label: 'Tue', value: Math.floor(Math.random() * 200) + 20 },
      { label: 'Wed', value: Math.floor(Math.random() * 300) + 50 },
      { label: 'Thu', value: Math.floor(Math.random() * 200) + 20 },
      { label: 'Fri', value: Math.floor(Math.random() * 200) + 20 },
      { label: 'Sat', value: Math.floor(Math.random() * 100) + 10 },
      { label: 'Sun', value: Math.floor(Math.random() * 200) + 20 },
    ];

    // Generate random values for chart data
    const newSandboxData = chartLabels.map(() => Math.floor(Math.random() * 1500) + 500);
    const newCibCompletedData = chartLabels.map(() => Math.floor(Math.random() * 800) + 300);
    const newPendingCibData = chartLabels.map(() => Math.floor(Math.random() * 1000) + 500);
    const newPhishingData = chartLabels.map(() => Math.floor(Math.random() * 1000) + 500);

    // Generate random values for stats cards
    const newTotalAttachmentEmails = Math.floor(Math.random() * 500) + 500;
    const newSandboxTesting = Math.floor(Math.random() * 2000) + 1500;
    const newCibCompleted = Math.floor(Math.random() * 500) + 500;
    const newPendingCibSubmissions = Math.floor(Math.random() * 1000) + 500;

    // Generate random values for agent status and license
    const newAgentUpToDate = Math.floor(Math.random() * 5) + 2;
    const newAgentOutdated = Math.floor(Math.random() * 3) + 1;
    const newLicenseAvailable = Math.floor(Math.random() * 200) + 400;
    const newLicenseAllocated = Math.floor(Math.random() * 50) + 10;

    // Generate random values for system overview
    const newPhishingMailsTotal = Math.floor(Math.random() * 300) + 500;
    const newPhishingMailsMalicious = Math.floor(Math.random() * 100) + 50;
    const newDisputesPending = Math.floor(Math.random() * 200) + 200;
    const newDisputesResolved = Math.floor(Math.random() * 400) + 600;
    const newQuarantineTotal = Math.floor(Math.random() * 100) + 100;
    const newQuarantineClean = Math.floor(Math.random() * 80) + 50;
    const newErrorLogs = Math.floor(Math.random() * 1000) + 1000;
    const newExceptionLogs = Math.floor(Math.random() * 800) + 800;

    // Update all state values
    setEmailsData(newEmailsData);
    setSandboxData(newSandboxData);
    setCibCompletedData(newCibCompletedData);
    setPendingCibData(newPendingCibData);
    setPhishingData(newPhishingData);
    setTotalAttachmentEmails(newTotalAttachmentEmails);
    setSandboxTesting(newSandboxTesting);
    setCibCompleted(newCibCompleted);
    setPendingCibSubmissions(newPendingCibSubmissions);
    setAgentUpToDate(newAgentUpToDate);
    setAgentOutdated(newAgentOutdated);
    setLicenseAvailable(newLicenseAvailable);
    setLicenseAllocated(newLicenseAllocated);
    setPhishingMailsTotal(newPhishingMailsTotal);
    setPhishingMailsMalicious(newPhishingMailsMalicious);
    setDisputesPending(newDisputesPending);
    setDisputesResolved(newDisputesResolved);
    setQuarantineTotal(newQuarantineTotal);
    setQuarantineClean(newQuarantineClean);
    setErrorLogs(newErrorLogs);
    setExceptionLogs(newExceptionLogs);
  };

  // Filter modal handlers
  const handleOpenFilterModal = () => {
    setFilterModalOpen(true);
  };

  const handleCloseFilterModal = () => {
    setFilterModalOpen(false);
  };

  const handleApplyFilters = (newStartDate: DateTime | null, newEndDate: DateTime | null) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setFilterModalOpen(false);

    // Show loading state
    setIsDataRefreshing(true);

    // Simulate data loading with random data after a short delay
    setTimeout(() => {
      generateRandomData();
      setIsDataRefreshing(false);
    }, 800);

    console.log("Applying filters:", {
      startDate: newStartDate ? newStartDate.toFormat("dd-MM-yyyy") : "none",
      endDate: newEndDate ? newEndDate.toFormat("dd-MM-yyyy") : "none"
    });
  };

  return (
    <div>
      <PageHeader
        title="Dashboard"
        breadcrumbs={breadcrumbs}
        actions={
          <IconButton
            size="small"
            onClick={handleOpenFilterModal}
            sx={{
              border: '1px solid #e0e0e0',
              borderRadius: '4px',
              p: 1,
              position: 'relative',
              ...(startDate || endDate ? {
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 3,
                  right: 3,
                  width: 8,
                  height: 8,
                  backgroundColor: '#3A52A6',
                  borderRadius: '50%',
                }
              } : {})
            }}
          >
            <FilterListIcon fontSize="small" />
          </IconButton>
        }
      />

      {/* Filter Options Modal */}
      <FilterOptionsModal
        open={filterModalOpen}
        onClose={handleCloseFilterModal}
        onApply={handleApplyFilters}
        initialStartDate={startDate}
        initialEndDate={endDate}
      />

      <Box sx={{ mt: 3, px: 3 }}>
        {/* Active Filters Display */}
        {(startDate || endDate) && (
          <Box
            sx={{
              mb: 2,
              p: 1.5,
              borderRadius: 1,
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(58, 82, 166, 0.1)' : 'rgba(58, 82, 166, 0.05)',
              border: '1px solid',
              borderColor: theme.palette.mode === 'dark' ? 'rgba(58, 82, 166, 0.2)' : 'rgba(58, 82, 166, 0.15)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                <strong>Active Filters:</strong> {startDate ? `From ${startDate.toFormat('dd MMM yyyy')}` : ''}
                {endDate ? `${startDate ? ' to ' : 'Until '}${endDate.toFormat('dd MMM yyyy')}` : ''}
              </Typography>
              {isDataRefreshing && (
                <Box
                  sx={{
                    display: 'inline-flex',
                    ml: 2,
                    alignItems: 'center',
                    animation: 'pulse 1.5s infinite ease-in-out',
                    '@keyframes pulse': {
                      '0%': { opacity: 0.6 },
                      '50%': { opacity: 1 },
                      '100%': { opacity: 0.6 }
                    }
                  }}
                >
                  <Box
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: '#4CAF50',
                      mr: 1
                    }}
                  />
                  <Typography variant="caption" color="success.main">
                    Refreshing data...
                  </Typography>
                </Box>
              )}
            </Box>
            <IconButton
              size="small"
              onClick={() => {
                setStartDate(null);
                setEndDate(null);
                // Also refresh data when clearing filters
                setIsDataRefreshing(true);
                setTimeout(() => {
                  generateRandomData();
                  setIsDataRefreshing(false);
                }, 800);
              }}
              sx={{ ml: 1 }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        )}

        {/* Top Stats Cards */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Attachment Emails"
              value={totalAttachmentEmails}
              icon={<EmailIcon />}
              color="#4F6AE5"
              onClick={() => console.log("Clicked on Total Attachment Emails")}
              isRefreshing={isDataRefreshing}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Sandbox Testing"
              value={sandboxTesting}
              icon={<SecurityIcon />}
              color="#FF9800"
              onClick={() => console.log("Clicked on Sandbox Testing")}
              isRefreshing={isDataRefreshing}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="CIB Completed"
              value={cibCompleted}
              icon={<CheckCircleIcon />}
              color="#4CAF50"
              onClick={() => console.log("Clicked on CIB Completed")}
              isRefreshing={isDataRefreshing}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Pending CIB Submissions"
              value={pendingCibSubmissions}
              icon={<WarningIcon />}
              color="#F44336"
              onClick={() => console.log("Clicked on Pending CIB Submissions")}
              isRefreshing={isDataRefreshing}
            />
          </Grid>
        </Grid>

        {/* Charts Section */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={12} lg={6}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
              height: '100%'
            }}>
              <EmailBarChart
                title="Total Attachment Emails (Last 7 working days)"
                data={emailsData}
              />
            </Box>
          </Grid>
          <Grid item xs={12} lg={6}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                height: '100%',
                minHeight: '400px',
                display: 'flex',
                flexDirection: 'column',
                border: '1px solid #e0e0e0',
                borderRadius: '8px',
                transition: 'all 0.3s ease',
                transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
                opacity: isDataRefreshing ? 0.8 : 1,
                '&:hover': {
                  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.08)',
                }
              }}
            >
              <Typography variant="subtitle1" fontWeight="medium" sx={{ mb: 2 }}>
                Trend Analysis
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <ChartCard
                    title="Sandbox Testing"
                    color="#FF9800"
                    data={sandboxData}
                    labels={chartLabels}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <ChartCard
                    title="CIB Completed"
                    color="#4CAF50"
                    data={cibCompletedData}
                    labels={chartLabels}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <ChartCard
                    title="Pending CIB"
                    color="#F44336"
                    data={pendingCibData}
                    labels={chartLabels}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <ChartCard
                    title="Phishing Status"
                    color="#4F6AE5"
                    data={phishingData}
                    labels={chartLabels}
                  />
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>

        {/* System Status Overview */}
        <Box
          sx={{
            mb: 2.5,
            mt: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 4,
                height: 24,
                backgroundColor: theme.palette.warning.main,
                borderRadius: 1,
                mr: 1.5
              }}
            />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                color: theme.palette.mode === 'dark' ? 'white' : 'text.primary',
                letterSpacing: '0.02em'
              }}
            >
              System Status Overview
            </Typography>
          </Box>
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.04)',
              px: 1.5,
              py: 0.5,
              borderRadius: 10,
              fontWeight: 'medium'
            }}
          >
            Real-time monitoring
          </Typography>
        </Box>

        {/* Agent Status and License Distribution */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <CircularProgressCard
                title="Agent Status"
                percentage={Math.round((agentUpToDate / (agentUpToDate + agentOutdated)) * 100)}
                color="#FF9800"
                items={[
                  { label: "Up-to-date", value: agentUpToDate, color: "#2E7D32" }, // Darker, more solid green
                  { label: "Outdated", value: agentOutdated, color: "#E65100" },   // Darker, more solid orange
                ]}
                totalLabel="Total Devices"
                totalValue={agentUpToDate + agentOutdated}
                description="Overview of agent installation status across all devices. Up-to-date agents have the latest security features and patches."
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <CircularProgressCard
                title="License Distribution"
                percentage={Math.round((licenseAvailable / (licenseAvailable + licenseAllocated)) * 100)}
                color="#2E7D32" // Darker, more solid green
                items={[
                  { label: "Available", value: licenseAvailable, color: "#2E7D32" }, // Darker, more solid green
                  { label: "Allocated", value: licenseAllocated, color: "#E65100" },  // Darker, more solid orange
                ]}
                totalLabel="Total Licenses"
                totalValue={licenseAvailable + licenseAllocated}
                description="Current distribution of software licenses across the organization. Shows available licenses that can be allocated to new devices."
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
        </Grid>

        {/* System Overview */}
        <Box
          sx={{
            mb: 2.5,
            mt: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 4,
                height: 24,
                backgroundColor: theme.palette.primary.main,
                borderRadius: 1,
                mr: 1.5
              }}
            />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                color: theme.palette.mode === 'dark' ? 'white' : 'text.primary',
                letterSpacing: '0.02em'
              }}
            >
              System Overview
            </Typography>
          </Box>
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.04)',
              px: 1.5,
              py: 0.5,
              borderRadius: 10,
              fontWeight: 'medium'
            }}
          >
            Last updated: {new Date().toLocaleDateString()}
          </Typography>
        </Box>





        {/* System Overview Cards */}
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} lg={3}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <EnhancedSystemOverviewCard
                title="Phishing Mails"
                icon={<FlagIcon />}
                color="#4F6AE5"
                description="Overview of all phishing emails detected by the system, including malicious content statistics and current system status."
                items={[
                  {
                    title: "Total Mails",
                    value: phishingMailsTotal.toString(),
                    trend: "up",
                    trendValue: 12,
                    chartData: [
                      phishingMailsTotal - Math.floor(Math.random() * 250) - 250,
                      phishingMailsTotal - Math.floor(Math.random() * 200) - 180,
                      phishingMailsTotal - Math.floor(Math.random() * 150) - 120,
                      phishingMailsTotal - Math.floor(Math.random() * 100) - 80,
                      phishingMailsTotal
                    ],
                    onClick: () => console.log("Navigating to Phishing Mails details")
                  },
                  {
                    title: "Total Malicious",
                    value: phishingMailsMalicious.toString(),
                    trend: "down",
                    trendValue: 5,
                    chartData: [
                      phishingMailsMalicious + Math.floor(Math.random() * 30) + 20,
                      phishingMailsMalicious + Math.floor(Math.random() * 20) + 15,
                      phishingMailsMalicious + Math.floor(Math.random() * 15) + 10,
                      phishingMailsMalicious + Math.floor(Math.random() * 10) + 5,
                      phishingMailsMalicious
                    ],
                    color: "#F44336"
                  },
                  {
                    title: "Status",
                    value: "Enabled",
                    color: "#4CAF50"
                  },
                ]}
                onViewMore={() => console.log("Navigating to Phishing page")}
                onCardClick={() => console.log("Card clicked - Phishing Mails")}
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <EnhancedSystemOverviewCard
                title="Disputes"
                icon={<WarningAmberIcon />}
                color="#FF9800"
                description="Track all user disputes about email classifications, including pending cases that need review and resolved cases."
                items={[
                  {
                    title: "Pending",
                    value: disputesPending.toString(),
                    trend: "up",
                    trendValue: 8,
                    chartData: [
                      disputesPending - Math.floor(Math.random() * 60) - 50,
                      disputesPending - Math.floor(Math.random() * 40) - 30,
                      disputesPending - Math.floor(Math.random() * 20) - 15,
                      disputesPending - Math.floor(Math.random() * 10) - 5,
                      disputesPending
                    ],
                    color: "#FF9800",
                    onClick: () => console.log("Navigating to Pending Disputes")
                  },
                  {
                    title: "Resolved",
                    value: disputesResolved.toString(),
                    trend: "up",
                    trendValue: 15,
                    chartData: [
                      disputesResolved - Math.floor(Math.random() * 150) - 150,
                      disputesResolved - Math.floor(Math.random() * 100) - 100,
                      disputesResolved - Math.floor(Math.random() * 70) - 60,
                      disputesResolved - Math.floor(Math.random() * 30) - 20,
                      disputesResolved
                    ],
                    color: "#4CAF50",
                    onClick: () => console.log("Navigating to Resolved Disputes")
                  },
                ]}
                tabs={["Pending", "Resolved"]}
                onViewMore={() => console.log("Navigating to Disputes page")}
                onCardClick={() => console.log("Card clicked - Disputes")}
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <EnhancedSystemOverviewCard
                title="Quarantine"
                icon={<QuarantineIcon />}
                color="#673AB7"
                description="Monitor emails placed in quarantine due to suspicious content or attachments, with statistics on clean vs. potentially harmful emails."
                items={[
                  {
                    title: "Total Emails",
                    value: quarantineTotal.toString(),
                    trend: "down",
                    trendValue: 3,
                    chartData: [
                      quarantineTotal + Math.floor(Math.random() * 30) + 20,
                      quarantineTotal + Math.floor(Math.random() * 20) + 15,
                      quarantineTotal + Math.floor(Math.random() * 10) + 5,
                      quarantineTotal + Math.floor(Math.random() * 5) + 2,
                      quarantineTotal
                    ],
                    onClick: () => console.log("Navigating to Quarantine details")
                  },
                  {
                    title: "Clean Emails",
                    value: quarantineClean.toString(),
                    trend: "flat",
                    trendValue: 0,
                    chartData: [
                      quarantineClean - Math.floor(Math.random() * 5) - 2,
                      quarantineClean + Math.floor(Math.random() * 5) + 3,
                      quarantineClean - Math.floor(Math.random() * 5) - 4,
                      quarantineClean + Math.floor(Math.random() * 5) + 0,
                      quarantineClean
                    ],
                    color: "#4CAF50"
                  },
                  {
                    title: "Last Updated",
                    value: "10/08/2023"
                  },
                ]}
                onViewMore={() => console.log("Navigating to Quarantine page")}
                onCardClick={() => console.log("Card clicked - Quarantine")}
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} lg={3}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <EnhancedSystemOverviewCard
                title="System Logs"
                icon={<BugReportIcon />}
                color="#F44336"
                description="View system logs including errors and exceptions to monitor system health and troubleshoot issues."
                items={[
                  {
                    title: "Error Logs",
                    value: errorLogs.toLocaleString(),
                    trend: "up",
                    trendValue: 22,
                    chartData: [
                      errorLogs - Math.floor(Math.random() * 400) - 350,
                      errorLogs - Math.floor(Math.random() * 300) - 250,
                      errorLogs - Math.floor(Math.random() * 200) - 150,
                      errorLogs - Math.floor(Math.random() * 100) - 50,
                      errorLogs
                    ],
                    color: "#F44336",
                    onClick: () => console.log("Navigating to Error Logs")
                  },
                  {
                    title: "Exception Logs",
                    value: exceptionLogs.toLocaleString(),
                    trend: "up",
                    trendValue: 17,
                    chartData: [
                      exceptionLogs - Math.floor(Math.random() * 200) - 170,
                      exceptionLogs - Math.floor(Math.random() * 150) - 120,
                      exceptionLogs - Math.floor(Math.random() * 100) - 70,
                      exceptionLogs - Math.floor(Math.random() * 50) - 20,
                      exceptionLogs
                    ],
                    color: "#FF9800",
                    onClick: () => console.log("Navigating to Exception Logs")
                  },
                ]}
                tabs={["Error Logs", "Exceptions"]}
                onViewMore={() => console.log("Navigating to Logs page")}
                onCardClick={() => console.log("Card clicked - System Logs")}
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
        </Grid>
          </Grid>
        </Grid>

        {/* Security Monitoring */}
        <Box
          sx={{
            mb: 2.5,
            mt: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box
              sx={{
                width: 4,
                height: 24,
                backgroundColor: theme.palette.error.main,
                borderRadius: 1,
                mr: 1.5
              }}
            />
            <Typography
              variant="h6"
              sx={{
                fontWeight: 'bold',
                color: theme.palette.mode === 'dark' ? 'white' : 'text.primary',
                letterSpacing: '0.02em'
              }}
            >
              Security Monitoring
            </Typography>
          </Box>
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.04)',
              px: 1.5,
              py: 0.5,
              borderRadius: 10,
              fontWeight: 'medium'
            }}
          >
            Active monitoring
          </Typography>
        </Box>

        {/* Security Monitoring Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <EnhancedSystemOverviewCard
              title="Rogue DB"
              icon={<DatabaseIcon />}
              color="#2196F3"
              description="Overview of malicious URLs, domains, and email addresses tracked in the system's rogue database."
              items={[
                {
                  title: "Malicious URLs",
                  value: "156",
                  trend: "up",
                  trendValue: 8,
                  chartData: [120, 130, 142, 148, 156],
                  onClick: () => console.log("Navigating to Rogue DB URL")
                },
                {
                  title: "Suspicious Domains",
                  value: "42",
                  trend: "up",
                  trendValue: 5,
                  chartData: [32, 35, 38, 40, 42],
                  color: "#FF9800"
                },
                {
                  title: "Blocked Emails",
                  value: "89",
                  trend: "down",
                  trendValue: 3,
                  chartData: [95, 93, 91, 90, 89],
                  color: "#F44336"
                },
              ]}
              onViewMore={() => console.log("Navigating to Rogue DB page")}
              onCardClick={() => console.log("Card clicked - Rogue DB")}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <EnhancedSystemOverviewCard
              title="Sandbox Activity"
              icon={<ShieldIcon />}
              color="#9C27B0"
              description="Overview of sandbox testing activities for suspicious files and attachments."
              items={[
                {
                  title: "Running Tests",
                  value: "18",
                  trend: "up",
                  trendValue: 12,
                  chartData: [10, 12, 15, 16, 18],
                  onClick: () => console.log("Navigating to Running Sandbox")
                },
                {
                  title: "Completed Tests",
                  value: "243",
                  trend: "up",
                  trendValue: 7,
                  chartData: [210, 220, 230, 235, 243],
                  color: "#4CAF50"
                },
                {
                  title: "Detected Threats",
                  value: "37",
                  trend: "down",
                  trendValue: 4,
                  chartData: [42, 40, 39, 38, 37],
                  color: "#F44336"
                },
              ]}
              onViewMore={() => console.log("Navigating to Sandbox page")}
              onCardClick={() => console.log("Card clicked - Sandbox Activity")}
            />
          </Grid>
        </Grid>

        {/* User Activity & Security Threats */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} md={6}>
            <EnhancedSystemOverviewCard
              title="User Activity"
              icon={<PeopleIcon />}
              color="#00BCD4"
              description="Overview of recent user activities and interactions with the system."
              items={[
                {
                  title: "Active Users",
                  value: "28",
                  trend: "up",
                  trendValue: 15,
                  chartData: [18, 20, 22, 25, 28],
                  onClick: () => console.log("Navigating to User Activity")
                },
                {
                  title: "New Reports",
                  value: "47",
                  trend: "up",
                  trendValue: 12,
                  chartData: [30, 35, 40, 43, 47],
                  color: "#4CAF50"
                },
                {
                  title: "Pending Actions",
                  value: "13",
                  trend: "down",
                  trendValue: 8,
                  chartData: [18, 16, 15, 14, 13],
                  color: "#FF9800"
                },
              ]}
              onViewMore={() => console.log("Navigating to User Activity page")}
              onCardClick={() => console.log("Card clicked - User Activity")}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <EnhancedSystemOverviewCard
              title="Security Threats"
              icon={<SecurityIcon />}
              color="#F44336"
              description="Overview of detected security threats and vulnerabilities in the system."
              items={[
                {
                  title: "Critical Threats",
                  value: "7",
                  trend: "down",
                  trendValue: 22,
                  chartData: [12, 10, 9, 8, 7],
                  onClick: () => console.log("Navigating to Critical Threats")
                },
                {
                  title: "Medium Threats",
                  value: "23",
                  trend: "down",
                  trendValue: 5,
                  chartData: [25, 24, 24, 23, 23],
                  color: "#FF9800"
                },
                {
                  title: "Resolved Threats",
                  value: "142",
                  trend: "up",
                  trendValue: 18,
                  chartData: [110, 120, 130, 135, 142],
                  color: "#4CAF50"
                },
              ]}
              tabs={["Critical", "Medium", "Low"]}
              onViewMore={() => console.log("Navigating to Security Threats page")}
              onCardClick={() => console.log("Card clicked - Security Threats")}
            />
          </Grid>
        </Grid>

        {/* Activity Timeline */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12}>
            <Box sx={{
              position: 'relative',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1
            }}>
              <ActivityTimelineCard
                title="Recent Activities"
                activities={[
                  {
                    id: 1,
                    type: 'alert',
                    title: 'Critical Security Alert',
                    description: isDataRefreshing ? 'Analyzing suspicious login patterns from multiple IP addresses...' : 'Suspicious login attempt detected from unknown IP address.',
                    user: 'System',
                    time: isDataRefreshing ? '2 min ago' : '10 min ago',
                    status: 'error'
                  },
                  {
                    id: 2,
                    type: 'report',
                    title: 'New Phishing Report',
                    description: isDataRefreshing ? 'Multiple users reported similar phishing attempts targeting financial data.' : 'User reported a suspicious email with malicious attachment.',
                    user: isDataRefreshing ? 'Sarah Johnson' : 'John Doe',
                    time: isDataRefreshing ? '15 min ago' : '45 min ago',
                    status: 'warning'
                  },
                  {
                    id: 3,
                    type: 'action',
                    title: 'Quarantine Action',
                    description: isDataRefreshing ? 'Batch of emails with suspicious JavaScript attachments quarantined.' : 'Email with suspicious attachment was automatically quarantined.',
                    user: 'System',
                    time: isDataRefreshing ? '30 min ago' : '1 hour ago',
                    status: 'success'
                  },
                  {
                    id: 4,
                    type: 'login',
                    title: 'Admin Login',
                    description: isDataRefreshing ? 'Security administrator accessed threat management console.' : 'Administrator logged in to the dashboard.',
                    user: isDataRefreshing ? 'Security Admin' : 'Admin User',
                    time: isDataRefreshing ? '1 hour ago' : '2 hours ago',
                    status: 'info'
                  },
                  {
                    id: 5,
                    type: 'update',
                    title: 'System Update',
                    description: isDataRefreshing ? 'AI detection models updated with latest threat intelligence data.' : 'Security definitions were updated to the latest version.',
                    user: 'System',
                    time: isDataRefreshing ? '2 hours ago' : '3 hours ago',
                    status: 'success'
                  }
                ]}
                onViewAll={() => console.log('View all activities')}
                isRefreshing={isDataRefreshing}
              />
            </Box>
          </Grid>
        </Grid>
      </Box>
    </div>
  );
};

export default Page;
