import { IMenuItem } from "@/interfaces";
import {
  faDashboard,
  faUserShield,
  faUsers,
  faUserTie,
  faPlugCircleBolt,
  faEnvelopeCircleCheck,
  faGavel,
  faChartLine,
  faShieldVirus,
  faFolderClosed,
  faDatabase,
  faTriangleExclamation,
  faBug,
  faUser,
  faKey,
  faLaptop,
  faFileLines,
  faPlay,
  faCheck,
  faLink,
  faGlobe,
  faEnvelope,
  faClipboardList,
} from "@fortawesome/free-solid-svg-icons";


// Admin menu items
export const adminMenuItems: IMenuItem[] = [
  {
    title: "Dashboard",
    path: "/dashboard",
    icon: faDashboard,
  },
  {
    title: "Super Admin",
    icon: faUserTie,
    submenu: [
      {
        title: "Dashboard",
        path: "/super-admin/dashboard",
        icon: faDashboard,
      },
      {
        title: "Customers",
        path: "/super-admin/customers",
        icon: faUsers,
      },
      {
        title: "Users",
        path: "/super-admin/users",
        icon: faUser,
      },
      {
        title: "Licenses",
        path: "/super-admin/licenses",
        icon: faKey,
      },
      {
        title: "All Logs Report",
        path: "/super-admin/logs-report",
        icon: faClipboardList,
      },
      {
        title: "Profile",
        path: "/super-admin/profile",
        icon: faUser,
      },
    ],
  },
  {
    title: "Plugin",
    icon: faPlugCircleBolt,
    submenu: [
      {
        title: "Available License",
        path: "/plugin/available-license",
        icon: faKey,
      },
      {
        title: "Allocated License",
        path: "/plugin/allocated-license",
        icon: faKey,
      },
      {
        title: "Agent Installed",
        path: "/plugin/agent-installed",
        icon: faLaptop,
      },
      {
        title: "License Report",
        path: "/plugin/license-report",
        icon: faFileLines,
      },
    ],
  },
  {
    title: "Phishing",
    path: "/phishing",
    icon: faEnvelopeCircleCheck,
  },
  {
    title: "Disputes",
    icon: faGavel,
    submenu: [
      {
        title: "Pending Disputes",
        path: "/disputes/pending",
        icon: faTriangleExclamation,
      },
      {
        title: "Resolved Disputes",
        path: "/disputes/resolved",
        icon: faCheck,
      },
    ],
  },
  {
    title: "Reports",
    path: "/reports",
    icon: faChartLine,
  },
  {
    title: "Sandbox",
    icon: faShieldVirus,
    submenu: [
      {
        title: "Running Sandbox",
        path: "/sandbox/running",
        icon: faPlay,
      },
      {
        title: "Completed Sandbox",
        path: "/sandbox/completed",
        icon: faCheck,
      },
    ],
  },
  {
    title: "Quarantine",
    path: "/quarantine",
    icon: faFolderClosed,
  },
  {
    title: "Rogue DB",
    icon: faDatabase,
    submenu: [
      {
        title: "URL",
        path: "/rogue-db/url",
        icon: faLink,
      },
      {
        title: "Domain",
        path: "/rogue-db/domain",
        icon: faGlobe,
      },
      {
        title: "Mail",
        path: "/rogue-db/mail",
        icon: faEnvelope,
      },
    ],
  },
  {
    title: "Logs",
    icon: faClipboardList,
    submenu: [
      {
        title: "Exception Logs",
        path: "/exception-logs",
        icon: faTriangleExclamation,
      },
      {
        title: "Error Logs",
        path: "/error-logs",
        icon: faBug,
      },
    ],
  },
  {
    title: "Profile",
    path: "/profile",
    icon: faUser,
  },
];
