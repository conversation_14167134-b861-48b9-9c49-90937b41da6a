"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@mui/material";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  IconButton,
  Link,
  Paper,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";

interface BulkAllocateButtonProps {
  onClick: () => void;
}

export const BulkAllocateButton: React.FC<BulkAllocateButtonProps> = ({
  onClick,
}) => {
  const [open, setOpen] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleOpen = () => {
    setOpen(true);
    // Also call the original onClick handler
    onClick();
  };

  const handleClose = () => {
    setOpen(false);
    setFile(null);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    if (selectedFile && selectedFile.name.toLowerCase().endsWith('.csv')) {
      setFile(selectedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFile = e.dataTransfer.files?.[0] || null;
    if (droppedFile && droppedFile.name.toLowerCase().endsWith('.csv')) {
      setFile(droppedFile);
    }
  };

  const handleDropzoneClick = () => {
    fileInputRef.current?.click();
  };

  const handleDownloadTemplate = () => {
    const csvContent = "license_id,allocated_to,mac_address\n";
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'license_allocation_template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleSubmit = () => {
    if (file) {
      alert(`CSV file "${file.name}" uploaded successfully. Processing license allocations.`);
      handleClose();
    }
  };

  return (
    <>
      <Button
        variant="contained"
        startIcon={<PersonAddIcon />}
        onClick={handleOpen}
        size="small"
      >
        Bulk allocate
      </Button>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pb: 1
        }}>
          <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
            Allocate Multiple Licenses
          </Typography>
          <IconButton edge="end" color="inherit" onClick={handleClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 3 }}>
            Upload a CSV file containing license allocations
          </Typography>

          <Paper
            elevation={0}
            sx={{
              border: '2px dashed rgba(0, 0, 0, 0.2)',
              p: 4,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              bgcolor: isDragging ? 'rgba(0, 0, 0, 0.08)' : 'rgba(0, 0, 0, 0.02)',
              borderColor: isDragging ? 'rgba(0, 0, 0, 0.5)' : (file ? 'rgba(58, 82, 166, 0.5)' : 'rgba(0, 0, 0, 0.2)'),
              cursor: 'pointer',
              transition: 'all 0.3s ease',
            }}
            onClick={handleDropzoneClick}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              type="file"
              ref={fileInputRef}
              style={{ display: 'none' }}
              accept=".csv"
              onChange={handleFileChange}
            />

            <CloudUploadIcon sx={{
              fontSize: 48,
              color: file ? '#3A52A6' : 'rgba(0, 0, 0, 0.3)',
              mb: 2
            }} />

            <Typography variant="body1" align="center" sx={{ mb: 1 }}>
              {file ? file.name : 'Drag and drop your CSV file here, or browse'}
            </Typography>

            {!file && (
              <Typography variant="body2" align="center" color="textSecondary">
                CSV file should contain the following columns:
                <br />
                <Box component="span" sx={{ fontFamily: 'monospace', color: '#1976d2' }}>
                  license_id, allocated_to, mac_address
                </Box>
              </Typography>
            )}
          </Paper>

          <Box sx={{ mt: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="textSecondary">
              Need a template? <Link href="#" onClick={handleDownloadTemplate} underline="hover">Download CSV template</Link>
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={handleClose} variant="outlined">
            Cancel
          </Button>
          <Button
            variant="contained"
            sx={{ bgcolor: "#3A52A6" }}
            disabled={!file}
            onClick={handleSubmit}
          >
            Upload and Allocate
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default BulkAllocateButton;
