"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  useTheme,
  InputAdornment,
  Popover,
  Grid,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SendIcon from "@mui/icons-material/Send";
import EmojiEmotionsIcon from "@mui/icons-material/EmojiEmotions";
import { useThemeWithToggle } from '@/context/ThemeContext';

interface AdminCommentModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (comment: string) => void;
  currentStatus: string;
  newStatus: string;
}

// Common emojis array
const commonEmojis = [
  '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
  '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙',
  '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
  '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞',
  '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '❣️', '💕',
];

const AdminCommentModal: React.FC<AdminCommentModalProps> = ({
  open,
  onClose,
  onSubmit,
  currentStatus,
  newStatus,
}) => {
  const [comment, setComment] = useState("");
  const [emojiPickerAnchorEl, setEmojiPickerAnchorEl] = useState<HTMLButtonElement | null>(null);
  const theme = useTheme();
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';

  // Reset comment when modal opens
  useEffect(() => {
    if (open) {
      setComment("");
    }
  }, [open]);

  // Emoji picker handlers
  const handleEmojiPickerOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setEmojiPickerAnchorEl(event.currentTarget);
  };

  const handleEmojiPickerClose = () => {
    setEmojiPickerAnchorEl(null);
  };

  const handleEmojiClick = (emoji: string) => {
    setComment(prev => prev + emoji);
    // Optional: close the picker after selection
    // handleEmojiPickerClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (comment.trim()) {
      onSubmit(comment);
      setComment("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'safe':
        return '#2E7D32'; // Darker, less bright green
      case 'unsafe':
        return '#C62828'; // Darker, less bright red
      default:
        return '#9e9e9e';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 1,
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
        }
      }}
    >
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
        pb: 1
      }}>
        <Typography variant="h6" component="div" sx={{
          fontWeight: 600,
          color: isDarkMode ? "#fff" : "#000"
        }}>
          Comments
        </Typography>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 3 }}>
        <Typography variant="body2" sx={{ mb: 3, color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.6)" }}>
          Please provide a comment for changing status from <strong style={{ color: getStatusColor(currentStatus) }}>{currentStatus}</strong> to <strong style={{ color: getStatusColor(newStatus) }}>{newStatus}</strong>
        </Typography>
        <Box component="form" onSubmit={handleSubmit} noValidate>
          <TextField
            autoFocus
            margin="normal"
            fullWidth
            id="comment"
            placeholder="Type your comment..."
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            onKeyDown={handleKeyDown}
            multiline
            rows={3}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton
                    size="small"
                    onClick={handleEmojiPickerOpen}
                    aria-label="emoji picker"
                  >
                    <EmojiEmotionsIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    onClick={handleSubmit}
                    disabled={!comment.trim()}
                    sx={{
                      color: comment.trim() ? '#3A52A6' : isDarkMode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
                    }}
                  >
                    <SendIcon />
                  </IconButton>
                </InputAdornment>
              ),
              sx: {
                backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: 1,
              }
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
                },
                '&:hover fieldset': {
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                },
                '&.Mui-focused fieldset': {
                  borderColor: '#3A52A6',
                },
              },
            }}
          />
        </Box>
      </DialogContent>
      <DialogActions sx={{
        px: 3,
        py: 2,
        borderTop: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
      }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            color: isDarkMode ? "#fff" : "rgba(0, 0, 0, 0.7)",
            borderColor: isDarkMode ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.23)",
            "&:hover": {
              borderColor: isDarkMode ? "rgba(255, 255, 255, 0.5)" : "rgba(0, 0, 0, 0.5)",
              backgroundColor: isDarkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)"
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={!comment.trim()}
          sx={{
            backgroundColor: "#3A52A6",
            "&:hover": {
              backgroundColor: "#2A3F8F"
            },
            "&.Mui-disabled": {
              backgroundColor: isDarkMode ? "rgba(255, 255, 255, 0.12)" : "rgba(0, 0, 0, 0.12)",
              color: isDarkMode ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.26)"
            }
          }}
        >
          Submit
        </Button>
      </DialogActions>

      {/* Emoji Picker Popover */}
      <Popover
        open={Boolean(emojiPickerAnchorEl)}
        anchorEl={emojiPickerAnchorEl}
        onClose={handleEmojiPickerClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'start',
        }}
        transformOrigin={{
          vertical: 'bottom',
          horizontal: 'start',
        }}
        sx={{
          '& .MuiPopover-paper': {
            padding: 1,
            backgroundColor: isDarkMode ? '#333' : '#fff',
            maxWidth: 320
          }
        }}
      >
        <Grid container spacing={0.5}>
          {commonEmojis.map((emoji, index) => (
            <Grid item key={index}>
              <IconButton
                onClick={() => handleEmojiClick(emoji)}
                size="small"
                sx={{
                  minWidth: '36px',
                  height: '36px',
                  fontSize: '1.2rem',
                  '&:hover': {
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
                  }
                }}
              >
                {emoji}
              </IconButton>
            </Grid>
          ))}
        </Grid>
      </Popover>
    </Dialog>
  );
};

export default AdminCommentModal;
