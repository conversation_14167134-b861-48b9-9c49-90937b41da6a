// import React from "react";
// import { IconButton } from "@mui/material";
// import { faBars } from "@fortawesome/free-solid-svg-icons";

// import { Icon } from "../icon";
// import ThemeToggle from "../themeToggel";
// import {
//   HeaderActions,
//   HeaderContainer,
//   HeaderContent,
//   AppTitle,
// } from "./header.style";

// interface HeaderProps {
//   title?: string;
//   onToggleSidebar?: () => void;
// }

// export const Header: React.FC<HeaderProps> = ({ title, onToggleSidebar }) => {
//   return (
//     <HeaderContainer>
//       <HeaderContent>
//         <div style={{ display: "flex", alignItems: "center" }}>
//           <IconButton
//             edge="start"
//             color="inherit"
//             aria-label="menu"
//             onClick={onToggleSidebar}
//           >
//             <Icon icon={faBars} size="medium" onlyIcon />
//           </IconButton>
//           <AppTitle variant="h6">Decision Support System</AppTitle>
//         </div>
//         <HeaderActions>
//           <ThemeToggle />
//         </HeaderActions>
//       </HeaderContent>
//     </HeaderContainer>
//   );
// };


import React, { useState, useEffect } from "react";
import { IconButton, Menu, MenuItem, Avatar, Typography, Box, Divider, ListItemIcon, ListItemText, Collapse, useTheme as useMuiTheme, Tooltip, Fade } from "@mui/material";
import {
  faBars,
  faUser,
  faSignOutAlt,
  faCog,
  faSun,
  faMoon,
  faLaptop,
  faChevronRight,
  faChevronDown,
  faPalette,
  faCheck,
  faShieldVirus,
  faBell,
  faSearch,
  faCircleCheck
} from "@fortawesome/free-solid-svg-icons";
import { useRouter } from "next/navigation";
import { Icon } from "../icon";
import { useTheme } from "@/context/ThemeContext";
import { ThemeToggleEnum } from "../themeToggel/enum";
import { RoutePathEnum } from "@/enum";
import { DEFAULT_THEME_LIST } from "@/constants/theme";
import {
  HeaderActions,
  HeaderContainer,
  HeaderContent,
  AppTitle,
  NotificationBell,
  AnimatedBadge,
  SearchContainer,
  SearchInput,
  SearchIconWrapper,
  StatusIndicator
} from "./header.style";
import styled from "@emotion/styled";

const MenuItemContent = styled(Box)`
  display: flex;
  align-items: center;
  gap: 12px;
`;

interface HeaderProps {
  title?: string;
  onToggleSidebar?: () => void;
  username?: string;
  role?: string;
}

export const Header: React.FC<HeaderProps> = ({
  title,
  onToggleSidebar,
  username = "<EMAIL>",
  role = "Admin",
}) => {
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchorEl, setNotificationAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const notificationsOpen = Boolean(notificationAnchorEl);
  const { mode, setTheme } = useTheme();
  const muiTheme = useMuiTheme();
  const [themeMenuOpen, setThemeMenuOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [systemStatus, setSystemStatus] = useState<'online' | 'warning' | 'offline'>('online');
  const [notificationCount, setNotificationCount] = useState(3);
  const [notifications, setNotifications] = useState([
    { id: 1, title: "New phishing attempt detected", time: "5 minutes ago", read: false },
    { id: 2, title: "System update available", time: "1 hour ago", read: false },
    { id: 3, title: "License expiring soon", time: "2 days ago", read: false },
  ]);

  // Simulate status changes for demo purposes
  useEffect(() => {
    const statusInterval = setInterval(() => {
      // Randomly change status for demonstration
      const statuses: Array<'online' | 'warning' | 'offline'> = ['online', 'warning', 'online'];
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
      setSystemStatus(randomStatus);
    }, 30000); // Change every 30 seconds

    return () => clearInterval(statusInterval);
  }, []);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchorEl(event.currentTarget);
  };

  const handleNotificationClose = () => {
    setNotificationAnchorEl(null);
  };

  const handleNotificationRead = (id: number) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
    setNotificationCount(prev => Math.max(0, prev - 1));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchValue.trim()) {
      // Implement search functionality
      console.log("Searching for:", searchValue);
      // Clear search after submission
      setSearchValue("");
    }
  };

  const handleLogout = () => {
    // Clear token and other auth data from localStorage
    import("@/utills/storage.utill").then(({ StorageHelper }) => {
      StorageHelper.clearLocalStorage();

      // Close the menu
      handleMenuClose();

      // Redirect to login page
      import("@/enum").then(({ RoutePathEnum }) => {
        window.location.href = RoutePathEnum.ADMIN_LOGIN;
      });
    });
  };

  const handleProfileSettings = () => {
    handleMenuClose();
    // Redirect to profile page using direct URL for consistency with sidebar
    window.location.href = "http://localhost:3000/profile";
  };

  const handleThemeToggle = () => {
    // Toggle the theme submenu
    setThemeMenuOpen(!themeMenuOpen);
  };

  const handleThemeSelect = (themeValue: string) => {
    // Set the selected theme
    setTheme(themeValue as "light" | "dark" | "system");
    // Don't close the menu so user can see the theme change
  };

  const getStatusText = () => {
    switch (systemStatus) {
      case 'online':
        return 'System Online';
      case 'warning':
        return 'Performance Issues';
      case 'offline':
        return 'System Offline';
      default:
        return 'System Online';
    }
  };

  return (
    <HeaderContainer>
      <HeaderContent>
        <div style={{ display: "flex", alignItems: "center" }}>
          <Box
            component="button"
            onClick={(e) => {
              // Add a visual feedback class to the button
              const button = e.currentTarget;
              button.classList.add('menu-button-active');

              // Remove the class after the animation completes
              setTimeout(() => {
                button.classList.remove('menu-button-active');
              }, 300);

              // Call the toggle function
              onToggleSidebar?.();
            }}
            className="menu-button"
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 40,
              height: 40,
              padding: 0,
              border: 'none',
              background: 'transparent',
              cursor: 'pointer',
              color: 'inherit',
              borderRadius: '4px',
              transition: 'all 0.2s cubic-bezier(0.25, 1, 0.5, 1)',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
              },
              '&:active, &.menu-button-active': {
                backgroundColor: 'rgba(0, 0, 0, 0.08)',
                transform: 'scale(0.95)',
              },
              '&:focus': {
                outline: 'none',
              },
              zIndex: 1200 // Ensure it's above other elements
            }}
            aria-label="menu"
          >
            <Icon
              icon={faBars}
              size="medium"
              onlyIcon
              sx={{
                transition: 'transform 0.3s cubic-bezier(0.25, 1, 0.5, 1)',
                '.menu-button-active &': {
                  transform: 'rotate(90deg) scale(1.1)',
                }
              }}
            />
          </Box>
          <AppTitle variant="h6">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Icon icon={faShieldVirus} size="small" onlyIcon color={muiTheme.palette.primary.main} />
              {title || "Phishing Dashboard"}
            </Box>
          </AppTitle>
        </div>

        <HeaderActions>
          <Box sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-end",
            gap: 2 // Add gap between elements
          }}>

            {/* User Profile Section */}
            <Box sx={{
              display: "flex",
              alignItems: "center"
            }}>
              <Box sx={{ textAlign: "right", mr: 1 }}>
                <Typography variant="subtitle2" color="inherit">
                  {username}
                </Typography>
                <Typography variant="caption" color="gray">
                  {role}
                </Typography>
              </Box>
              <IconButton
                onClick={handleMenuOpen}
                sx={{
                  ml: 0.5,
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'scale(1.05)'
                  }
                }}
              >
                <Avatar sx={{
                  width: 32,
                  height: 32,
                  bgcolor: "#1976d2",
                  boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                }}>
                  {username[0]}
                </Avatar>
              </IconButton>

              <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleMenuClose}
                PaperProps={{
                  elevation: 4,
                  sx: {
                    minWidth: 200,
                    borderRadius: 2,
                    mt: 1,
                    overflow: 'visible',
                    filter: 'drop-shadow(0px 3px 8px rgba(0,0,0,0.15))',
                    '&:before': {
                      content: '""',
                      display: 'block',
                      position: 'absolute',
                      top: 0,
                      right: 14,
                      width: 10,
                      height: 10,
                      bgcolor: 'background.paper',
                      transform: 'translateY(-50%) rotate(45deg)',
                      zIndex: 0,
                    },
                  },
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
              >
                <Box sx={{ p: 2, pb: 1 }}>
                  <Avatar sx={{
                    width: 50,
                    height: 50,
                    mb: 1,
                    mx: 'auto',
                    bgcolor: "#1976d2",
                    boxShadow: '0 2px 8px rgba(0,0,0,0.2)'
                  }}>
                    {username[0]}
                  </Avatar>
                  <Typography variant="body1" align="center" fontWeight="bold">{username}</Typography>
                  <Typography variant="caption" align="center" display="block" color="text.secondary">{role}</Typography>
                </Box>

                <Divider />

                <MenuItem onClick={handleProfileSettings} sx={{ py: 1.5 }}>
                  <MenuItemContent>
                    <Icon icon={faUser} size="small" onlyIcon />
                    <Typography variant="body2">Profile</Typography>
                  </MenuItemContent>
                </MenuItem>

                <MenuItem onClick={handleProfileSettings} sx={{ py: 1.5 }}>
                  <MenuItemContent>
                    <Icon icon={faCog} size="small" onlyIcon />
                    <Typography variant="body2">Settings</Typography>
                  </MenuItemContent>
                </MenuItem>

                {/* Theme Menu Item with Dropdown */}
                <MenuItem onClick={handleThemeToggle} sx={{ py: 1.5 }}>
                  <MenuItemContent>
                    <Icon icon={faPalette} size="small" onlyIcon />
                    <Typography variant="body2" sx={{ flex: 1 }}>Theme</Typography>
                    <Icon
                      icon={themeMenuOpen ? faChevronDown : faChevronRight}
                      size="small"
                      onlyIcon
                      sx={{ color: 'text.secondary', fontSize: '0.75rem' }}
                    />
                  </MenuItemContent>
                </MenuItem>

                {/* Theme Submenu */}
                <Collapse in={themeMenuOpen} timeout="auto" unmountOnExit>
                  <Box sx={{ pl: 2 }}>
                    {/* Light Theme Option */}
                    <MenuItem
                      onClick={() => handleThemeSelect('light')}
                      sx={{
                        py: 1.5,
                        bgcolor: mode === 'light' ? 'action.selected' : 'transparent'
                      }}
                    >
                      <MenuItemContent>
                        <Icon icon={faSun} size="small" onlyIcon sx={{ color: 'warning.main' }} />
                        <Typography variant="body2" sx={{ flex: 1 }}>Light theme</Typography>
                        {mode === 'light' && (
                          <Icon icon={faCheck} size="small" onlyIcon sx={{ color: 'primary.main', fontSize: '0.75rem' }} />
                        )}
                      </MenuItemContent>
                    </MenuItem>

                    {/* Dark Theme Option */}
                    <MenuItem
                      onClick={() => handleThemeSelect('dark')}
                      sx={{
                        py: 1.5,
                        bgcolor: mode === 'dark' ? 'action.selected' : 'transparent'
                      }}
                    >
                      <MenuItemContent>
                        <Icon icon={faMoon} size="small" onlyIcon sx={{ color: 'info.main' }} />
                        <Typography variant="body2" sx={{ flex: 1 }}>Dark theme</Typography>
                        {mode === 'dark' && (
                          <Icon icon={faCheck} size="small" onlyIcon sx={{ color: 'primary.main', fontSize: '0.75rem' }} />
                        )}
                      </MenuItemContent>
                    </MenuItem>

                    {/* Device Default Option */}
                    <MenuItem
                      onClick={() => handleThemeSelect('system')}
                      sx={{
                        py: 1.5,
                        bgcolor: mode === 'system' ? 'action.selected' : 'transparent'
                      }}
                    >
                      <MenuItemContent>
                        <Icon icon={faLaptop} size="small" onlyIcon sx={{ color: 'success.main' }} />
                        <Typography variant="body2" sx={{ flex: 1 }}>Device default</Typography>
                        {mode === 'system' && (
                          <Icon icon={faCheck} size="small" onlyIcon sx={{ color: 'primary.main', fontSize: '0.75rem' }} />
                        )}
                      </MenuItemContent>
                    </MenuItem>
                  </Box>
                </Collapse>

                <Divider />

                <MenuItem onClick={handleLogout} sx={{ py: 1.5 }}>
                  <MenuItemContent>
                    <Icon icon={faSignOutAlt} size="small" onlyIcon />
                    <Typography variant="body2">Logout</Typography>
                  </MenuItemContent>
                </MenuItem>
              </Menu>
            </Box>
          </Box>
        </HeaderActions>
      </HeaderContent>
    </HeaderContainer>
  );
};
