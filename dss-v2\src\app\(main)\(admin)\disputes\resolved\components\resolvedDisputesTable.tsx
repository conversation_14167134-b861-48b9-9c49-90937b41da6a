import React, { useState, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableFooter,
  Box,
} from '@mui/material';
import { CustomTablePagination } from '@/components/common/table/tablePagination';
import { CommonTableStyle } from '@/components/common/table/commonTableStyle';
import { ViewDetailsButton } from '../../pending/components/viewDetailsButton';
import { StatusButton } from '../../pending/components/statusButton';

interface DisputeData {
  sn: number;
  sendersEmail: string;
  receiversEmail: string;
  subject: string;
  counter: number;
  aiStatus: string;
  adminStatus: string;
  createdAt: string;
  details: string;
}

interface ResolvedDisputesTableProps {
  data: DisputeData[];
  visibleColumns?: string[];
  onAdminStatusChange?: (index: number, newStatus: string, comment?: string) => void;
}

export const ResolvedDisputesTable: React.FC<ResolvedDisputesTableProps> = ({
  data,
  visibleColumns = ['sn', 'sendersEmail', 'receiversEmail', 'subject', 'counter', 'aiStatus', 'adminStatus', 'createdAt', 'details'],
  onAdminStatusChange
}) => {
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
  }, []);

  // Calculate pagination
  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentPageData = data.slice(startIndex, endIndex);

  return (
    <Box sx={{ width: '100%' }}>
      <CommonTableStyle elevation={0}>
        <Table sx={{ minWidth: 650 }} aria-label="resolved disputes table">
          <TableHead>
            <TableRow sx={{ backgroundColor: '#3A52A6' }}>
              {visibleColumns.includes('sn') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '5%' })}>SN</TableCell>
              )}
              {visibleColumns.includes('sendersEmail') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>SENDER'S EMAIL</TableCell>
              )}
              {visibleColumns.includes('receiversEmail') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>RECEIVER'S EMAIL</TableCell>
              )}
              {visibleColumns.includes('subject') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>SUBJECT</TableCell>
              )}
              {visibleColumns.includes('counter') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>COUNTER</TableCell>
              )}
              {visibleColumns.includes('aiStatus') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>AI STATUS</TableCell>
              )}
              {visibleColumns.includes('adminStatus') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>ADMIN STATUS</TableCell>
              )}
              {visibleColumns.includes('createdAt') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>CREATED AT</TableCell>
              )}
              {visibleColumns.includes('details') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>DETAILS</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {currentPageData.map((row, index) => (
              <TableRow
                key={row.sn}
                sx={{
                  '&:nth-of-type(odd)': { backgroundColor: '#f9f9f9' },
                  '&:hover': { backgroundColor: '#f1f1f1' },
                }}
              >
                {visibleColumns.includes('sn') && (
                  <TableCell>{startIndex + index + 1}</TableCell>
                )}
                {visibleColumns.includes('sendersEmail') && (
                  <TableCell>{row.sendersEmail}</TableCell>
                )}
                {visibleColumns.includes('receiversEmail') && (
                  <TableCell>{row.receiversEmail}</TableCell>
                )}
                {visibleColumns.includes('subject') && (
                  <TableCell>{row.subject}</TableCell>
                )}
                {visibleColumns.includes('counter') && (
                  <TableCell>{row.counter}</TableCell>
                )}
                {visibleColumns.includes('aiStatus') && (
                  <TableCell>
                    <StatusButton type="ai" status={row.aiStatus} onStatusChange={() => {}} />
                  </TableCell>
                )}
                {visibleColumns.includes('adminStatus') && (
                  <TableCell>
                    <StatusButton
                      type="admin"
                      status={row.adminStatus}
                      onStatusChange={(newStatus, _, comment) => {
                        if (onAdminStatusChange) {
                          onAdminStatusChange(startIndex + index, newStatus, comment);
                        }
                      }}
                      index={startIndex + index}
                      itemId={`dispute-${row.sn}`}
                    />
                  </TableCell>
                )}
                {visibleColumns.includes('createdAt') && (
                  <TableCell>{row.createdAt}</TableCell>
                )}
                {visibleColumns.includes('details') && (
                  <TableCell>
                    <ViewDetailsButton
                      value={row.details}
                      index={index}
                      useDirectNavigation={true}
                      source="resolved"
                    />
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CommonTableStyle>

      {/* Pagination */}
      <Table>
        <TableFooter>
          <TableRow>
            <CustomTablePagination
              page={page}
              limit={rowsPerPage}
              total={data.length}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </Box>
  );
};

export default ResolvedDisputesTable;
