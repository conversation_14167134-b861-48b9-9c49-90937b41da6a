"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';

interface PendingDisputesFilterButtonProps {
  filterOptions: { label: string; value: string; type?: 'text' | 'date' | 'email' | 'number' }[];
  onApplyFilters: (filters: string[]) => void;
  searchTerm: string;
}

const PendingDisputesFilterButton: React.FC<PendingDisputesFilterButtonProps> = ({
  filterOptions,
  onApplyFilters,
  searchTerm
}) => {

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Pending Disputes Filters"
      title="Pending Disputes Filters"
      description="Apply filters to refine your pending disputes search results"
      icon={<ReportProblemIcon />}
      searchTerm={searchTerm}
    />
  );
};

export default PendingDisputesFilterButton;
