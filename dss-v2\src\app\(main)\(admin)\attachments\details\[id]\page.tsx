"use client";

import React from "react";
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  Chip,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
  Alert,
  Card,
  CardContent,
  Tab,
  Tabs,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import {
  AttachFile,
  Description,
  Warning,
  Security,
  CheckCircle,
  Error,
  Info,
  CloudDownload,
  Delete,
  PlayArrow,
  Block,
  CheckCircleOutline,
  Email,
  CalendarToday,
  Person,
  Link,
  Code,
  History,
  NetworkCheck,
  Settings,
  Fingerprint,
} from "@mui/icons-material";
import { useThemeWithToggle } from '@/context/ThemeContext';
import { PageHeader } from "@/components/common";
import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay } from "@/components/common/breadcrumb";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`attachment-tabpanel-${index}`}
      aria-labelledby={`attachment-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `attachment-tab-${index}`,
    'aria-controls': `attachment-tabpanel-${index}`,
  };
}

// Mock data for the attachment details
const mockAttachmentData = {
  id: "att-123456",
  name: "invoice_april_2023.pdf",
  status: "Malicious",
  type: "application/pdf",
  size: "1.2 MB",
  uploadDate: "15-05-2023 10:30 AM",
  hash: {
    md5: "d41d8cd98f00b204e9800998ecf8427e",
    sha1: "da39a3ee5e6b4b0d3255bfef95601890afd80709",
    sha256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
  },
  threatScore: 85,
  scanResults: {
    malware: true,
    phishing: false,
    suspicious: true,
    clean: false
  },
  associatedEmail: {
    messageId: "msg-789012",
    sendersEmail: "<EMAIL>",
    receiversEmail: "<EMAIL>",
    subject: "Invoice for April 2023",
    createTime: "15-05-2023 10:25 AM"
  },
  metadata: {
    creationDate: "14-05-2023 09:15 AM",
    modificationDate: "14-05-2023 09:30 AM",
    author: "Unknown",
    embeddedLinks: [
      "https://malicious-site.com/download",
      "https://another-bad-site.net/payload"
    ],
    embeddedObjects: [
      "JavaScript code",
      "Embedded macro"
    ]
  },
  analysisHistory: [
    {
      date: "15-05-2023 10:31 AM",
      action: "Initial scan",
      result: "Detected malicious JavaScript",
      user: "System"
    },
    {
      date: "15-05-2023 10:35 AM",
      action: "Sandbox analysis",
      result: "Attempted connection to known malicious domains",
      user: "System"
    },
    {
      date: "15-05-2023 11:00 AM",
      action: "Manual review",
      result: "Confirmed malicious",
      user: "Admin"
    }
  ],
  sandboxResults: {
    behaviorSummary: "The file attempts to execute obfuscated JavaScript code that tries to download additional payloads from remote servers.",
    networkConnections: [
      "malicious-site.com:443",
      "another-bad-site.net:80"
    ],
    systemModifications: [
      "Created registry key: HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
      "Modified system32 files"
    ],
    suspiciousActivities: [
      "Attempted to disable security software",
      "Tried to access sensitive user data"
    ]
  }
};

export default function AttachmentDetails({ params }: { params: { id: string } }) {
  const attachmentId = params.id;
  console.log('Attachment ID:', attachmentId);

  const [tabValue, setTabValue] = React.useState(0);
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';

  // Use the mock data with the provided ID
  const attachmentData = { ...mockAttachmentData, id: attachmentId };

  // Breadcrumbs for the page
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Quarantine",
      path: RoutePathEnum.QUARANTINE,
      forwardParam: false,
      clickable: true,
    },
    {
      name: `Attachment Details: ${attachmentData?.name || attachmentId}`,
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  // Handler functions
  const handleDownload = () => {
    console.log(`Downloading attachment: ${attachmentId}`);
    alert("Download functionality would be implemented here with appropriate security warnings.");
  };

  const handleRelease = () => {
    console.log(`Releasing attachment: ${attachmentId}`);
    alert("Release functionality would be implemented here.");
  };

  const handleDelete = () => {
    console.log(`Deleting attachment: ${attachmentId}`);
    alert("Delete functionality would be implemented here.");
  };

  const handleAddToAllowlist = () => {
    console.log(`Adding attachment to allowlist: ${attachmentId}`);
    alert("Add to allowlist functionality would be implemented here.");
  };

  const handleAddToBlocklist = () => {
    console.log(`Adding attachment to blocklist: ${attachmentId}`);
    alert("Add to blocklist functionality would be implemented here.");
  };

  const handleRunAdditionalAnalysis = () => {
    console.log(`Running additional analysis on attachment: ${attachmentId}`);
    alert("Additional analysis functionality would be implemented here.");
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Function to get color based on status
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'malicious':
        return 'error';
      case 'suspicious':
        return 'warning';
      case 'safe':
        return 'success';
      default:
        return 'default';
    }
  };

  // Function to get icon based on status
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'malicious':
        return <Error color="error" />;
      case 'suspicious':
        return <Warning color="warning" />;
      case 'safe':
        return <CheckCircle color="success" />;
      default:
        return <Info color="info" />;
    }
  };

  return (
    <>
      <PageHeader
        title={`Attachment Details: ${attachmentData.name}`}
        breadcrumbs={breadcrumbs}
      />

      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: 2,
          backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.02)',
          mb: 3
        }}
      >
        {/* Header with file info and status */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 3
          }}
        >
          <AttachFile
            sx={{
              mr: 1.5,
              fontSize: '2rem',
              color: isDarkMode ? 'primary.light' : 'primary.main'
            }}
          />
          <Typography
            variant="h5"
            component="h1"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
            }}
          >
            {attachmentData.name}
          </Typography>
          <Box sx={{ flexGrow: 1 }} />
          <Chip
            icon={getStatusIcon(attachmentData.status)}
            label={attachmentData.status}
            color={getStatusColor(attachmentData.status) as any}
            sx={{
              fontWeight: 600,
              ml: 1
            }}
          />
        </Box>

        {/* Warning banner for malicious files */}
        {attachmentData.status.toLowerCase() === 'malicious' && (
          <Alert
            severity="error"
            variant="filled"
            sx={{ mb: 3 }}
          >
            This file has been identified as malicious. Handle with extreme caution.
          </Alert>
        )}

        {/* Action buttons */}
        <Box sx={{ mb: 3, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="contained"
            startIcon={<CloudDownload />}
            onClick={handleDownload}
            color="primary"
          >
            Download (with caution)
          </Button>
          <Button
            variant="outlined"
            startIcon={<CheckCircleOutline />}
            onClick={handleRelease}
          >
            Release to recipient
          </Button>
          <Button
            variant="outlined"
            startIcon={<Delete />}
            onClick={handleDelete}
            color="error"
          >
            Delete permanently
          </Button>
          <Button
            variant="outlined"
            startIcon={<CheckCircle />}
            onClick={handleAddToAllowlist}
            color="success"
          >
            Add to allowlist
          </Button>
          <Button
            variant="outlined"
            startIcon={<Block />}
            onClick={handleAddToBlocklist}
            color="error"
          >
            Add to blocklist
          </Button>
          <Button
            variant="outlined"
            startIcon={<PlayArrow />}
            onClick={handleRunAdditionalAnalysis}
            color="info"
          >
            Run additional analysis
          </Button>
        </Box>

        {/* Tabs for different sections */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="attachment details tabs"
            textColor="primary"
            indicatorColor="primary"
          >
            <Tab label="Overview" {...a11yProps(0)} />
            <Tab label="Security Analysis" {...a11yProps(1)} />
            <Tab label="Email Context" {...a11yProps(2)} />
            <Tab label="Technical Details" {...a11yProps(3)} />
            <Tab label="History" {...a11yProps(4)} />
          </Tabs>
        </Box>

        {/* Overview Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <Description sx={{ mr: 1 }} /> Basic Information
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="textSecondary">File Name</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{attachmentData.name}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="textSecondary">File Type</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{attachmentData.type}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="textSecondary">File Size</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{attachmentData.size}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="textSecondary">Upload Date</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Typography variant="body2">{attachmentData.uploadDate}</Typography>
                    </Grid>

                    <Grid item xs={4}>
                      <Typography variant="subtitle2" color="textSecondary">Status</Typography>
                    </Grid>
                    <Grid item xs={8}>
                      <Chip
                        label={attachmentData.status}
                        size="small"
                        color={getStatusColor(attachmentData.status) as any}
                      />
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                    <Security sx={{ mr: 1 }} /> Security Summary
                  </Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>Threat Score</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box sx={{ width: '100%', mr: 1 }}>
                      <LinearProgress
                        variant="determinate"
                        value={attachmentData.threatScore}
                        color={attachmentData.threatScore > 70 ? "error" : attachmentData.threatScore > 30 ? "warning" : "success"}
                        sx={{ height: 10, borderRadius: 5 }}
                      />
                    </Box>
                    <Box sx={{ minWidth: 35 }}>
                      <Typography variant="body2" color="text.secondary">{`${attachmentData.threatScore}%`}</Typography>
                    </Box>
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>Scan Results</Typography>
                  <Grid container spacing={1} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Chip
                        icon={attachmentData.scanResults.malware ? <Error /> : <CheckCircle />}
                        label="Malware"
                        size="small"
                        color={attachmentData.scanResults.malware ? "error" : "success"}
                        sx={{ width: '100%' }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Chip
                        icon={attachmentData.scanResults.phishing ? <Error /> : <CheckCircle />}
                        label="Phishing"
                        size="small"
                        color={attachmentData.scanResults.phishing ? "error" : "success"}
                        sx={{ width: '100%' }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Chip
                        icon={attachmentData.scanResults.suspicious ? <Warning /> : <CheckCircle />}
                        label="Suspicious"
                        size="small"
                        color={attachmentData.scanResults.suspicious ? "warning" : "success"}
                        sx={{ width: '100%' }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Chip
                        icon={attachmentData.scanResults.clean ? <CheckCircle /> : <Error />}
                        label="Clean"
                        size="small"
                        color={attachmentData.scanResults.clean ? "success" : "error"}
                        sx={{ width: '100%' }}
                      />
                    </Grid>
                  </Grid>

                  <Typography variant="subtitle2" gutterBottom>File Hashes</Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      <Typography variant="caption" color="textSecondary">MD5:</Typography>
                      <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>{attachmentData.hash.md5}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="caption" color="textSecondary">SHA-1:</Typography>
                      <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>{attachmentData.hash.sha1}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Security Analysis Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Sandbox Analysis Results</Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Typography variant="subtitle2" gutterBottom>Behavior Summary</Typography>
                  <Typography variant="body2" paragraph>
                    {attachmentData.sandboxResults?.behaviorSummary || "No sandbox analysis has been performed on this file."}
                  </Typography>

                  {attachmentData.sandboxResults?.networkConnections && (
                    <>
                      <Typography variant="subtitle2" gutterBottom>Network Connections</Typography>
                      <List dense>
                        {attachmentData.sandboxResults.networkConnections.map((connection, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <NetworkCheck color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary={connection} />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>System Impact</Typography>
                  <Divider sx={{ mb: 2 }} />

                  {attachmentData.sandboxResults?.systemModifications && (
                    <>
                      <Typography variant="subtitle2" gutterBottom>System Modifications</Typography>
                      <List dense>
                        {attachmentData.sandboxResults.systemModifications.map((modification, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Settings color="warning" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary={modification} />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  )}

                  {attachmentData.sandboxResults?.suspiciousActivities && (
                    <>
                      <Typography variant="subtitle2" gutterBottom>Suspicious Activities</Typography>
                      <List dense>
                        {attachmentData.sandboxResults.suspiciousActivities.map((activity, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Warning color="error" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary={activity} />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Email Context Tab */}
        <TabPanel value={tabValue} index={2}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <Email sx={{ mr: 1 }} /> Associated Email
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="textSecondary">Message ID</Typography>
                  <Typography variant="body2">{attachmentData.associatedEmail.messageId}</Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="textSecondary">Date</Typography>
                  <Typography variant="body2">{attachmentData.associatedEmail.createTime}</Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="textSecondary">Sender</Typography>
                  <Typography variant="body2">{attachmentData.associatedEmail.sendersEmail}</Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="textSecondary">Recipient</Typography>
                  <Typography variant="body2">{attachmentData.associatedEmail.receiversEmail}</Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="textSecondary">Subject</Typography>
                  <Typography variant="body2">{attachmentData.associatedEmail.subject}</Typography>
                </Grid>
              </Grid>

              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<Email />}
                  href={`/phishing/email-details?id=${attachmentData.associatedEmail.messageId}`}
                >
                  View Full Email Details
                </Button>
              </Box>
            </CardContent>
          </Card>
        </TabPanel>

        {/* Technical Details Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>File Metadata</Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    {attachmentData.metadata.creationDate && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="textSecondary">Creation Date</Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{attachmentData.metadata.creationDate}</Typography>
                        </Grid>
                      </>
                    )}

                    {attachmentData.metadata.modificationDate && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="textSecondary">Modification Date</Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{attachmentData.metadata.modificationDate}</Typography>
                        </Grid>
                      </>
                    )}

                    {attachmentData.metadata.author && (
                      <>
                        <Grid item xs={4}>
                          <Typography variant="subtitle2" color="textSecondary">Author</Typography>
                        </Grid>
                        <Grid item xs={8}>
                          <Typography variant="body2">{attachmentData.metadata.author}</Typography>
                        </Grid>
                      </>
                    )}
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Embedded Content</Typography>
                  <Divider sx={{ mb: 2 }} />

                  {attachmentData.metadata.embeddedLinks && attachmentData.metadata.embeddedLinks.length > 0 && (
                    <>
                      <Typography variant="subtitle2" gutterBottom>Embedded Links</Typography>
                      <List dense>
                        {attachmentData.metadata.embeddedLinks.map((link, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Link color="primary" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText
                              primary={link}
                              primaryTypographyProps={{
                                sx: { wordBreak: 'break-all' }
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  )}

                  {attachmentData.metadata.embeddedObjects && attachmentData.metadata.embeddedObjects.length > 0 && (
                    <>
                      <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>Embedded Objects</Typography>
                      <List dense>
                        {attachmentData.metadata.embeddedObjects.map((object, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <Code color="warning" fontSize="small" />
                            </ListItemIcon>
                            <ListItemText primary={object} />
                          </ListItem>
                        ))}
                      </List>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>Complete File Hashes</Typography>
                  <Divider sx={{ mb: 2 }} />

                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="subtitle2" color="textSecondary" sx={{ display: 'flex', alignItems: 'center' }}>
                        <Fingerprint fontSize="small" sx={{ mr: 0.5 }} /> MD5
                      </Typography>
                      <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                        {attachmentData.hash.md5}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Typography variant="subtitle2" color="textSecondary" sx={{ display: 'flex', alignItems: 'center' }}>
                        <Fingerprint fontSize="small" sx={{ mr: 0.5 }} /> SHA-1
                      </Typography>
                      <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                        {attachmentData.hash.sha1}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Typography variant="subtitle2" color="textSecondary" sx={{ display: 'flex', alignItems: 'center' }}>
                        <Fingerprint fontSize="small" sx={{ mr: 0.5 }} /> SHA-256
                      </Typography>
                      <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                        {attachmentData.hash.sha256}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* History Tab */}
        <TabPanel value={tabValue} index={4}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <History sx={{ mr: 1 }} /> Analysis History
              </Typography>
              <Divider sx={{ mb: 2 }} />

              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Action</TableCell>
                      <TableCell>Result</TableCell>
                      <TableCell>User</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {attachmentData.analysisHistory.map((history, index) => (
                      <TableRow key={index}>
                        <TableCell>{history.date}</TableCell>
                        <TableCell>{history.action}</TableCell>
                        <TableCell>{history.result}</TableCell>
                        <TableCell>{history.user || 'System'}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </TabPanel>
      </Paper>
    </>
  );
}
