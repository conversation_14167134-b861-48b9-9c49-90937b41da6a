import React from 'react';
import { Chip, Box, Tooltip, IconButton } from '@mui/material';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import FlagIcon from '@mui/icons-material/Flag';

interface StatusChipProps {
  status?: string;
  type: 'ai' | 'admin';
  onStatusChange?: (status: string) => void;
  value?: string;
  index?: number;
}

export const StatusChip: React.FC<StatusChipProps> = ({ status, type, onStatusChange, value }) => {
  // Use value prop if status is not provided (for table component integration)
  const statusText = status || value;
  const getChipColor = () => {
    // Handle undefined or null status
    if (!statusText) {
      return {
        bgcolor: '#9e9e9e',
        color: 'white',
      };
    }

    switch (statusText.toLowerCase()) {
      case 'safe':
        return {
          bgcolor: 'white',
          color: '#2E7D32', // Darker, less bright green
          border: '1px solid #2E7D32',
        };
      case 'unsafe':
        return {
          bgcolor: 'white',
          color: '#C62828', // Darker, less bright red
          border: '1px solid #C62828',
        };
      case 'pending':
        return {
          bgcolor: type === 'admin' ? '#FFB74D' : '#FFB74D', // Light shade of orange for both
          color: 'black',
        };
      default:
        return {
          bgcolor: '#9e9e9e',
          color: 'white',
        };
    }
  };

  const chipColors = getChipColor();

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Chip
        label={statusText || 'Unknown'}
        size="small"
        sx={{
          backgroundColor: chipColors.bgcolor,
          color: chipColors.color,
          fontSize: '0.75rem',
          height: '24px',
          border: chipColors.border,
          fontWeight: 'medium',
          '& .MuiChip-label': {
            px: 1,
          },
        }}
      />
      {type === 'admin' && (
        <IconButton size="small" sx={{ ml: 0.5, p: 0 }}>
          <ArrowDropDownIcon fontSize="small" />
        </IconButton>
      )}
    </Box>
  );
};

export default StatusChip;
