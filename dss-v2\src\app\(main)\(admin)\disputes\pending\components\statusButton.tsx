import React from 'react';
import { Button } from '@mui/material';
import { AdminStatusDropdown } from './adminStatusDropdown';

interface StatusButtonProps {
  status?: string;
  type: 'ai' | 'admin';
  onStatusChange?: (status: string, index?: number, comment?: string) => void;
  value?: string;
  index?: number;
  itemId?: string; // Unique identifier for the item
}

export const StatusButton: React.FC<StatusButtonProps> = ({ status, type, onStatusChange, value, index, itemId }) => {
  // Use value prop if status is not provided (for table component integration)
  const statusText = status || value;

  const getButtonColor = () => {
    // Handle undefined or null status
    if (!statusText) {
      return {
        bgcolor: '#9e9e9e',
        color: 'white',
      };
    }

    switch (statusText.toLowerCase()) {
      case 'safe':
        return {
          bgcolor: '#2E7D32', // Darker, less bright green
          color: 'white',
        };
      case 'unsafe':
        return {
          bgcolor: '#C62828', // Darker, less bright red
          color: 'white',
        };
      case 'pending':
        return {
          bgcolor: '#FFB74D', // Light shade of orange for both
          color: 'black',
        };
      default:
        return {
          bgcolor: '#9e9e9e',
          color: 'white',
        };
    }
  };

  const buttonColors = getButtonColor();

  const handleClick = () => {
    if (onStatusChange) {
      onStatusChange(statusText || '', index);
    }
  };

  const handleStatusChange = (newStatus: string, comment?: string) => {
    if (onStatusChange) {
      onStatusChange(newStatus, index, comment);
    }
  };

  // For admin type, render the admin status buttons
  if (type === 'admin') {
    // Generate a default itemId if none is provided
    const defaultItemId = `dispute-${index}`;

    return (
      <AdminStatusDropdown
        status={statusText}
        onStatusChange={handleStatusChange}
        index={index}
        itemId={itemId || defaultItemId}
      />
    );
  }

  // For AI type or any other, render the button
  return (
    <Button
      onClick={handleClick}
      size="small"
      variant="contained"
      sx={{
        backgroundColor: buttonColors.bgcolor,
        color: buttonColors.color,
        textTransform: 'none',
        minWidth: '60px',
        fontSize: '0.75rem',
        py: 0.5,
        '&:hover': {
          backgroundColor: buttonColors.bgcolor,
          opacity: 0.9,
        },
      }}
    >
      {statusText || 'Unknown'}
    </Button>
  );
};

export default StatusButton;
