import React, { useState } from 'react';
import { Box, Typography, Paper, Tooltip, Fade } from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';

interface ChartCardProps {
  title: string;
  color: string;
  data: number[];
  labels: string[];
}

const ChartCard: React.FC<ChartCardProps> = ({ title, color, data, labels }) => {
  // Find the max value for scaling
  const maxValue = Math.max(...data);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // Calculate trend (positive or negative)
  const trend = data[data.length - 1] > data[0] ? 'up' : 'down';
  const trendPercentage = Math.abs(Math.round(((data[data.length - 1] - data[0]) / data[0]) * 100));

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.08)',
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 500 }}>
          {title}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            px: 1,
            py: 0.5,
            borderRadius: '4px',
            backgroundColor: `${color}20`,
            color: color,
            fontSize: '0.75rem',
            fontWeight: 'bold',
          }}
        >
          {trend === 'up' ? (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingUpIcon fontSize="small" sx={{ mr: 0.5 }} />
              {trendPercentage}%
            </Box>
          ) : (
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TrendingDownIcon fontSize="small" sx={{ mr: 0.5 }} />
              {trendPercentage}%
            </Box>
          )}
        </Box>
      </Box>

      {/* Line chart visualization */}
      <Box sx={{ flexGrow: 1, height: '150px', mt: 1, position: 'relative' }}>
        {/* Chart grid lines */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}>
          {[0, 1, 2, 3].map((i) => (
            <Box
              key={i}
              sx={{
                width: '100%',
                height: '1px',
                backgroundColor: '#f0f0f0',
              }}
            />
          ))}
        </Box>

        {/* Y-axis labels */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 20,
          width: '20px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
        }}>
          {[0, 1, 2, 3].map((i) => (
            <Typography
              key={i}
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: 'text.secondary',
                transform: 'translateY(-50%)',
              }}
            >
              {Math.round(maxValue * (3 - i) / 3)}
            </Typography>
          ))}
        </Box>

        {/* Line chart area */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 20,
          right: 0,
          bottom: 20,
          height: 'calc(100% - 20px)',
        }}>
          {/* Draw the line */}
          <svg width="100%" height="100%" style={{ overflow: 'visible' }}>
            {/* Area under the line */}
            <defs>
              <linearGradient id={`gradient-${title.replace(/\s+/g, '-')}`} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={color} stopOpacity="0.3" />
                <stop offset="100%" stopColor={color} stopOpacity="0.05" />
              </linearGradient>
            </defs>

            {/* Create the area under the line */}
            <path
              d={`
                M ${0} ${100 - (data[0] / maxValue) * 100}
                ${data.map((value, i) => {
                  const x = (i / (data.length - 1)) * 100;
                  const y = 100 - (value / maxValue) * 100;
                  return `L ${x} ${y}`;
                }).join(' ')}
                L ${100} ${100}
                L ${0} ${100}
                Z
              `}
              fill={`url(#gradient-${title.replace(/\s+/g, '-')})`}
              strokeWidth="0"
            />

            {/* Create the line */}
            <path
              d={`
                M ${0} ${100 - (data[0] / maxValue) * 100}
                ${data.map((value, i) => {
                  const x = (i / (data.length - 1)) * 100;
                  const y = 100 - (value / maxValue) * 100;
                  return `L ${x} ${y}`;
                }).join(' ')}
              `}
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Data points */}
            {data.map((value, i) => {
              const x = (i / (data.length - 1)) * 100;
              const y = 100 - (value / maxValue) * 100;
              const isHovered = hoveredIndex === i;

              return (
                <g key={i}>
                  <circle
                    cx={x}
                    cy={y}
                    r={isHovered ? 5 : 3}
                    fill={isHovered ? color : 'white'}
                    stroke={color}
                    strokeWidth="2"
                    style={{
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                    }}
                    onMouseEnter={() => setHoveredIndex(i)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  />

                  {isHovered && (
                    <g>
                      <rect
                        x={x - 20}
                        y={y - 25}
                        width="40"
                        height="20"
                        rx="4"
                        fill={color}
                      />
                      <text
                        x={x}
                        y={y - 12}
                        textAnchor="middle"
                        fill="white"
                        fontSize="10"
                        fontWeight="bold"
                      >
                        {value.toLocaleString()}
                      </text>
                    </g>
                  )}
                </g>
              );
            })}
          </svg>
        </Box>

        {/* X-axis labels */}
        <Box sx={{
          position: 'absolute',
          left: 20,
          right: 0,
          bottom: 0,
          height: '20px',
          display: 'flex',
          justifyContent: 'space-between',
        }}>
          {labels.map((label, index) => {
            const isHovered = hoveredIndex === index;
            return (
              <Typography
                key={index}
                variant="caption"
                sx={{
                  fontSize: '0.6rem',
                  color: isHovered ? color : 'text.secondary',
                  fontWeight: isHovered ? 'bold' : 'normal',
                  transition: 'all 0.3s ease',
                }}
              >
                {label}
              </Typography>
            );
          })}
        </Box>
      </Box>
    </Paper>
  );
};

export default ChartCard;
