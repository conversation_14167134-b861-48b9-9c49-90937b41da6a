"use client";

import React, { useState, useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";

import { LayoutContainer, MainContent } from "../(main)/layout.style";
import { Header } from "@/components/common/header/header";
import { Sidebar } from "@/components/common/sidebar/sidebar";
import { Footer } from "@/components/common/footer/footer";
import { Loading } from "@/components/common/loading";

interface SuperAdminLayoutProps {
  children: React.ReactNode;
}

const SuperAdminLayout = ({ children }: SuperAdminLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Create a stable reference to the toggle function that doesn't depend on state
  const toggleSidebarRef = React.useRef(() => {
    // Use direct DOM manipulation for maximum performance
    const sidebar = document.querySelector('.sidebar-container');
    const mainContent = document.querySelector('.main-content');

    if (sidebar && mainContent) {
      // Add transition class to enable smooth animations
      sidebar.classList.add('with-transition');
      mainContent.classList.add('with-transition');

      if (sidebar.classList.contains('icon-only')) {
        sidebar.classList.remove('icon-only');
        mainContent.classList.remove('sidebar-icon-only');
        // Update state without causing re-renders in the toggle function
        setTimeout(() => setSidebarOpen(true), 0);
      } else {
        sidebar.classList.add('icon-only');
        mainContent.classList.add('sidebar-icon-only');
        // Update state without causing re-renders in the toggle function
        setTimeout(() => setSidebarOpen(false), 0);
      }

      // Remove transition class after animation completes to prevent lag during scrolling
      setTimeout(() => {
        sidebar.classList.remove('with-transition');
        mainContent.classList.remove('with-transition');
      }, 500); // Slightly longer than the transition duration (0.45s)
    }
  });

  // Expose a stable function that never changes
  const toggleSidebar = React.useCallback(() => {
    toggleSidebarRef.current();
  }, []);

  // First, mark when component is mounted on client
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Show loading indicator when route changes
  useEffect(() => {
    // Only run on client side
    if (!isMounted) return;

    // Set a timeout to simulate loading and then hide it
    if (isLoading) {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 800); // Adjust timing as needed

      return () => clearTimeout(timer);
    }
  }, [isLoading, isMounted]);

  // Detect route changes - only run on client side
  useEffect(() => {
    // Only run on client side
    if (!isMounted) return;

    setIsLoading(true);
    // Loading state will be cleared by the timer in the other effect
  }, [pathname, searchParams, isMounted]);

  return (
    <LayoutContainer suppressHydrationWarning>
      <Header title="Super Admin" role="Super Admin" onToggleSidebar={toggleSidebar} />
      <Sidebar isOpen={sidebarOpen} />
      <MainContent
        className={`main-content ${sidebarOpen ? '' : 'sidebar-icon-only'}`}
        suppressHydrationWarning
      >
        <div className="content-wrapper">
          {children}
        </div>
      </MainContent>
      <Footer />
      {isMounted && <Loading isLoading={isLoading} message="Loading" />}
    </LayoutContainer>
  );
};

export default SuperAdminLayout;
