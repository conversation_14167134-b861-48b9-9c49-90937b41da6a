import { styled } from '@mui/material/styles';
import { Box, Paper } from '@mui/material';

export const ResolvedDisputesTableContainer = styled(Box)(({ theme }) => ({
  '& .MuiTableHead-root': {
    backgroundColor: '#3A52A6',
    '& .MuiTableCell-head': {
      color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
      fontWeight: 600,
      padding: '12px 16px',
    }
  },
  '& .MuiTableBody-root': {
    '& .MuiTableRow-root': {
      '&:nth-of-type(odd)': {
        backgroundColor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',
      },
      '&:hover': {
        backgroundColor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.04)' : 'rgba(255, 255, 255, 0.04)',
      },
    },
    '& .MuiTableCell-body': {
      padding: '8px 16px',
    }
  },
  '& .MuiTablePagination-root': {
    borderTop: `1px solid ${theme.palette.divider}`,
  }
}));

export const PaginationInfoText = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: '8px 16px',
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
}));

export const PaginationContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '8px 0',
  borderTop: `1px solid ${theme.palette.divider}`,
}));

export const PaginationNavigation = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  '& .MuiButton-root': {
    minWidth: '32px',
    padding: '4px 8px',
  }
}));
