"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useSearchParams } from "next/navigation";
import useMeasure from "react-use-measure";
import { useMediaQuery, useTheme } from "@mui/material";

import { IBreadcrumbDisplay } from "@/components/common";
import { RoutePathEnum } from "@/enum";
import { MeasureRefType } from "@/interfaces";

// Define the email data interface
interface EmailData {
  id: string;
  messageId: string;
  sendersEmail: string;
  receiversEmail: string;
  subject: string;
  createTime: string;
  securityStatus: {
    final: string;
    reason: string;
  };
  authentication: {
    dkim: boolean;
    spf: boolean;
    dmarc: boolean;
  };
  threatIntelligence: {
    databaseCheck: boolean;
  };
  contentAnalysis: {
    urls: Array<{
      url: string;
      status: string;
    }>;
    attachments: Array<{
      name: string;
      status: string;
    }>;
  };
  disputeHistory: Array<{
    date: string;
    userComment: string;
    adminResponse: string;
    adminStatus: string;
  }>;
  emailBody: string;
}

// Mock data for demonstration
const mockEmailData: EmailData = {
  id: "456",
  messageId: "f9b4c3e-da5c-1...",
  sendersEmail: "<EMAIL>",
  receiversEmail: "<EMAIL>",
  subject: "Subject 278",
  createTime: "01-01-2025 12:00 AM",
  securityStatus: {
    final: "UNSAFE",
    reason: "Status reflected by ai"
  },
  authentication: {
    dkim: true,
    spf: true,
    dmarc: true
  },
  threatIntelligence: {
    databaseCheck: true
  },
  contentAnalysis: {
    urls: [
      { url: "https://example.com/page123", status: "Malicious" },
      { url: "https://example.com/page456", status: "Safe" }
    ],
    attachments: [
      { name: "attachment/file_1.pdf", status: "Unsupported" },
      { name: "attachment/file_2.pdf", status: "Malicious" },
      { name: "attachment/file_3.pdf", status: "Safe" }
    ]
  },
  disputeHistory: [
    {
      date: "-",
      userComment: "No updates available",
      adminResponse: "No updates for this email",
      adminStatus: "None"
    }
  ],
  emailBody: "Email body content 123"
};

/**
 * Email Details Controller
 * @return {Object} Controller with getters and handlers
 */
export const EmailDetailsController = () => {
  const theme = useTheme();
  const isMobileView = useMediaQuery(theme.breakpoints.down("md"));
  const [ref, { height }] = useMeasure();
  const searchParams = useSearchParams();
  const emailId = searchParams.get('id') || '';
  
  // Tab state
  const [tabValue, setTabValue] = useState(0);
  
  // In a real application, you would fetch the email data based on the emailId
  // For now, we'll use the mock data
  const emailData = useMemo(() => mockEmailData, []);
  
  /**
   * Handle tab change
   */
  const handleTabChange = useCallback((_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  }, []);

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Phishing",
      path: RoutePathEnum.PHISHING,
      forwardParam: false,
    },
    {
      name: "Email Details",
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  return {
    getters: {
      breadcrumbs,
      emailData,
      tabValue,
      height,
      isMobileView,
      emailId,
    },
    handlers: {
      handleTabChange,
    },
    ref: ref as MeasureRefType,
  };
};
