"use client";

import React, { useState, useEffect, use } from "react";
import {
  Box,
  Tabs,
  Tab,
  Paper,
  Typography,
  Grid,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@mui/material";
import SecurityIcon from '@mui/icons-material/Security';
import HistoryIcon from '@mui/icons-material/History';
import EmailIcon from '@mui/icons-material/Email';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import { useThemeWithToggle } from '@/context/ThemeContext';
import { PageHeader } from "@/components/common";
import { DisputeDetailsController } from "./dispute-details.controller";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dispute-tabpanel-${index}`}
      aria-labelledby={`dispute-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `dispute-tab-${index}`,
    'aria-controls': `dispute-tabpanel-${index}`,
  };
}

export default function DisputeDetails({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const disputeId = unwrappedParams.id;

  const [tabValue, setTabValue] = useState(0);
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';
  const { getters } = DisputeDetailsController(disputeId);
  const { disputeData, breadcrumbs } = getters;

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (!disputeData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">Loading dispute details...</Typography>
      </Box>
    );
  }

  return (
    <>
      <PageHeader
        title={`Dispute Details #${disputeData.id}`}
        breadcrumbs={breadcrumbs}
      />

      <Paper elevation={0} sx={{ borderRadius: 1, overflow: 'hidden', mb: 3 }}>
        <Box sx={{
          px: 2,
          pt: 1.5,
          pb: 0.5,
          display: 'flex',
          alignItems: 'center',
          borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)"}`,
        }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.6)",
              mr: 2
            }}
          >
            Status:
          </Typography>
          <Chip
            label={disputeData.securityStatus.final}
            size="small"
            icon={disputeData.securityStatus.final === "SAFE" ? <CheckCircleIcon fontSize="small" /> : <CancelIcon fontSize="small" />}
            color={disputeData.securityStatus.final === "SAFE" ? "success" : "error"}
            sx={{ mr: 1, fontWeight: 500 }}
          />
        </Box>

        <Box sx={{ width: '100%' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="dispute details tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                px: 2,
                '& .MuiTab-root': {
                  minHeight: '48px',
                  fontSize: '0.85rem',
                  textTransform: 'none',
                  fontWeight: 500,
                }
              }}
            >
              <Tab
                icon={<SecurityIcon fontSize="small" sx={{ fontSize: '1rem' }} />}
                iconPosition="start"
                label="Details & Security"
                {...a11yProps(0)}
              />
              <Tab
                icon={<HistoryIcon fontSize="small" sx={{ fontSize: '1rem' }} />}
                iconPosition="start"
                label="Dispute History"
                {...a11yProps(1)}
              />
              <Tab
                icon={<EmailIcon fontSize="small" sx={{ fontSize: '1rem' }} />}
                iconPosition="start"
                label="Email Body"
                {...a11yProps(2)}
              />
            </Tabs>
          </Box>

          <Box sx={{ p: 2 }}>
            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                      borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                      borderRadius: '4px'
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                      Basic Information
                    </Typography>
                    <Box sx={{ pl: 0.5 }}>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>Sender:</strong> {disputeData.sendersEmail}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>Receiver:</strong> {disputeData.receiversEmail}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>Subject:</strong> {disputeData.subject}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>Created:</strong> {disputeData.createdAt}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                      borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                      borderRadius: '4px'
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                      Authentication
                    </Typography>
                    <Box sx={{ pl: 0.5 }}>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>DKIM:</strong> {disputeData.authentication.dkim ? 'Passed' : 'Failed'}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>SPF:</strong> {disputeData.authentication.spf ? 'Passed' : 'Failed'}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                        <strong>DMARC:</strong> {disputeData.authentication.dmarc ? 'Passed' : 'Failed'}
                      </Typography>
                    </Box>
                  </Paper>
                </Grid>

                <Grid item xs={12}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                      borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                      borderRadius: '4px'
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                      Content Analysis
                    </Typography>
                    <Box sx={{ pl: 0.5 }}>
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold', fontSize: '0.85rem' }}>
                        URLs:
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        {disputeData.contentAnalysis.urls.map((url, index) => (
                          <Chip
                            key={index}
                            label={url.url}
                            size="small"
                            color={url.status === "Safe" ? "success" : "error"}
                            sx={{ mr: 1, mb: 1, fontSize: '0.75rem' }}
                          />
                        ))}
                      </Box>
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold', fontSize: '0.85rem' }}>
                        Attachments:
                      </Typography>
                      <Box>
                        {disputeData.contentAnalysis.attachments.map((attachment, index) => (
                          <Chip
                            key={index}
                            label={attachment.name}
                            size="small"
                            color={attachment.status === "Safe" ? "success" : "error"}
                            sx={{ mr: 1, mb: 1, fontSize: '0.75rem' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  </Paper>
                </Grid>
              </Grid>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                  borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                  borderRadius: '4px'
                }}
              >
                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                  Dispute History
                </Typography>
                <Table size="small">
                  <TableBody>
                    {disputeData.disputeHistory.map((history, index) => (
                      <TableRow key={index} sx={{ '&:last-child td, &:last-child th': { border: 0 } }}>
                        <TableCell sx={{ width: '15%', fontSize: '0.8rem', pl: 0 }}>
                          {history.date}
                        </TableCell>
                        <TableCell sx={{ width: '30%', fontSize: '0.8rem' }}>
                          {history.userComment}
                        </TableCell>
                        <TableCell sx={{ width: '30%', fontSize: '0.8rem' }}>
                          {history.adminResponse}
                        </TableCell>
                        <TableCell sx={{ width: '15%', fontSize: '0.8rem' }}>
                          <Chip
                            label={history.adminStatus}
                            size="small"
                            color={history.adminStatus === "Safe" ? "success" : "error"}
                            sx={{ fontSize: '0.75rem' }}
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </Paper>
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                  borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                  borderRadius: '4px'
                }}
              >
                <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                  Email Content
                </Typography>
                <Box sx={{
                  p: 1.5,
                  bgcolor: isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.04)',
                  borderRadius: '4px',
                  maxHeight: '400px',
                  overflow: 'auto'
                }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.85rem' }}>
                    {disputeData.emailBody}
                  </Typography>
                </Box>
              </Paper>
            </TabPanel>
          </Box>
        </Box>
      </Paper>
    </>
  );
}
