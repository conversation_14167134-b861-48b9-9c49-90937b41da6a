import React from "react";
import { But<PERSON>, Tooltip } from "@mui/material";

interface RevokeButtonProps {
  onClick?: (value: string) => void;
  value: string;
  index: number;
}

export const RevokeButton: React.FC<RevokeButtonProps> = ({
  onClick,
  value,
  index,
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(value);
    }
  };

  return (
    <Button
      onClick={handleClick}
      size="small"
      variant="contained"
      color="error"
    >
      Revoke
    </Button>
  );
};

export default RevokeButton;
