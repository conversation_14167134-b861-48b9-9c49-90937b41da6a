"use client";

import React, { useState, use<PERSON>emo, use<PERSON><PERSON>back } from "react";
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  useTheme,
  IconButton,
  Autocomplete,
  Chip
} from "@mui/material";
import { PageHeader, SearchBar, TableComponent, ActiveColumn } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import { Icon } from "@/components/common/icon";
import {
  faEdit,
  faTrash,
  faPlus,
  faUser,
  faUserPlus,
  faEnvelope,
  faLock,
  faPhone,
  faBuilding,
  faUserTie
} from "@fortawesome/free-solid-svg-icons";

// Mock data for customers (for dropdown selection)
const mockCustomers = [
  { id: 1, name: "<PERSON>" },
  { id: 2, name: "<PERSON>edIn" },
  { id: 3, name: "Twitter" },
  { id: 4, name: "<PERSON><PERSON>" },
  { id: 5, name: "<PERSON>" },
  { id: 6, name: "<PERSON>" },
  { id: 7, name: "<PERSON>" },
  { id: 8, name: "Google" },
  { id: 9, name: "Facebook" },
  { id: 10, name: "Amazon" }
];

// Mock data for users
const mockUsers = [
  {
    id: 1,
    sn: 1,
    name: "John Doe",
    email: "<EMAIL>",
    phone: "7418523647",
    role: "Admin",
    customer: "Oracle",
    customerId: 1,
    status: "Active",
    createdAt: "19-05-2023 08:18 PM",
  },
  {
    id: 2,
    sn: 2,
    name: "Jane Smith",
    email: "<EMAIL>",
    phone: "7418523646",
    role: "User",
    customer: "LinkedIn",
    customerId: 2,
    status: "Active",
    createdAt: "19-05-2023 08:17 PM",
  },
  {
    id: 3,
    sn: 3,
    name: "Robert Johnson",
    email: "<EMAIL>",
    phone: "7418523645",
    role: "Admin",
    customer: "Twitter",
    customerId: 3,
    status: "Inactive",
    createdAt: "19-05-2023 08:15 PM",
  },
  {
    id: 4,
    sn: 4,
    name: "Emily Davis",
    email: "<EMAIL>",
    phone: "7418523699",
    role: "User",
    customer: "Tesla",
    customerId: 4,
    status: "Active",
    createdAt: "19-05-2023 08:14 PM",
  },
  {
    id: 5,
    sn: 5,
    name: "Michael Wilson",
    email: "<EMAIL>",
    phone: "7418523698",
    role: "Admin",
    customer: "IBM",
    customerId: 5,
    status: "Active",
    createdAt: "19-05-2023 08:12 PM",
  },
  {
    id: 6,
    sn: 6,
    name: "Sarah Brown",
    email: "<EMAIL>",
    phone: "7418523697",
    role: "User",
    customer: "Microsoft",
    customerId: 6,
    status: "Inactive",
    createdAt: "19-05-2023 08:10 PM",
  },
  {
    id: 7,
    sn: 7,
    name: "David Miller",
    email: "<EMAIL>",
    phone: "7418523696",
    role: "Admin",
    customer: "Apple",
    customerId: 7,
    status: "Active",
    createdAt: "19-05-2023 08:08 PM",
  },
  {
    id: 8,
    sn: 8,
    name: "Jennifer Taylor",
    email: "<EMAIL>",
    phone: "7418523695",
    role: "User",
    customer: "Google",
    customerId: 8,
    status: "Active",
    createdAt: "19-05-2023 08:05 PM",
  },
  {
    id: 9,
    sn: 9,
    name: "Thomas Anderson",
    email: "<EMAIL>",
    phone: "7418523694",
    role: "Admin",
    customer: "Facebook",
    customerId: 9,
    status: "Inactive",
    createdAt: "19-05-2023 08:03 PM",
  },
  {
    id: 10,
    sn: 10,
    name: "Lisa Martinez",
    email: "<EMAIL>",
    phone: "7418523693",
    role: "User",
    customer: "Amazon",
    customerId: 10,
    status: "Active",
    createdAt: "19-05-2023 08:01 PM",
  }
];

const UsersPage = () => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "sn",
    "name",
    "email",
    "phone",
    "role",
    "customer",
    "status",
    "actions",
  ]);

  // Form state for add/edit user
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    role: "User",
    customer: null as any,
    password: "",
    confirmPassword: ""
  });

  // Column options for the ActiveColumn component
  const columnOptions = [
    { value: "sn", label: "SN" },
    { value: "name", label: "Name" },
    { value: "email", label: "Email" },
    { value: "phone", label: "Phone" },
    { value: "role", label: "Role" },
    { value: "customer", label: "Customer" },
    { value: "status", label: "Status" },
    { value: "actions", label: "Actions" },
  ];

  // Filter options for the SearchBar component
  const filterOptions = [
    { value: "name", label: "Name" },
    { value: "email", label: "Email" },
    { value: "phone", label: "Phone" },
    { value: "customer", label: "Customer" },
  ];

  // Handle search
  const handleSearch = useCallback((value: string, filters: string[] = []) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page on search
  }, []);

  // Handle column visibility change
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  // Handle customer selection change
  const handleCustomerChange = useCallback((customer: any) => {
    setSelectedCustomer(customer);
    setPage(1); // Reset to first page on customer change
  }, []);

  // Handle delete
  const handleDelete = useCallback((user: any) => {
    setCurrentUser(user);
    setDeleteDialogOpen(true);
  }, []);

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Handle rows per page change
  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1); // Reset to first page when changing rows per page
  }, []);

  // Handle add user dialog open
  const handleAddUserClick = useCallback(() => {
    setCurrentUser(null);
    setFormData({
      name: "",
      email: "",
      phone: "",
      role: "User",
      customer: null,
      password: "",
      confirmPassword: ""
    });
    setAddUserDialogOpen(true);
  }, []);

  // Handle edit user
  const handleEditUser = useCallback((user: any) => {
    setCurrentUser(user);
    setFormData({
      name: user.name,
      email: user.email,
      phone: user.phone,
      role: user.role,
      customer: mockCustomers.find(c => c.id === user.customerId) || null,
      password: "",
      confirmPassword: ""
    });
    setAddUserDialogOpen(true);
  }, []);

  // Handle form input change
  const handleFormInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name as string]: value
    }));
  }, []);

  // Handle customer selection in form
  const handleFormCustomerChange = useCallback((_: any, value: any) => {
    setFormData(prev => ({
      ...prev,
      customer: value
    }));
  }, []);

  // Handle form submission
  const handleFormSubmit = useCallback(() => {
    // In a real app, you would save the changes here
    console.log("Form submitted:", formData);
    setAddUserDialogOpen(false);
  }, [formData]);

  // Filter data based on search term and selected customer
  const filteredData = useMemo(() => {
    let filtered = mockUsers;

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.phone.includes(searchLower) ||
        user.customer.toLowerCase().includes(searchLower)
      );
    }

    // Filter by selected customer
    if (selectedCustomer) {
      filtered = filtered.filter(user => user.customerId === selectedCustomer.id);
    }

    return filtered;
  }, [mockUsers, searchTerm, selectedCustomer]);

  // Calculate total pages
  const totalCount = filteredData.length;

  // Create table headers
  const headers = useMemo(() => [
    { id: "sn", label: "SN", sortable: true },
    { id: "name", label: "NAME", sortable: true },
    { id: "email", label: "EMAIL", sortable: true },
    { id: "phone", label: "PHONE", sortable: true },
    { id: "role", label: "ROLE", sortable: true,
      renderCell: (row: any) => (
        <Chip
          label={row.role}
          color={row.role === "Admin" ? "primary" : "default"}
          size="small"
        />
      )
    },
    { id: "customer", label: "CUSTOMER", sortable: true },
    { id: "status", label: "STATUS", sortable: true,
      renderCell: (row: any) => (
        <Chip
          label={row.status}
          color={row.status === "Active" ? "success" : "error"}
          size="small"
        />
      )
    },
    {
      id: "actions",
      label: "ACTIONS",
      sortable: false,
      renderCell: (row: any) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton
            size="small"
            onClick={() => handleEditUser(row)}
            sx={{ color: theme.palette.primary.main }}
          >
            <Icon icon={faEdit} size="small" onlyIcon />
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleDelete(row)}
            sx={{ color: theme.palette.error.main }}
          >
            <Icon icon={faTrash} size="small" onlyIcon />
          </IconButton>
        </Box>
      )
    },
  ], [theme.palette.primary.main, theme.palette.error.main, handleEditUser, handleDelete]);

  // Create header with search and column selector
  const header = useMemo(() => (
    <SuppressHydrationWarning>
      <PageHeader
        title="User Management"
        breadcrumbs={[
          { name: "Dashboard", path: "/dashboard", forwardParam: false },
          { name: "Super Admin", path: "/super-admin/dashboard", forwardParam: false },
          { name: "Users", path: "/super-admin/users", forwardParam: false },
        ]}
        actions={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SearchBar
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              onClear={() => handleSearch("")}
              sx={{ minWidth: 300 }}
            />
            <ActiveColumn
              columnOptions={columnOptions}
              visibleColumns={visibleColumns}
              onColumnVisibilityChange={handleColumnVisibilityChange}
              variant="popover"
            />
            <Autocomplete
              options={mockCustomers}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Customer"
                  variant="outlined"
                  size="small"
                />
              )}
              value={selectedCustomer}
              onChange={(_, newValue) => handleCustomerChange(newValue)}
              sx={{ minWidth: 200 }}
            />
            <Button
              variant="contained"
              startIcon={<Icon icon={faUserPlus} size="small" onlyIcon />}
              onClick={handleAddUserClick}
              size="small"
              sx={{
                bgcolor: '#3A52A6',
                '&:hover': {
                  bgcolor: '#2A3F8F',
                },
                padding: '6px 16px',
                fontWeight: 500,
              }}
            >
              Add User
            </Button>
          </Box>
        }
      />
    </SuppressHydrationWarning>
  ), [handleSearch, searchTerm, columnOptions, visibleColumns, handleColumnVisibilityChange, selectedCustomer, handleCustomerChange, handleAddUserClick]);

  // Create table component
  const table = useMemo(() => (
    <Paper elevation={0} sx={{ mt: 3, borderRadius: 1, overflow: 'hidden' }}>
      <SuppressHydrationWarning>
        <TableComponent<any>
          isLoading={false}
          headerField={headers}
          tableBody={filteredData}
          paginationData={{
            onPageChange: handlePageChange,
            onRowsPerPageChange: handleRowsPerPageChange,
            total: totalCount,
            page: page,
            limit: rowsPerPage,
          }}
          translation={{
            noDataTitle: "No Users Found",
          }}
          maxHeight={600}
          onRowClick={() => {}}
          visibleColumns={visibleColumns}
        />
      </SuppressHydrationWarning>
    </Paper>
  ), [
    headers,
    filteredData,
    handlePageChange,
    handleRowsPerPageChange,
    totalCount,
    page,
    rowsPerPage,
    visibleColumns,
  ]);

  return (
    <div>
      {header}
      {table}

      {/* Add/Edit User Dialog */}
      <Dialog open={addUserDialogOpen} onClose={() => setAddUserDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Icon icon={faUserPlus} size="small" />
            {currentUser ? "Edit User" : "Add New User"}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Full Name"
                name="name"
                value={formData.name}
                onChange={handleFormInputChange}
                fullWidth
                required
                InputProps={{
                  startAdornment: (
                    <Box sx={{ mr: 1, color: 'text.secondary' }}>
                      <Icon icon={faUser} size="small" onlyIcon />
                    </Box>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFormInputChange}
                fullWidth
                required
                InputProps={{
                  startAdornment: (
                    <Box sx={{ mr: 1, color: 'text.secondary' }}>
                      <Icon icon={faEnvelope} size="small" onlyIcon />
                    </Box>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Phone"
                name="phone"
                value={formData.phone}
                onChange={handleFormInputChange}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <Box sx={{ mr: 1, color: 'text.secondary' }}>
                      <Icon icon={faPhone} size="small" onlyIcon />
                    </Box>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  name="role"
                  value={formData.role}
                  onChange={handleFormInputChange}
                  label="Role"
                  startAdornment={
                    <Box sx={{ mr: 1, color: 'text.secondary' }}>
                      <Icon icon={faUserTie} size="small" onlyIcon />
                    </Box>
                  }
                >
                  <MenuItem value="Admin">Admin</MenuItem>
                  <MenuItem value="User">User</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Autocomplete
                options={mockCustomers}
                getOptionLabel={(option) => option.name}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Customer"
                    required
                    InputProps={{
                      ...params.InputProps,
                      startAdornment: (
                        <>
                          <Box sx={{ mr: 1, color: 'text.secondary' }}>
                            <Icon icon={faBuilding} size="small" onlyIcon />
                          </Box>
                          {params.InputProps.startAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                value={formData.customer}
                onChange={handleFormCustomerChange}
              />
            </Grid>
            {!currentUser && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="Password"
                    name="password"
                    type="password"
                    value={formData.password}
                    onChange={handleFormInputChange}
                    fullWidth
                    required={!currentUser}
                    InputProps={{
                      startAdornment: (
                        <Box sx={{ mr: 1, color: 'text.secondary' }}>
                          <Icon icon={faLock} size="small" onlyIcon />
                        </Box>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    label="Confirm Password"
                    name="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={handleFormInputChange}
                    fullWidth
                    required={!currentUser}
                    InputProps={{
                      startAdornment: (
                        <Box sx={{ mr: 1, color: 'text.secondary' }}>
                          <Icon icon={faLock} size="small" onlyIcon />
                        </Box>
                      ),
                    }}
                    error={formData.password !== formData.confirmPassword && formData.confirmPassword !== ""}
                    helperText={formData.password !== formData.confirmPassword && formData.confirmPassword !== "" ? "Passwords do not match" : ""}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setAddUserDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleFormSubmit}
            disabled={
              !formData.name ||
              !formData.email ||
              !formData.customer ||
              (!currentUser && (!formData.password || formData.password !== formData.confirmPassword))
            }
          >
            {currentUser ? "Save Changes" : "Add User"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete user "{currentUser?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => {
              // In a real app, you would delete the user here
              setDeleteDialogOpen(false);
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default UsersPage;