"use client";

import React, { useState, useMemo, useCallback } from "react";
import {
  Box,
  Paper,
  TextField,
  useTheme,
  Autocomplete,
  Chip
} from "@mui/material";
import { PageHeader, SearchBar, TableComponent, ActiveColumn } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";

// Empty data arrays - page cleared
const mockCustomers: any[] = [];
const mockUsers: any[] = [];

const UsersPage = () => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "sn",
    "name",
    "email",
    "phone",
    "role",
    "customer",
    "status",
  ]);

  // Column options for the ActiveColumn component
  const columnOptions = [
    { value: "sn", label: "SN" },
    { value: "name", label: "Name" },
    { value: "email", label: "Email" },
    { value: "phone", label: "Phone" },
    { value: "role", label: "Role" },
    { value: "customer", label: "Customer" },
    { value: "status", label: "Status" },
  ];

  // Filter options for the SearchBar component
  const filterOptions = [
    { value: "name", label: "Name" },
    { value: "email", label: "Email" },
    { value: "phone", label: "Phone" },
    { value: "customer", label: "Customer" },
  ];

  // Handle search
  const handleSearch = useCallback((value: string, filters: string[] = []) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page on search
  }, []);

  // Handle column visibility change
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  // Handle customer selection change
  const handleCustomerChange = useCallback((customer: any) => {
    setSelectedCustomer(customer);
    setPage(1); // Reset to first page on customer change
  }, []);

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Handle rows per page change
  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1); // Reset to first page when changing rows per page
  }, []);

  // Filter data based on search term and selected customer
  const filteredData = useMemo(() => {
    let filtered = mockUsers;

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(user =>
        user.name.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.phone.includes(searchLower) ||
        user.customer.toLowerCase().includes(searchLower)
      );
    }

    // Filter by selected customer
    if (selectedCustomer) {
      filtered = filtered.filter(user => user.customerId === selectedCustomer.id);
    }

    return filtered;
  }, [mockUsers, searchTerm, selectedCustomer]);

  // Calculate total pages
  const totalCount = filteredData.length;

  // Create table headers
  const headers = useMemo(() => [
    { id: "sn", label: "SN", sortable: true },
    { id: "name", label: "NAME", sortable: true },
    { id: "email", label: "EMAIL", sortable: true },
    { id: "phone", label: "PHONE", sortable: true },
    { id: "role", label: "ROLE", sortable: true,
      renderCell: (row: any) => (
        <Chip
          label={row.role}
          color={row.role === "Admin" ? "primary" : "default"}
          size="small"
        />
      )
    },
    { id: "customer", label: "CUSTOMER", sortable: true },
    { id: "status", label: "STATUS", sortable: true,
      renderCell: (row: any) => (
        <Chip
          label={row.status}
          color={row.status === "Active" ? "success" : "error"}
          size="small"
        />
      )
    },
  ], []);

  // Create header with search and column selector
  const header = useMemo(() => (
    <SuppressHydrationWarning>
      <PageHeader
        title="User Management"
        breadcrumbs={[
          { name: "Dashboard", path: "/dashboard", forwardParam: false },
          { name: "Super Admin", path: "/super-admin/dashboard", forwardParam: false },
          { name: "Users", path: "/super-admin/users", forwardParam: false },
        ]}
        actions={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SearchBar
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              onClear={() => handleSearch("")}
              sx={{ minWidth: 300 }}
            />
            <ActiveColumn
              columnOptions={columnOptions}
              visibleColumns={visibleColumns}
              onColumnVisibilityChange={handleColumnVisibilityChange}
              variant="popover"
            />
            <Autocomplete
              options={mockCustomers}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Customer"
                  variant="outlined"
                  size="small"
                />
              )}
              value={selectedCustomer}
              onChange={(_, newValue) => handleCustomerChange(newValue)}
              sx={{ minWidth: 200 }}
            />
          </Box>
        }
      />
    </SuppressHydrationWarning>
  ), [handleSearch, searchTerm, columnOptions, visibleColumns, handleColumnVisibilityChange, selectedCustomer, handleCustomerChange]);

  // Create table component
  const table = useMemo(() => (
    <Paper elevation={0} sx={{ mt: 3, borderRadius: 1, overflow: 'hidden' }}>
      <SuppressHydrationWarning>
        <TableComponent<any>
          isLoading={false}
          headerField={headers}
          tableBody={filteredData}
          paginationData={{
            onPageChange: handlePageChange,
            onRowsPerPageChange: handleRowsPerPageChange,
            total: totalCount,
            page: page,
            limit: rowsPerPage,
          }}
          translation={{
            noDataTitle: "No Users Found",
          }}
          maxHeight={600}
          onRowClick={() => {}}
          visibleColumns={visibleColumns}
        />
      </SuppressHydrationWarning>
    </Paper>
  ), [
    headers,
    filteredData,
    handlePageChange,
    handleRowsPerPageChange,
    totalCount,
    page,
    rowsPerPage,
    visibleColumns,
  ]);

  return (
    <div>
      {header}
      {table}
    </div>
  );
};

export default UsersPage;