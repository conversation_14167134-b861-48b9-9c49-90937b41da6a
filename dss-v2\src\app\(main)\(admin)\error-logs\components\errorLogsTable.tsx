import React, { useState, useCallback, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableFooter,
  Paper,
  Box,
} from '@mui/material';
import { ViewButton } from './viewButton';
import { CustomTablePagination } from '@/components/common/table/tablePagination';
import { CommonTableStyle } from '@/components/common/table/commonTableStyle';
import { useColumnVisibility } from '@/hooks';

interface ErrorLogData {
  id: string;
  user: string;
  path: string;
  method: string;
  errorType: string;
  errorMessage: string;
  timestamp: string;
  traceback: string;
}

interface ErrorLogsTableProps {
  data: ErrorLogData[];
  visibleColumns?: string[];
}

export const ErrorLogsTable: React.FC<ErrorLogsTableProps> = ({
  data,
  visibleColumns = ['sn', 'user', 'path', 'method', 'errorType', 'errorMessage', 'timestamp', 'traceback']
}) => {
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
  }, []);

  // Calculate the current page data
  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentPageData = data.slice(startIndex, endIndex);

  return (
    <Box sx={{ width: '100%' }}>
      <CommonTableStyle elevation={0}>
        <Table sx={{ minWidth: 650 }} aria-label="error logs table">
          <TableHead>
            <TableRow sx={{ backgroundColor: '#3A52A6' }}>
              {visibleColumns.includes('sn') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '5%' })}>SN</TableCell>
              )}
              {visibleColumns.includes('user') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>USER</TableCell>
              )}
              {visibleColumns.includes('path') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '20%' })}>PATH</TableCell>
              )}
              {visibleColumns.includes('method') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '8%' })}>METHOD</TableCell>
              )}
              {visibleColumns.includes('errorType') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '12%' })}>ERROR TYPE</TableCell>
              )}
              {visibleColumns.includes('errorMessage') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '20%' })}>ERROR MESSAGE</TableCell>
              )}
              {visibleColumns.includes('timestamp') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>TIMESTAMP</TableCell>
              )}
              {visibleColumns.includes('traceback') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>TRACEBACK</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {currentPageData.map((row, index) => (
              <TableRow
                key={row.id}
                sx={{
                  '&:nth-of-type(odd)': { backgroundColor: '#f9f9f9' },
                  '&:hover': { backgroundColor: '#f1f1f1' },
                }}
              >
                {visibleColumns.includes('sn') && (
                  <TableCell>{startIndex + index + 1}</TableCell>
                )}
                {visibleColumns.includes('user') && (
                  <TableCell>{row.user}</TableCell>
                )}
                {visibleColumns.includes('path') && (
                  <TableCell sx={{ wordBreak: 'break-all' }}>{row.path}</TableCell>
                )}
                {visibleColumns.includes('method') && (
                  <TableCell>{row.method}</TableCell>
                )}
                {visibleColumns.includes('errorType') && (
                  <TableCell>{row.errorType}</TableCell>
                )}
                {visibleColumns.includes('errorMessage') && (
                  <TableCell sx={{ wordBreak: 'break-all' }}>{row.errorMessage}</TableCell>
                )}
                {visibleColumns.includes('timestamp') && (
                  <TableCell>{row.timestamp}</TableCell>
                )}
                {visibleColumns.includes('traceback') && (
                  <TableCell>
                    <ViewButton errorId={row.id} />
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CommonTableStyle>

      {/* Pagination */}
      <Table>
        <TableFooter>
          <TableRow>
            <CustomTablePagination
              page={page}
              limit={rowsPerPage}
              total={data.length}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </Box>
  );
};

export default ErrorLogsTable;
