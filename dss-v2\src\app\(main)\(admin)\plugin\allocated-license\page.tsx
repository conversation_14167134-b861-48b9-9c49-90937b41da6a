"use client";

import React, { JSX, useMemo } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>ton, Box } from "@mui/material";

import { AllocatedLicenseController } from "./allocated-license.controller";
import RevokeLicenseModal from "./components/revokeButton/revokeLicenseModal";
import AllocatedLicenseFilterButton from "./components/allocatedLicenseFilterButton";
import { PageHeader, TableComponent } from "@/components/common";
import { SearchBar } from "@/components/common/searchBar";
import { ActiveColumn } from "@/components/common/activeColumn";

/**
 * @page {AllocatedLicense} - Display Allocated License Information
 * @return {JSX.Element}
 */
export default function AllocatedLicense(): JSX.Element {
  const router = useRouter();
  const { getters, handlers, ref } = AllocatedLicenseController();
  const {
    headers,
    tablePaginationData,
    newApplication,
    contactPagination,
    height,
    breadcrumbs,
    selectedLicenses,
    COLUMN_OPTIONS,
    visibleColumns,
    isRevokeModalOpen,
    selectedLicenseData,
    searchTerm,
  } = getters;
  const {
    changePage,
    changeRows,
    handleSearch,
    handleRevoke,
    handleSelectLicense,
    handleBulkRevoke,
    handleColumnVisibilityChange,
    handleCloseRevokeModal,
    handleRevokeSubmit,
  } = handlers;

  const table = useMemo(
    () => (
      <TableComponent<any>
        isLoading={false}
        headerField={headers}
        tableBody={newApplication}
        paginationData={{
          onPageChange: changePage,
          onRowsPerPageChange: changeRows,
          total: contactPagination.totalCount,
          page: tablePaginationData.page,
          limit: tablePaginationData.limit,
        }}
        translation={{
          noDataTitle: "No Data Found",
        }}
        maxHeight={height}
        onRowClick={() => {}}
        visibleColumns={visibleColumns}
        componentProps={{
          revoke: {
            onClick: handleRevoke,
          },
          checkbox: {
            onChange: handleSelectLicense,
            isSelected: (value: string) => selectedLicenses.includes(value),
          },
        }}
      />
    ),
    [
      tablePaginationData.page,
      tablePaginationData.limit,
      headers,
      newApplication,
      changePage,
      changeRows,
      contactPagination.totalCount,
      height,
      handleRevoke,
      handleSelectLicense,
      selectedLicenses,
      visibleColumns,
    ]
  );
  const FILTER_OPTIONS = [
    { label: "License ID", value: "licenseId" },
    { label: "Allocated To", value: "allocatedTo" },
    { label: "MAC Address", value: "macAddress" },
    { label: "Validity From", value: "validityFrom" },
    { label: "Validity Till", value: "validityTill" },
    { label: "Issue", value: "issue" },
  ];
  const bulkRevokeButton = useMemo(() => {
    if (selectedLicenses.length === 0) return null;

    return (
      <Button
        variant="contained"
        color="error"
        size="small"
        onClick={handleBulkRevoke}
      >
        Revoke All ({selectedLicenses.length})
      </Button>
    );
  }, [selectedLicenses.length, handleBulkRevoke]);

  const customSearchBar = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={handleSearch}
          placeholder="Search allocated licenses..."
          actionButton={bulkRevokeButton}
          filterOptions={FILTER_OPTIONS}
          useAdvancedFilter={false}
        />
        <AllocatedLicenseFilterButton
          filterOptions={FILTER_OPTIONS.map(option => ({
            ...option,
            type: option.value.includes('validity') ? 'date' : 'text'
          }))}
          onApplyFilters={(filters) => handleSearch(searchTerm, filters)}
          searchTerm={searchTerm}
        />
        <ActiveColumn
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          variant="popover"
        />
      </Box>
    );
  }, [handleSearch, bulkRevokeButton, FILTER_OPTIONS, COLUMN_OPTIONS, visibleColumns, handleColumnVisibilityChange, searchTerm]);

  const header = useMemo(
    () => (
      <div ref={ref}>
        <PageHeader
          title="Allocated License"
          breadcrumbs={breadcrumbs}
          actions={customSearchBar}
        />
      </div>
    ),
    [breadcrumbs, ref, customSearchBar]
  );

  return (
    <>
      {header}
      {table}

      {/* Revoke License Modal */}
      <RevokeLicenseModal
        open={isRevokeModalOpen}
        onClose={handleCloseRevokeModal}
        onRevoke={handleRevokeSubmit}
        licenseData={selectedLicenseData}
      />
    </>
  );
}
