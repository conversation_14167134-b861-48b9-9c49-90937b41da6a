"use client";

import React, { JSX, useMemo } from "react";
import { Paper, Box } from "@mui/material";

import { PhishingController } from "./phishing.controller";
import { PhishingTableContainer } from "./components/phishingTable.style";
import { PageHeader, TableComponent, SearchBar } from "@/components/common";
import { TableFilterButton } from "@/components/common/tableFilter";
import { ActiveColumn } from "@/components/common/activeColumn";
import ViewDetailsButton from "./components/viewDetailsButton";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";

/**
 * @page {Phishing} - Display Phishing Emails Information
 * @return {JSX.Element}
 */
export default function Phishing(): JSX.Element {
  const { getters, handlers, ref } = PhishingController();
  const {
    headers,
    tablePaginationData,
    newApplication,
    contactPagination,
    height,
    breadcrumbs,
    FILTER_OPTIONS,
    COLUMN_OPTIONS,
    visibleColumns,
    searchTerm,
  } = getters;
  const { changePage, changeRows, handleSearch, handleColumnVisibilityChange } = handlers;

  const customSearchBar = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={handleSearch}
          placeholder="Search phishing emails..."
          filterOptions={FILTER_OPTIONS}
          useAdvancedFilter={false}
        />
        <TableFilterButton
          filterOptions={FILTER_OPTIONS}
          onApplyFilters={(filters) => handleSearch(searchTerm, filters)}
          buttonLabel="Filter"
          title="Phishing Filters"
        />
        <ActiveColumn
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          variant="popover"
        />
      </Box>
    );
  }, [handleSearch, FILTER_OPTIONS, COLUMN_OPTIONS, visibleColumns, handleColumnVisibilityChange, searchTerm]);

  const table = useMemo(
    () => (
      <Paper elevation={0} sx={{ borderRadius: 1, overflow: 'hidden' }}>
        <PhishingTableContainer>
          <TableComponent<any>
            isLoading={false}
            headerField={headers}
            tableBody={newApplication}
            paginationData={{
              onPageChange: changePage,
              onRowsPerPageChange: changeRows,
              total: contactPagination.totalCount,
              page: tablePaginationData.page,
              limit: tablePaginationData.limit,
            }}
            translation={{
              noDataTitle: "No Data Found",
            }}
            maxHeight={height}
            onRowClick={() => {}}
            visibleColumns={visibleColumns}
            componentProps={{
              details: {
                onClick: () => {}, // The onClick is now handled directly in the ViewDetailsButton component
              },
            }}
          />
        </PhishingTableContainer>
      </Paper>
    ),
    [
      tablePaginationData.page,
      tablePaginationData.limit,
      headers,
      newApplication,
      changePage,
      changeRows,
      contactPagination.totalCount,
      contactPagination.totalPages,
      height,
      visibleColumns
    ]
  );

  const header = useMemo(
    () => (
      <SuppressHydrationWarning>
        <div ref={ref}>
          <PageHeader
            title="Phishing Mails"
            breadcrumbs={breadcrumbs}
            actions={customSearchBar}
          />
        </div>
      </SuppressHydrationWarning>
    ),
    [breadcrumbs, ref, customSearchBar]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
}
