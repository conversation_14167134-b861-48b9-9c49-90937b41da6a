"use client";

import React, { useState, useMemo } from 'react';
import { Box, Paper } from '@mui/material';
import { PageHeader } from '@/components/common/pageHeader/pageHeader';
import { ExceptionLogsController } from './exception-logs.controller';
import { ExceptionLogsTable } from './components/exceptionLogsTable';
import { SearchBar } from './components/searchBar';
import ExceptionLogsFilterButton from './components/exceptionLogsFilterButton';
import { SuppressHydrationWarning } from '@/components/common/suppressHydrationWarning';

// Mock data for the exception logs table
const mockExceptionLogsData = [
  {
    id: '1',
    user: 'Anonymous',
    path: '/media/cdr_files/cdr_19536bd19a90863f_dummy-pdf_2.pdf',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '"/app/media/cdr_files/cdr_19536bd19a90863f_dummy-pdf_2.pdf" does not exist',
    timestamp: '26-02-2025 01:25 PM',
    traceback: `Traceback (most recent call last):
  File "/usr/local/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/views/static.py", line 40, in serve
    raise Http404(_("%(path)s does not exist") % {"path": fullpath})
django.http.response.Http404: "/app/media/cdr_files/cdr_19536bd19a90863f_dummy-pdf_2.pdf" does not exist`,
  },
  {
    id: '2',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '26-02-2025 11:40 AM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '3',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 04:22 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '4',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 03:48 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '5',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 03:47 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '6',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 01:04 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '7',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 12:54 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '8',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 11:51 AM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '9',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '24-02-2025 11:23 AM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
  {
    id: '10',
    user: 'Anonymous',
    path: '/media/attachments/...',
    method: 'GET',
    exceptionType: 'Http404',
    exceptionMessage: '/app/media/attachments/...',
    timestamp: '21-02-2025 04:34 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
  },
];

const Page = () => {
  const { getters, handlers } = ExceptionLogsController();
  const {
    breadcrumbs,
    COLUMN_OPTIONS,
    visibleColumns,
    initialVisibleColumns
  } = getters;
  const { handleColumnVisibilityChange } = handlers;
  const [filteredData, setFilteredData] = useState(mockExceptionLogsData);
  const [searchTerm, setSearchTerm] = useState("");

  const handleSearch = (query: string, filters: string[]) => {
    setSearchTerm(query);

    if (!query) {
      setFilteredData(mockExceptionLogsData);
      return;
    }

    const lowercasedQuery = query.toLowerCase();
    const filtered = mockExceptionLogsData.filter((item) => {
      if (filters.length === 0) {
        return (
          item.user.toLowerCase().includes(lowercasedQuery) ||
          item.path.toLowerCase().includes(lowercasedQuery) ||
          item.method.toLowerCase().includes(lowercasedQuery) ||
          item.exceptionType.toLowerCase().includes(lowercasedQuery) ||
          item.exceptionMessage.toLowerCase().includes(lowercasedQuery) ||
          item.timestamp.toLowerCase().includes(lowercasedQuery)
        );
      }

      return filters.some((filter) => {
        const value = item[filter as keyof typeof item];
        return value?.toString().toLowerCase().includes(lowercasedQuery);
      });
    });

    setFilteredData(filtered);
  };

  // Export functionality removed

  const customSearchBar = useMemo(
    () => (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={handleSearch}
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
        />
        <ExceptionLogsFilterButton
          onApplyFilters={(filters) => handleSearch(searchTerm, filters)}
          searchTerm={searchTerm}
        />
      </Box>
    ),
    [
      handleSearch,
      COLUMN_OPTIONS,
      visibleColumns,
      handleColumnVisibilityChange,
      searchTerm
    ]
  );

  const header = useMemo(
    () => (
      <SuppressHydrationWarning>
        <div>
          <PageHeader
            title="Exception Logs"
            breadcrumbs={breadcrumbs}
            actions={customSearchBar}
          />
        </div>
      </SuppressHydrationWarning>
    ),
    [breadcrumbs, customSearchBar]
  );

  const table = useMemo(
    () => (
      <SuppressHydrationWarning>
        <Box sx={{ mt: 3, px: 3 }}>
          <Paper elevation={0} sx={{ borderRadius: 1, overflow: 'hidden' }}>
            <ExceptionLogsTable
              data={filteredData}
              visibleColumns={visibleColumns}
            />
          </Paper>
        </Box>
      </SuppressHydrationWarning>
    ),
    [filteredData, visibleColumns]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
};

export default Page;
