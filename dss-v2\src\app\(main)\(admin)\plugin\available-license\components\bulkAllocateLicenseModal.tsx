"use client";

import React, { useState, useCallback, useRef } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  useTheme,
  Link,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { styled } from "@mui/material/styles";

interface BulkAllocateLicenseModalProps {
  open: boolean;
  onClose: () => void;
  onAllocate: (data: File) => void;
}

// Styled components for the dropzone
const DropzoneContainer = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.2)'}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
  transition: 'all 0.3s ease',
  '&:hover': {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
    borderColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.3)',
  },
}));

const UploadIcon = styled(CloudUploadIcon)(({ theme }) => ({
  fontSize: 48,
  color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.3)',
  marginBottom: theme.spacing(2),
}));

const HiddenInput = styled('input')({
  display: 'none',
});

export const BulkAllocateLicenseModal: React.FC<BulkAllocateLicenseModalProps> = ({
  open,
  onClose,
  onAllocate,
}) => {

  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [file, setFile] = useState<File | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle file selection
  const handleFileChange = (selectedFile: File | null) => {
    setError(null);

    if (!selectedFile) {
      return;
    }

    // Check if it's a CSV file
    if (!selectedFile.name.toLowerCase().endsWith('.csv')) {
      setError('Please upload a CSV file');
      return;
    }

    setFile(selectedFile);
  };

  // Handle file input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0] || null;
    handleFileChange(selectedFile);
  };

  // Handle click on dropzone
  const handleDropzoneClick = () => {
    fileInputRef.current?.click();
  };

  // Handle drag events
  const handleDragEnter = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const droppedFile = e.dataTransfer.files?.[0] || null;
    handleFileChange(droppedFile);
  }, []);

  // Handle form submission
  const handleSubmit = () => {
    if (!file) {
      setError('Please select a CSV file');
      return;
    }

    onAllocate(file);
    resetForm();
  };

  // Reset form state
  const resetForm = () => {
    setFile(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle modal close
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Generate CSV template
  const handleDownloadTemplate = () => {
    const csvContent = "license_id,allocated_to,mac_address\n";
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'license_allocation_template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 1,
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
        }
      }}
    >
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
        pb: 1
      }}>
        <Typography variant="h6" component="div" sx={{
          fontWeight: 600,
          color: isDarkMode ? "#fff" : "#000"
        }}>
          Allocate Multiple Licenses
        </Typography>
        <IconButton edge="end" color="inherit" onClick={handleClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 3 }}>
        <Typography variant="body2" sx={{ mb: 3, color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.6)" }}>
          Upload a CSV file containing license allocations
        </Typography>

        <DropzoneContainer
          onClick={handleDropzoneClick}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          sx={{
            borderColor: isDragging
              ? (isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.5)')
              : (file ? (isDarkMode ? 'rgba(58, 82, 166, 0.7)' : 'rgba(58, 82, 166, 0.5)') : undefined),
            backgroundColor: isDragging
              ? (isDarkMode ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.08)')
              : (file ? (isDarkMode ? 'rgba(58, 82, 166, 0.1)' : 'rgba(58, 82, 166, 0.05)') : undefined),
          }}
        >
          <HiddenInput
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleInputChange}
          />

          <UploadIcon
            sx={{
              color: file
                ? '#3A52A6'
                : (isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.3)')
            }}
          />

          <Typography variant="body1" align="center" sx={{ mb: 1 }}>
            {file ? file.name : 'Drag and drop your CSV file here, or browse'}
          </Typography>

          {!file && (
            <Typography variant="body2" align="center" color="textSecondary">
              CSV file should contain the following columns:
              <br />
              <Box component="span" sx={{ fontFamily: 'monospace', color: isDarkMode ? '#90caf9' : '#1976d2' }}>
                license_id, allocated_to, mac_address
              </Box>
            </Typography>
          )}

          {error && (
            <Typography variant="body2" color="error" align="center" sx={{ mt: 1 }}>
              {error}
            </Typography>
          )}
        </DropzoneContainer>

        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="textSecondary">
            Need a template? <Link href="#" onClick={handleDownloadTemplate} underline="hover">Download CSV template</Link>
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions sx={{
        px: 3,
        py: 2,
        borderTop: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
        display: "flex",
        justifyContent: "space-between"
      }}>
        <Button
          onClick={handleClose}
          variant="outlined"
          sx={{
            color: isDarkMode ? "#fff" : "rgba(0, 0, 0, 0.7)",
            borderColor: isDarkMode ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.23)",
            "&:hover": {
              borderColor: isDarkMode ? "rgba(255, 255, 255, 0.5)" : "rgba(0, 0, 0, 0.5)",
              backgroundColor: isDarkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)"
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={!file}
          sx={{
            backgroundColor: "#3A52A6",
            "&:hover": {
              backgroundColor: "#2A3F8F"
            },
            "&.Mui-disabled": {
              backgroundColor: isDarkMode ? "rgba(255, 255, 255, 0.12)" : "rgba(0, 0, 0, 0.12)",
              color: isDarkMode ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.26)"
            }
          }}
        >
          Upload and Allocate
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkAllocateLicenseModal;
