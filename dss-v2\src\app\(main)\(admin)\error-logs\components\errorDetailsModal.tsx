"use client";

import React from 'react';
import {
  Dialog,
  DialogContent,
  IconButton,
  Typography,
  Box,
  Divider,
  Paper,
  useTheme
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LinkIcon from '@mui/icons-material/Link';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import CodeIcon from '@mui/icons-material/Code';
import ErrorIcon from '@mui/icons-material/Error';

interface ErrorDetailsModalProps {
  open: boolean;
  onClose: () => void;
  errorData: {
    errorType: string;
    timestamp: string;
    path: string;
    method: string;
    user: string;
    errorMessage: string;
    traceback?: string;
  };
}

const ErrorDetailsModal: React.FC<ErrorDetailsModalProps> = ({
  open,
  onClose,
  errorData
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: isDarkMode 
            ? '0 8px 32px rgba(0, 0, 0, 0.4)' 
            : '0 8px 32px rgba(0, 0, 0, 0.1)',
          backgroundColor: isDarkMode ? '#1e1e1e' : '#ffffff',
          overflow: 'hidden'
        }
      }}
    >
      {/* Timestamp at the top */}
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center',
          px: 3, 
          py: 2,
        }}
      >
        <AccessTimeIcon 
          sx={{ 
            mr: 1, 
            color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)' 
          }} 
          fontSize="small" 
        />
        <Typography 
          variant="body2" 
          sx={{ 
            color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)' 
          }}
        >
          {errorData.timestamp}
        </Typography>
        <Box sx={{ flexGrow: 1 }} />
        <IconButton
          edge="end"
          onClick={onClose}
          aria-label="close"
          size="small"
        >
          <CloseIcon />
        </IconButton>
      </Box>

      <DialogContent sx={{ p: 3, pt: 0 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            p: 2, 
            mb: 3, 
            borderRadius: 2,
            backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
            border: '1px solid',
            borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)'
          }}
        >
          {/* Path */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <LinkIcon 
              sx={{ 
                mr: 1.5, 
                mt: 0.3,
                color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)' 
              }} 
              fontSize="small" 
            />
            <Box>
              <Typography 
                variant="body2" 
                sx={{ 
                  fontWeight: 600,
                  mb: 0.5,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                Path
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                  wordBreak: 'break-all'
                }}
              >
                {errorData.path}
              </Typography>
            </Box>
          </Box>

          {/* Method */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <CodeIcon 
              sx={{ 
                mr: 1.5, 
                mt: 0.3,
                color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)' 
              }} 
              fontSize="small" 
            />
            <Box>
              <Typography 
                variant="body2" 
                sx={{ 
                  fontWeight: 600,
                  mb: 0.5,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                Method
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                  fontFamily: 'monospace',
                  backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                  px: 1,
                  py: 0.5,
                  borderRadius: 0.5,
                  display: 'inline-block'
                }}
              >
                {errorData.method}
              </Typography>
            </Box>
          </Box>

          {/* User */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <PersonOutlineIcon 
              sx={{ 
                mr: 1.5, 
                mt: 0.3,
                color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)' 
              }} 
              fontSize="small" 
            />
            <Box>
              <Typography 
                variant="body2" 
                sx={{ 
                  fontWeight: 600,
                  mb: 0.5,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                User
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                }}
              >
                {errorData.user}
              </Typography>
            </Box>
          </Box>

          {/* Message */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
            <ErrorIcon 
              sx={{ 
                mr: 1.5, 
                mt: 0.3,
                color: theme.palette.error.main
              }} 
              fontSize="small" 
            />
            <Box>
              <Typography 
                variant="body2" 
                sx={{ 
                  fontWeight: 600,
                  mb: 0.5,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                Message
              </Typography>
              <Typography 
                variant="body2" 
                sx={{ 
                  color: theme.palette.error.main,
                  wordBreak: 'break-all'
                }}
              >
                {errorData.errorMessage}
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* HTTP Error */}
        <Box sx={{ mb: 1 }}>
          <Typography 
            variant="subtitle2" 
            sx={{ 
              fontWeight: 600,
              mb: 1,
              display: 'flex',
              alignItems: 'center',
              color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
            }}
          >
            <CodeIcon sx={{ mr: 1, fontSize: '1rem' }} />
            HTTP Error
          </Typography>
        </Box>
        <Paper
          elevation={0}
          sx={{
            backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.03)',
            borderRadius: 2,
            p: 2,
            border: '1px solid',
            borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            lineHeight: 1.5,
            whiteSpace: 'pre-wrap',
            overflowX: 'auto',
            color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
          }}
        >
          HTTP Error
        </Paper>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorDetailsModal;
