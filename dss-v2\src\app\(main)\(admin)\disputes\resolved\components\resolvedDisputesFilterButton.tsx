"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import DoneAllIcon from '@mui/icons-material/DoneAll';

interface ResolvedDisputesFilterButtonProps {
  filterOptions: { label: string; value: string; type?: 'text' | 'date' | 'email' | 'number' }[];
  onApplyFilters: (filters: string[]) => void;
  searchTerm: string;
}

const ResolvedDisputesFilterButton: React.FC<ResolvedDisputesFilterButtonProps> = ({
  filterOptions,
  onApplyFilters,
  searchTerm
}) => {

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Resolved Disputes Filters"
      title="Resolved Disputes Filters"
      description="Apply filters to refine your resolved disputes search results"
      icon={<DoneAllIcon />}
      searchTerm={searchTerm}
    />
  );
};

export default ResolvedDisputesFilterButton;
