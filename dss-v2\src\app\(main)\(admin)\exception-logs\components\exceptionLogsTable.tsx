import React, { useState, useCallback } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableFooter,
} from '@mui/material';
import { ViewButton } from './viewButton';
import { CustomTablePagination } from '@/components/common/table/tablePagination';
import { CommonTableStyle } from '@/components/common/table/commonTableStyle';

interface ExceptionLogData {
  id: string;
  user: string;
  path: string;
  method: string;
  exceptionType: string;
  exceptionMessage: string;
  timestamp: string;
  traceback: string;
}

interface ExceptionLogsTableProps {
  data: ExceptionLogData[];
  visibleColumns?: string[];
}

export const ExceptionLogsTable: React.FC<ExceptionLogsTableProps> = ({
  data,
  visibleColumns = ['sn', 'user', 'path', 'method', 'exceptionType', 'exceptionMessage', 'timestamp', 'traceback']
}) => {
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
  }, []);

  // Calculate the current page data
  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentPageData = data.slice(startIndex, endIndex);

  return (
    <Box sx={{ width: '100%' }}>
      <CommonTableStyle elevation={0}>
        <Table sx={{ minWidth: 650 }} aria-label="exception logs table">
          <TableHead>
            <TableRow sx={{ backgroundColor: '#3A52A6' }}>
              {visibleColumns.includes('sn') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '5%' })}>SN</TableCell>
              )}
              {visibleColumns.includes('user') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>USER</TableCell>
              )}
              {visibleColumns.includes('path') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '20%' })}>PATH</TableCell>
              )}
              {visibleColumns.includes('method') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '8%' })}>METHOD</TableCell>
              )}
              {visibleColumns.includes('exceptionType') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '12%' })}>EXCEPTION TYPE</TableCell>
              )}
              {visibleColumns.includes('exceptionMessage') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '20%' })}>EXCEPTION MESSAGE</TableCell>
              )}
              {visibleColumns.includes('timestamp') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>TIMESTAMP</TableCell>
              )}
              {visibleColumns.includes('traceback') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '10%' })}>TRACEBACK</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {currentPageData.map((row, index) => (
              <TableRow
                key={row.id}
                sx={{
                  '&:nth-of-type(odd)': { backgroundColor: '#f9f9f9' },
                  '&:hover': { backgroundColor: '#f1f1f1' },
                }}
              >
                {visibleColumns.includes('sn') && (
                  <TableCell>{startIndex + index + 1}</TableCell>
                )}
                {visibleColumns.includes('user') && (
                  <TableCell>{row.user}</TableCell>
                )}
                {visibleColumns.includes('path') && (
                  <TableCell sx={{ wordBreak: 'break-all' }}>{row.path}</TableCell>
                )}
                {visibleColumns.includes('method') && (
                  <TableCell>{row.method}</TableCell>
                )}
                {visibleColumns.includes('exceptionType') && (
                  <TableCell>{row.exceptionType}</TableCell>
                )}
                {visibleColumns.includes('exceptionMessage') && (
                  <TableCell sx={{ wordBreak: 'break-all' }}>{row.exceptionMessage}</TableCell>
                )}
                {visibleColumns.includes('timestamp') && (
                  <TableCell>{row.timestamp}</TableCell>
                )}
                {visibleColumns.includes('traceback') && (
                  <TableCell>
                    <ViewButton exceptionId={row.id} />
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CommonTableStyle>

      {/* Pagination */}
      <Table>
        <TableFooter>
          <TableRow>
            <CustomTablePagination
              page={page}
              limit={rowsPerPage}
              total={data.length}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </Box>
  );
};

export default ExceptionLogsTable;
