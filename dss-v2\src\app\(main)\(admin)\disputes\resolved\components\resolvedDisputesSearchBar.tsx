import React, { useState } from 'react';
import { 
  Box, 
  TextField, 
  InputAdornment, 
  IconButton, 
  Button, 
  Menu, 
  MenuItem, 
  Checkbox, 
  ListItemText,
  Popover,
  Typography,
  Divider
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';

interface ResolvedDisputesSearchBarProps {
  onSearch: (term: string, filters: string[]) => void;
  placeholder?: string;
}

const ResolvedDisputesSearchBar: React.FC<ResolvedDisputesSearchBarProps> = ({ 
  onSearch,
  placeholder = "Search..." 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState<string[]>([
    'sendersEmail',
    'receiversEmail',
    'subject',
  ]);
  
  // Filter menu state
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null);
  const filterOpen = Boolean(filterAnchorEl);
  
  // Columns menu state
  const [columnsAnchorEl, setColumnsAnchorEl] = useState<null | HTMLElement>(null);
  const columnsOpen = Boolean(columnsAnchorEl);

  const handleFilterClick = (event: React.MouseEvent<HTMLElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleColumnsClick = (event: React.MouseEvent<HTMLElement>) => {
    setColumnsAnchorEl(event.currentTarget);
  };

  const handleColumnsClose = () => {
    setColumnsAnchorEl(null);
  };

  const handleFilterChange = (filter: string) => {
    setSelectedFilters(prev => {
      if (prev.includes(filter)) {
        return prev.filter(f => f !== filter);
      } else {
        return [...prev, filter];
      }
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value, selectedFilters);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch(searchTerm, selectedFilters);
    }
  };

  const filterOptions = [
    { value: 'sendersEmail', label: 'Sender\'s Email' },
    { value: 'receiversEmail', label: 'Receiver\'s Email' },
    { value: 'subject', label: 'Subject' },
    { value: 'counter', label: 'Counter' },
    { value: 'aiStatus', label: 'AI Status' },
    { value: 'adminStatus', label: 'Admin Status' },
    { value: 'createdAt', label: 'Created At' },
  ];

  const columnOptions = [
    { value: 'sn', label: 'SN' },
    { value: 'sendersEmail', label: 'Sender\'s Email' },
    { value: 'receiversEmail', label: 'Receiver\'s Email' },
    { value: 'subject', label: 'Subject' },
    { value: 'counter', label: 'Counter' },
    { value: 'aiStatus', label: 'AI Status' },
    { value: 'adminStatus', label: 'Admin Status' },
    { value: 'createdAt', label: 'Created At' },
    { value: 'details', label: 'Details' },
  ];

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <TextField
        placeholder={placeholder}
        value={searchTerm}
        onChange={handleSearchChange}
        onKeyDown={handleKeyDown}
        size="small"
        sx={{ 
          width: 250,
          '& .MuiOutlinedInput-root': {
            borderRadius: 1,
            backgroundColor: (theme) => 
              theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
          }
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon fontSize="small" />
            </InputAdornment>
          ),
        }}
      />
      
      {/* Filter Button */}
      <Button
        variant="outlined"
        size="small"
        startIcon={<FilterListIcon />}
        onClick={handleFilterClick}
        sx={{ 
          textTransform: 'none',
          borderColor: 'rgba(0, 0, 0, 0.12)',
          color: 'text.primary',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          }
        }}
      >
        Filter
      </Button>
      
      {/* Columns Button */}
      <Button
        variant="outlined"
        size="small"
        startIcon={<ViewColumnIcon />}
        onClick={handleColumnsClick}
        sx={{ 
          textTransform: 'none',
          borderColor: 'rgba(0, 0, 0, 0.12)',
          color: 'text.primary',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          }
        }}
      >
        Columns
      </Button>
      
      {/* Filter Menu */}
      <Popover
        open={filterOpen}
        anchorEl={filterAnchorEl}
        onClose={handleFilterClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: { 
            width: 250,
            p: 1,
            mt: 0.5,
          }
        }}
      >
        <Typography variant="subtitle2" sx={{ p: 1, fontWeight: 600 }}>
          Search in:
        </Typography>
        <Divider />
        {filterOptions.map((option) => (
          <MenuItem 
            key={option.value} 
            onClick={() => handleFilterChange(option.value)}
            dense
          >
            <Checkbox 
              checked={selectedFilters.includes(option.value)} 
              size="small"
              sx={{ p: 0.5, mr: 1 }}
            />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Popover>
      
      {/* Columns Menu */}
      <Popover
        open={columnsOpen}
        anchorEl={columnsAnchorEl}
        onClose={handleColumnsClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: { 
            width: 250,
            p: 1,
            mt: 0.5,
          }
        }}
      >
        <Typography variant="subtitle2" sx={{ p: 1, fontWeight: 600 }}>
          Show columns:
        </Typography>
        <Divider />
        {columnOptions.map((option) => (
          <MenuItem 
            key={option.value} 
            onClick={() => {}}
            dense
          >
            <Checkbox 
              checked={true} 
              size="small"
              sx={{ p: 0.5, mr: 1 }}
            />
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Popover>
    </Box>
  );
};

export default ResolvedDisputesSearchBar;
