"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import BugReportIcon from '@mui/icons-material/BugReport';

interface ExceptionLogsFilterButtonProps {
  onApplyFilters: (filters: string[]) => void;
  searchTerm: string;
}

const ExceptionLogsFilterButton: React.FC<ExceptionLogsFilterButtonProps> = ({
  onApplyFilters,
  searchTerm
}) => {
  const filterOptions = [
    { label: "User", value: "user", type: "text" },
    { label: "Path", value: "path", type: "text" },
    { label: "Method", value: "method", type: "text" },
    { label: "Exception Type", value: "exceptionType", type: "text" },
    { label: "Exception Message", value: "exceptionMessage", type: "text" },
    { label: "Timestamp", value: "timestamp", type: "date" },
    { label: "Traceback", value: "traceback", type: "text" },
  ];

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Exception Logs Filters"
      title="Exception Logs Filters"
      description="Apply filters to refine your exception logs search results"
      icon={<BugReportIcon />}
      searchTerm={searchTerm}
    />
  );
};

export default ExceptionLogsFilterButton;
