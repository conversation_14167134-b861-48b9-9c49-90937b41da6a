{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/app/%28main%29/layout.style.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { styled } from '@mui/material';\r\n\r\nexport const LayoutContainer = styled('div')(({ theme }) => ({\r\n  display: 'flex',\r\n  minHeight: '100vh',\r\n  width: '100%',\r\n  overflow: 'hidden',\r\n  [theme.breakpoints.down('sm')]: {\r\n    flexDirection: 'column',\r\n  },\r\n}));\r\n\r\nexport const MainContent = styled('main', {\r\n  shouldForwardProp: (prop) => prop !== 'className',\r\n})(({ theme }) => ({\r\n  flexGrow: 1,\r\n  marginLeft: '240px',\r\n  marginTop: '64px',\r\n  marginBottom: '40px', // Add margin for footer\r\n  padding: theme.spacing(3),\r\n  backgroundColor: theme.palette.background.default,\r\n  // No transition by default for better performance during normal use\r\n  transition: 'none',\r\n  transform: 'translateZ(0) translateX(0)', // Force hardware acceleration\r\n  backfaceVisibility: 'hidden', // Optimize rendering\r\n  overflow: 'hidden',\r\n  // Only apply transitions when the with-transition class is present\r\n  '&.with-transition': {\r\n    transition: 'all 0.45s cubic-bezier(0.25, 1, 0.5, 1)', // Match sidebar transition\r\n    willChange: 'margin-left, transform', // Hint to browser for optimization\r\n  },\r\n  '&.sidebar-icon-only': {\r\n    marginLeft: '64px',\r\n  },\r\n  // Add sliding animation for main content\r\n  '&.with-transition:not(.sidebar-icon-only)': {\r\n    transform: 'translateZ(0) translateX(0)', // No transform when sidebar is open\r\n  },\r\n  '&.with-transition.sidebar-icon-only': {\r\n    transform: 'translateZ(0) translateX(-20px)', // Slide content slightly when sidebar collapses\r\n  },\r\n  // Add subtle shadow transition for depth effect\r\n  '&.with-transition:not(.sidebar-icon-only)': {\r\n    boxShadow: 'none',\r\n  },\r\n  '&.with-transition.sidebar-icon-only': {\r\n    boxShadow: '-5px 0 15px rgba(0, 0, 0, 0.03)', // Subtle shadow when sidebar collapses\r\n  },\r\n  [theme.breakpoints.down('sm')]: {\r\n    marginLeft: 0,\r\n    width: '100%',\r\n    '&.sidebar-icon-only': {\r\n      marginLeft: 0,\r\n    },\r\n  },\r\n  '& .content-wrapper': {\r\n    height: '100%',\r\n    overflow: 'auto',\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    // Custom scrollbar for content wrapper\r\n    '&::-webkit-scrollbar': {\r\n      width: '8px',\r\n      height: '8px',\r\n    },\r\n    '&::-webkit-scrollbar-track': {\r\n      background: 'transparent',\r\n    },\r\n    '&::-webkit-scrollbar-thumb': {\r\n      borderRadius: '10px',\r\n      background: theme.palette.mode === 'light'\r\n        ? 'rgba(0, 0, 0, 0.3)'\r\n        : 'rgba(255, 255, 255, 0.3)',\r\n      border: '2px solid transparent',\r\n      backgroundClip: 'content-box',\r\n      transition: 'background-color 0.3s ease',\r\n    },\r\n    '&::-webkit-scrollbar-thumb:hover': {\r\n      background: theme.palette.mode === 'light'\r\n        ? 'rgba(0, 0, 0, 0.5)'\r\n        : 'rgba(255, 255, 255, 0.5)',\r\n      border: '2px solid transparent',\r\n      backgroundClip: 'content-box',\r\n    },\r\n    '& .page-content': {\r\n      flex: 1,\r\n      overflow: 'auto',\r\n      // Same scrollbar styling for page content\r\n      '&::-webkit-scrollbar': {\r\n        width: '8px',\r\n        height: '8px',\r\n      },\r\n      '&::-webkit-scrollbar-track': {\r\n        background: 'transparent',\r\n      },\r\n      '&::-webkit-scrollbar-thumb': {\r\n        borderRadius: '10px',\r\n        background: theme.palette.mode === 'light'\r\n          ? 'rgba(0, 0, 0, 0.3)'\r\n          : 'rgba(255, 255, 255, 0.3)',\r\n        border: '2px solid transparent',\r\n        backgroundClip: 'content-box',\r\n        transition: 'background-color 0.3s ease',\r\n      },\r\n      '&::-webkit-scrollbar-thumb:hover': {\r\n        background: theme.palette.mode === 'light'\r\n          ? 'rgba(0, 0, 0, 0.5)'\r\n          : 'rgba(255, 255, 255, 0.5)',\r\n        border: '2px solid transparent',\r\n        backgroundClip: 'content-box',\r\n      },\r\n    },\r\n    '& .table-container': {\r\n      overflow: 'auto',\r\n      maxWidth: '100%',\r\n    },\r\n  },\r\n}));"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAIO,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC3D,SAAS;QACT,WAAW;QACX,OAAO;QACP,UAAU;QACV,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,eAAe;QACjB;IACF,CAAC;AAEM,MAAM,cAAc,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;IACxC,mBAAmB,CAAC,OAAS,SAAS;AACxC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACjB,UAAU;QACV,YAAY;QACZ,WAAW;QACX,cAAc;QACd,SAAS,MAAM,OAAO,CAAC;QACvB,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,OAAO;QACjD,oEAAoE;QACpE,YAAY;QACZ,WAAW;QACX,oBAAoB;QACpB,UAAU;QACV,mEAAmE;QACnE,qBAAqB;YACnB,YAAY;YACZ,YAAY;QACd;QACA,uBAAuB;YACrB,YAAY;QACd;QACA,yCAAyC;QACzC,6CAA6C;YAC3C,WAAW;QACb;QACA,uCAAuC;YACrC,WAAW;QACb;QACA,gDAAgD;QAChD,6CAA6C;YAC3C,WAAW;QACb;QACA,uCAAuC;YACrC,WAAW;QACb;QACA,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,YAAY;YACZ,OAAO;YACP,uBAAuB;gBACrB,YAAY;YACd;QACF;QACA,sBAAsB;YACpB,QAAQ;YACR,UAAU;YACV,SAAS;YACT,eAAe;YACf,uCAAuC;YACvC,wBAAwB;gBACtB,OAAO;gBACP,QAAQ;YACV;YACA,8BAA8B;gBAC5B,YAAY;YACd;YACA,8BAA8B;gBAC5B,cAAc;gBACd,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,UAC/B,uBACA;gBACJ,QAAQ;gBACR,gBAAgB;gBAChB,YAAY;YACd;YACA,oCAAoC;gBAClC,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,UAC/B,uBACA;gBACJ,QAAQ;gBACR,gBAAgB;YAClB;YACA,mBAAmB;gBACjB,MAAM;gBACN,UAAU;gBACV,0CAA0C;gBAC1C,wBAAwB;oBACtB,OAAO;oBACP,QAAQ;gBACV;gBACA,8BAA8B;oBAC5B,YAAY;gBACd;gBACA,8BAA8B;oBAC5B,cAAc;oBACd,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,UAC/B,uBACA;oBACJ,QAAQ;oBACR,gBAAgB;oBAChB,YAAY;gBACd;gBACA,oCAAoC;oBAClC,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,UAC/B,uBACA;oBACJ,QAAQ;oBACR,gBAAgB;gBAClB;YACF;YACA,sBAAsB;gBACpB,UAAU;gBACV,UAAU;YACZ;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/icon/icon.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { IconDefinition } from \"@fortawesome/fontawesome-svg-core\";\r\nimport { useTheme } from \"@mui/material\";\r\n\r\ninterface IconProps {\r\n  icon: IconDefinition;\r\n  title?: string;\r\n  onClick?: () => void;\r\n  color?: string;\r\n  size?: \"small\" | \"medium\" | \"large\";\r\n  onlyIcon?: boolean;\r\n  className?: string;\r\n  label?: string;\r\n}\r\n\r\nexport const Icon: React.FC<IconProps> = ({\r\n  icon,\r\n  title,\r\n  onClick,\r\n  color,\r\n  size = \"medium\",\r\n  onlyIcon = false,\r\n  className = \"\",\r\n}) => {\r\n  const theme = useTheme();\r\n  const sizeClass = {\r\n    small: \"w-4 h-4\",\r\n    medium: \"w-5 h-5\",\r\n    large: \"w-6 h-6\",\r\n  };\r\n\r\n  // Use provided color or inherit from theme\r\n  const iconColor = color || (theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.common.black);\r\n\r\n  return (\r\n    <span\r\n      className={`inline-flex items-center justify-center ${\r\n        onClick ? \"cursor-pointer\" : \"\"\r\n      } ${className}`}\r\n      onClick={onClick}\r\n      title={title}\r\n    >\r\n      <FontAwesomeIcon\r\n        icon={icon}\r\n        className={sizeClass[size]}\r\n        style={{ color: iconColor }}\r\n      />\r\n      {!onlyIcon && title && <span className=\"ml-2\">{title}</span>}\r\n    </span>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAaO,MAAM,OAA4B,CAAC,EACxC,IAAI,EACJ,KAAK,EACL,OAAO,EACP,KAAK,EACL,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,YAAY,EAAE,EACf;IACC,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,YAAY;QAChB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,2CAA2C;IAC3C,MAAM,YAAY,SAAS,CAAC,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;IAEnH,qBACE,8OAAC;QACC,WAAW,CAAC,wCAAwC,EAClD,UAAU,mBAAmB,GAC9B,CAAC,EAAE,WAAW;QACf,SAAS;QACT,OAAO;;0BAEP,8OAAC,oKAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,WAAW,SAAS,CAAC,KAAK;gBAC1B,OAAO;oBAAE,OAAO;gBAAU;;;;;;YAE3B,CAAC,YAAY,uBAAS,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/icon/mui-icons.tsx"], "sourcesContent": ["/**\r\n * Central icon management file\r\n * This file exports all Material-UI icons used in the application\r\n * to ensure consistency and easier maintenance.\r\n */\r\n\r\n// Navigation & Layout Icons\r\nimport DashboardIcon from \"@mui/icons-material/Dashboard\";\r\nimport MenuIcon from \"@mui/icons-material/Menu\";\r\nimport ArrowBackIcon from \"@mui/icons-material/ArrowBack\";\r\nimport ArrowForwardIcon from \"@mui/icons-material/ArrowForward\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport MoreVertIcon from \"@mui/icons-material/MoreVert\";\r\n\r\n// Feature/Section Icons\r\nimport ExtensionIcon from \"@mui/icons-material/Extension\"; // For Plugin\r\nimport EmailIcon from \"@mui/icons-material/Email\"; // For Phishing mails\r\nimport GavelIcon from \"@mui/icons-material/Gavel\"; // For Disputes\r\nimport AssessmentIcon from \"@mui/icons-material/Assessment\"; // For Report\r\nimport SecurityIcon from \"@mui/icons-material/Security\"; // For Sandbox\r\nimport FolderSpecialIcon from \"@mui/icons-material/FolderSpecial\"; // For Quarantine\r\nimport StorageIcon from \"@mui/icons-material/Storage\"; // For RogueDB\r\nimport BugReportIcon from \"@mui/icons-material/BugReport\"; // For Logs report\r\nimport PersonIcon from \"@mui/icons-material/Person\"; // For Profile\r\nimport VpnKeyIcon from \"@mui/icons-material/VpnKey\"; // For License related items\r\nimport ComputerIcon from \"@mui/icons-material/Computer\"; // For Agent installed\r\nimport AssignmentIcon from \"@mui/icons-material/Assignment\"; // For Reports\r\nimport SettingsIcon from \"@mui/icons-material/Settings\";\r\n\r\n// Status Icons\r\nimport PlayArrowIcon from \"@mui/icons-material/PlayArrow\"; // For Running Sandbox\r\nimport DoneIcon from \"@mui/icons-material/Done\"; // For Completed Sandbox\r\nimport ErrorIcon from \"@mui/icons-material/Error\"; // For Error logs\r\nimport WarningIcon from \"@mui/icons-material/Warning\"; // For Exception logs\r\nimport CheckCircleIcon from \"@mui/icons-material/CheckCircle\";\r\nimport CancelIcon from \"@mui/icons-material/Cancel\";\r\nimport InfoIcon from \"@mui/icons-material/Info\";\r\n\r\n// Entity Icons\r\nimport LinkIcon from \"@mui/icons-material/Link\"; // For URLs\r\nimport DomainIcon from \"@mui/icons-material/Domain\"; // For Domains\r\nimport MailIcon from \"@mui/icons-material/Mail\"; // For Mails\r\n\r\n// Action Icons\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport DeleteIcon from \"@mui/icons-material/Delete\";\r\nimport EditIcon from \"@mui/icons-material/Edit\";\r\nimport VisibilityIcon from \"@mui/icons-material/Visibility\";\r\nimport VisibilityOffIcon from \"@mui/icons-material/VisibilityOff\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport FilterListIcon from \"@mui/icons-material/FilterList\";\r\nimport LogoutIcon from \"@mui/icons-material/Logout\";\r\nimport DownloadIcon from \"@mui/icons-material/Download\";\r\nimport UploadIcon from \"@mui/icons-material/Upload\";\r\nimport RefreshIcon from \"@mui/icons-material/Refresh\";\r\nimport HelpIcon from \"@mui/icons-material/Help\";\r\nimport NotificationsIcon from \"@mui/icons-material/Notifications\";\r\n\r\nimport ArrowDropDownIcon from \"@mui/icons-material/ArrowDropDown\";\r\nimport LockIcon from \"@mui/icons-material/Lock\";\r\nimport PaletteIcon from \"@mui/icons-material/Palette\";\r\n\r\n/**\r\n * Export all icons for direct imports\r\n * Usage: import { DashboardIcon } from 'src/components/icon/mui-icons';\r\n */\r\nexport const MuiIcons = {\r\n  // Navigation & Layout Icons\r\n  DashboardIcon,\r\n  MenuIcon,\r\n  ArrowBackIcon,\r\n  ArrowForwardIcon,\r\n  CloseIcon,\r\n  MoreVertIcon,\r\n\r\n  // Feature/Section Icons\r\n  ExtensionIcon,\r\n  EmailIcon,\r\n  GavelIcon,\r\n  AssessmentIcon,\r\n  SecurityIcon,\r\n  FolderSpecialIcon,\r\n  StorageIcon,\r\n  BugReportIcon,\r\n  PersonIcon,\r\n  VpnKeyIcon,\r\n  ComputerIcon,\r\n  AssignmentIcon,\r\n  SettingsIcon,\r\n\r\n  // Status Icons\r\n  PlayArrowIcon,\r\n  DoneIcon,\r\n  ErrorIcon,\r\n  WarningIcon,\r\n  CheckCircleIcon,\r\n  CancelIcon,\r\n  InfoIcon,\r\n\r\n  // Entity Icons\r\n  LinkIcon,\r\n  DomainIcon,\r\n  MailIcon,\r\n\r\n  // Action Icons\r\n  AddIcon,\r\n  DeleteIcon,\r\n  EditIcon,\r\n  VisibilityIcon,\r\n  VisibilityOffIcon,\r\n  SearchIcon,\r\n  FilterListIcon,\r\n  LogoutIcon,\r\n  DownloadIcon,\r\n  UploadIcon,\r\n  RefreshIcon,\r\n  HelpIcon,\r\n  NotificationsIcon,\r\n\r\n  ArrowDropDownIcon,\r\n  LockIcon,\r\n  PaletteIcon,\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,4BAA4B;;;;AAC5B;AACA;AACA;AACA;AACA;AACA;AAEA,wBAAwB;AACxB,yQAA2D,aAAa;AACxE,iQAAmD,qBAAqB;AACxE,iQAAmD,eAAe;AAClE,2QAA6D,aAAa;AAC1E,uQAAyD,cAAc;AACvE,iRAAmE,iBAAiB;AACpF,qQAAuD,cAAc;AACrE,yQAA2D,kBAAkB;AAC7E,mQAAqD,cAAc;AACnE,mQAAqD,4BAA4B;AACjF,uQAAyD,sBAAsB;AAC/E,2QAA6D,cAAc;AAC3E;AAEA,eAAe;AACf,yQAA2D,sBAAsB;AACjF,+PAAiD,wBAAwB;AACzE,iQAAmD,iBAAiB;AACpE,qQAAuD,qBAAqB;AAC5E;AACA;AACA;AAEA,eAAe;AACf,+PAAiD,WAAW;AAC5D,mQAAqD,cAAc;AACnE,+PAAiD,YAAY;AAE7D,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMO,MAAM,WAAW;IACtB,4BAA4B;IAC5B,eAAA,uJAAA,CAAA,UAAa;IACb,UAAA,kJAAA,CAAA,UAAQ;IACR,eAAA,uJAAA,CAAA,UAAa;IACb,kBAAA,0JAAA,CAAA,UAAgB;IAChB,WAAA,mJAAA,CAAA,UAAS;IACT,cAAA,sJAAA,CAAA,UAAY;IAEZ,wBAAwB;IACxB,eAAA,uJAAA,CAAA,UAAa;IACb,WAAA,mJAAA,CAAA,UAAS;IACT,WAAA,mJAAA,CAAA,UAAS;IACT,gBAAA,wJAAA,CAAA,UAAc;IACd,cAAA,sJAAA,CAAA,UAAY;IACZ,mBAAA,2JAAA,CAAA,UAAiB;IACjB,aAAA,qJAAA,CAAA,UAAW;IACX,eAAA,uJAAA,CAAA,UAAa;IACb,YAAA,oJAAA,CAAA,UAAU;IACV,YAAA,oJAAA,CAAA,UAAU;IACV,cAAA,sJAAA,CAAA,UAAY;IACZ,gBAAA,wJAAA,CAAA,UAAc;IACd,cAAA,sJAAA,CAAA,UAAY;IAEZ,eAAe;IACf,eAAA,uJAAA,CAAA,UAAa;IACb,UAAA,kJAAA,CAAA,UAAQ;IACR,WAAA,mJAAA,CAAA,UAAS;IACT,aAAA,qJAAA,CAAA,UAAW;IACX,iBAAA,yJAAA,CAAA,UAAe;IACf,YAAA,oJAAA,CAAA,UAAU;IACV,UAAA,kJAAA,CAAA,UAAQ;IAER,eAAe;IACf,UAAA,kJAAA,CAAA,UAAQ;IACR,YAAA,oJAAA,CAAA,UAAU;IACV,UAAA,kJAAA,CAAA,UAAQ;IAER,eAAe;IACf,SAAA,iJAAA,CAAA,UAAO;IACP,YAAA,oJAAA,CAAA,UAAU;IACV,UAAA,kJAAA,CAAA,UAAQ;IACR,gBAAA,wJAAA,CAAA,UAAc;IACd,mBAAA,2JAAA,CAAA,UAAiB;IACjB,YAAA,oJAAA,CAAA,UAAU;IACV,gBAAA,wJAAA,CAAA,UAAc;IACd,YAAA,oJAAA,CAAA,UAAU;IACV,cAAA,sJAAA,CAAA,UAAY;IACZ,YAAA,oJAAA,CAAA,UAAU;IACV,aAAA,qJAAA,CAAA,UAAW;IACX,UAAA,kJAAA,CAAA,UAAQ;IACR,mBAAA,2JAAA,CAAA,UAAiB;IAEjB,mBAAA,2JAAA,CAAA,UAAiB;IACjB,UAAA,kJAAA,CAAA,UAAQ;IACR,aAAA,qJAAA,CAAA,UAAW;AACb", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/icon/index.ts"], "sourcesContent": ["export * from './icon';\r\nexport * from './mui-icons';\r\n\r\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/header/header.style.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { styled, keyframes } from \"@mui/material\";\r\nimport { Typography, Box, Badge } from \"@mui/material\";\r\n\r\n// Subtle pulse animation for notification bell\r\nexport const pulseAnimation = keyframes`\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n`;\r\n\r\n// Subtle shine animation for the app title\r\nexport const shineAnimation = keyframes`\r\n  0% {\r\n    background-position: -100px;\r\n  }\r\n  100% {\r\n    background-position: 200px;\r\n  }\r\n`;\r\n\r\nexport const HeaderContainer = styled(\"header\")(({ theme }) => ({\r\n  position: \"fixed\",\r\n  top: 0,\r\n  right: 0,\r\n  left: 0,\r\n  height: \"64px\",\r\n  backgroundColor: theme.palette.background.paper,\r\n  background: `linear-gradient(to right, ${theme.palette.background.paper}, ${theme.palette.mode === 'light' ? 'rgba(245, 247, 250, 1)' : 'rgba(35, 38, 45, 1)'}, ${theme.palette.background.paper})`,\r\n  borderBottom: `1px solid ${theme.palette.divider}`,\r\n  boxShadow: \"0 4px 10px rgba(0, 0, 0, 0.1)\",\r\n  zIndex: theme.zIndex.appBar,\r\n  width: \"100%\",\r\n  overflow: \"hidden\",\r\n  transition: \"all 0.3s ease\",\r\n}));\r\n\r\nexport const HeaderContent = styled(\"div\")(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"space-between\",\r\n  height: \"100%\",\r\n  padding: theme.spacing(0, 2),\r\n}));\r\n\r\nexport const HeaderActions = styled(\"div\")(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  gap: theme.spacing(2),\r\n}));\r\n\r\nexport const AppTitle = styled(Typography)(({ theme }) => ({\r\n  fontWeight: 700,\r\n  fontSize: \"1.25rem\",\r\n  letterSpacing: \"0.5px\",\r\n  marginLeft: theme.spacing(2),\r\n  marginRight: theme.spacing(2),\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  position: \"relative\",\r\n  \"&::after\": {\r\n    content: '\"\"',\r\n    position: \"absolute\",\r\n    bottom: -4,\r\n    left: 0,\r\n    width: \"30%\",\r\n    height: \"2px\",\r\n    background: `linear-gradient(to right, ${theme.palette.primary.main}, transparent)`,\r\n    transition: \"width 0.3s ease\",\r\n  },\r\n  \"&:hover::after\": {\r\n    width: \"100%\",\r\n  },\r\n  \"& svg\": {\r\n    transition: \"transform 0.3s ease\",\r\n  },\r\n  \"&:hover svg\": {\r\n    transform: \"rotate(10deg)\",\r\n  },\r\n  [theme.breakpoints.down(\"sm\")]: {\r\n    display: \"none\",\r\n  },\r\n}));\r\n\r\n// Notification bell styling\r\nexport const NotificationBell = styled(Box)(({ theme }) => ({\r\n  position: \"relative\",\r\n  cursor: \"pointer\",\r\n  padding: theme.spacing(1.2), // Larger padding\r\n  borderRadius: \"50%\",\r\n  transition: \"all 0.2s ease\",\r\n  width: 40, // Fixed width\r\n  height: 40, // Fixed height\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  \"&:hover\": {\r\n    backgroundColor: theme.palette.action.hover,\r\n    transform: \"scale(1.05)\", // Subtle scale effect on hover\r\n  },\r\n  \"&:active\": {\r\n    transform: \"scale(0.95)\", // Subtle press effect\r\n  },\r\n  \"& svg\": {\r\n    color: theme.palette.text.secondary,\r\n    transition: \"color 0.2s\",\r\n    fontSize: \"1.4rem\", // Larger icon\r\n  },\r\n  \"&:hover svg\": {\r\n    color: theme.palette.primary.main,\r\n  },\r\n  // Adjust badge positioning\r\n  \"& .MuiBadge-root\": {\r\n    display: \"flex\",\r\n    alignItems: \"center\",\r\n    justifyContent: \"center\",\r\n  }\r\n}));\r\n\r\nexport const AnimatedBadge = styled(Badge)(({ theme }) => ({\r\n  \"& .MuiBadge-badge\": {\r\n    backgroundColor: theme.palette.error.main,\r\n    color: theme.palette.error.contrastText,\r\n    fontWeight: \"bold\",\r\n    fontSize: \"0.65rem\", // Slightly larger font size\r\n    minWidth: \"18px\", // Slightly larger minimum width\r\n    height: \"18px\", // Slightly larger height\r\n    padding: \"0 4px\", // Smaller padding\r\n    transform: \"scale(0.85) translate(20%, -30%)\", // Adjusted position for larger button\r\n    transformOrigin: \"100% 0%\", // Transform from top-right corner\r\n    animation: `${pulseAnimation} 2s infinite ease-in-out`,\r\n    boxShadow: \"0 2px 4px rgba(0,0,0,0.2)\", // Add subtle shadow for depth\r\n  }\r\n}));\r\n\r\n// Search bar styling\r\nexport const SearchContainer = styled(Box)(({ theme }) => ({\r\n  position: \"relative\",\r\n  borderRadius: theme.shape.borderRadius,\r\n  backgroundColor: theme.palette.mode === 'light'\r\n    ? 'rgba(0, 0, 0, 0.04)'\r\n    : 'rgba(255, 255, 255, 0.05)',\r\n  '&:hover': {\r\n    backgroundColor: theme.palette.mode === 'light'\r\n      ? 'rgba(0, 0, 0, 0.08)'\r\n      : 'rgba(255, 255, 255, 0.1)',\r\n  },\r\n  marginRight: theme.spacing(2),\r\n  marginLeft: 0,\r\n  width: '100%',\r\n  maxWidth: '240px',\r\n  [theme.breakpoints.down('md')]: {\r\n    display: 'none',\r\n  },\r\n  transition: 'all 0.3s ease',\r\n}));\r\n\r\nexport const SearchInput = styled('input')(({ theme }) => ({\r\n  padding: theme.spacing(1, 1, 1, 4),\r\n  transition: theme.transitions.create('width'),\r\n  width: '100%',\r\n  border: 'none',\r\n  outline: 'none',\r\n  color: theme.palette.text.primary,\r\n  backgroundColor: 'transparent',\r\n  fontSize: '0.875rem',\r\n  '&::placeholder': {\r\n    color: theme.palette.text.secondary,\r\n    opacity: 0.7,\r\n  },\r\n}));\r\n\r\nexport const SearchIconWrapper = styled(Box)(({ theme }) => ({\r\n  padding: theme.spacing(0, 1),\r\n  height: '100%',\r\n  position: 'absolute',\r\n  pointerEvents: 'none',\r\n  display: 'flex',\r\n  alignItems: 'center',\r\n  justifyContent: 'center',\r\n  color: theme.palette.text.secondary,\r\n}));\r\n\r\n// System status indicator\r\nexport const StatusIndicator = styled(Box, {\r\n  shouldForwardProp: (prop) => prop !== 'status',\r\n})<{ status: 'online' | 'warning' | 'offline' }>(({ theme, status }) => {\r\n  const getStatusColor = () => {\r\n    switch (status) {\r\n      case 'online':\r\n        return theme.palette.success.main;\r\n      case 'warning':\r\n        return theme.palette.warning.main;\r\n      case 'offline':\r\n        return theme.palette.error.main;\r\n      default:\r\n        return theme.palette.success.main;\r\n    }\r\n  };\r\n\r\n  return {\r\n    display: 'flex',\r\n    alignItems: 'center',\r\n    gap: theme.spacing(0.5),\r\n    padding: theme.spacing(0.5, 1),\r\n    borderRadius: theme.shape.borderRadius,\r\n    backgroundColor: theme.palette.mode === 'light'\r\n      ? 'rgba(0, 0, 0, 0.04)'\r\n      : 'rgba(255, 255, 255, 0.05)',\r\n    '& .status-dot': {\r\n      width: 8,\r\n      height: 8,\r\n      borderRadius: '50%',\r\n      backgroundColor: getStatusColor(),\r\n      boxShadow: `0 0 0 2px ${theme.palette.background.paper}, 0 0 0 4px ${getStatusColor()}33`,\r\n    },\r\n    '& .status-text': {\r\n      fontSize: '0.75rem',\r\n      color: theme.palette.text.secondary,\r\n      fontWeight: 500,\r\n    },\r\n    [theme.breakpoints.down('md')]: {\r\n      '& .status-text': {\r\n        display: 'none',\r\n      },\r\n      padding: theme.spacing(0.5),\r\n    },\r\n  };\r\n});\r\n\r\n// User profile related styled components\r\nexport const UserInfo = styled(Box)(({ theme }) => ({\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  cursor: \"pointer\",\r\n  padding: theme.spacing(0.5, 1),\r\n  borderRadius: theme.shape.borderRadius,\r\n  transition: \"all 0.3s ease\",\r\n  \"&:hover\": {\r\n    backgroundColor: theme.palette.action.hover,\r\n    transform: 'translateY(-2px)',\r\n  },\r\n}));\r\n\r\nexport const UserName = styled(Typography)(({ theme }) => ({\r\n  fontWeight: 600,\r\n  color: theme.palette.text.primary,\r\n  [theme.breakpoints.down(\"md\")]: {\r\n    display: \"none\",\r\n  },\r\n}));\r\n\r\nexport const UserRole = styled(Typography)(({ theme }) => ({\r\n  color: theme.palette.text.secondary,\r\n  fontSize: \"0.75rem\",\r\n  [theme.breakpoints.down(\"md\")]: {\r\n    display: \"none\",\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;AAAA;AACA;AAAA;AAAA;AAHA;;;AAMO,MAAM,iBAAiB,oMAAA,CAAA,YAAS,CAAC;;;;;;;;;;AAUxC,CAAC;AAGM,MAAM,iBAAiB,oMAAA,CAAA,YAAS,CAAC;;;;;;;AAOxC,CAAC;AAEM,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC9D,UAAU;QACV,KAAK;QACL,OAAO;QACP,MAAM;QACN,QAAQ;QACR,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,YAAY,CAAC,0BAA0B,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,2BAA2B,sBAAsB,EAAE,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;QACnM,cAAc,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QAClD,WAAW;QACX,QAAQ,MAAM,MAAM,CAAC,MAAM;QAC3B,OAAO;QACP,UAAU;QACV,YAAY;IACd,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,QAAQ;QACR,SAAS,MAAM,OAAO,CAAC,GAAG;IAC5B,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,SAAS;QACT,YAAY;QACZ,KAAK,MAAM,OAAO,CAAC;IACrB,CAAC;AAEM,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,sMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,YAAY;QACZ,UAAU;QACV,eAAe;QACf,YAAY,MAAM,OAAO,CAAC;QAC1B,aAAa,MAAM,OAAO,CAAC;QAC3B,SAAS;QACT,YAAY;QACZ,UAAU;QACV,YAAY;YACV,SAAS;YACT,UAAU;YACV,QAAQ,CAAC;YACT,MAAM;YACN,OAAO;YACP,QAAQ;YACR,YAAY,CAAC,0BAA0B,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;YACnF,YAAY;QACd;QACA,kBAAkB;YAChB,OAAO;QACT;QACA,SAAS;YACP,YAAY;QACd;QACA,eAAe;YACb,WAAW;QACb;QACA,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,SAAS;QACX;IACF,CAAC;AAGM,MAAM,mBAAmB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC1D,UAAU;QACV,QAAQ;QACR,SAAS,MAAM,OAAO,CAAC;QACvB,cAAc;QACd,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;YAC3C,WAAW;QACb;QACA,YAAY;YACV,WAAW;QACb;QACA,SAAS;YACP,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;YACnC,YAAY;YACZ,UAAU;QACZ;QACA,eAAe;YACb,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QACnC;QACA,2BAA2B;QAC3B,oBAAoB;YAClB,SAAS;YACT,YAAY;YACZ,gBAAgB;QAClB;IACF,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,uLAAA,CAAA,QAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,qBAAqB;YACnB,iBAAiB,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI;YACzC,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,YAAY;YACvC,YAAY;YACZ,UAAU;YACV,UAAU;YACV,QAAQ;YACR,SAAS;YACT,WAAW;YACX,iBAAiB;YACjB,WAAW,GAAG,eAAe,wBAAwB,CAAC;YACtD,WAAW;QACb;IACF,CAAC;AAGM,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,UAAU;QACV,cAAc,MAAM,KAAK,CAAC,YAAY;QACtC,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UACpC,wBACA;QACJ,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UACpC,wBACA;QACN;QACA,aAAa,MAAM,OAAO,CAAC;QAC3B,YAAY;QACZ,OAAO;QACP,UAAU;QACV,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,SAAS;QACX;QACA,YAAY;IACd,CAAC;AAEM,MAAM,cAAc,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,SAAS,MAAM,OAAO,CAAC,GAAG,GAAG,GAAG;QAChC,YAAY,MAAM,WAAW,CAAC,MAAM,CAAC;QACrC,OAAO;QACP,QAAQ;QACR,SAAS;QACT,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,iBAAiB;QACjB,UAAU;QACV,kBAAkB;YAChB,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;YACnC,SAAS;QACX;IACF,CAAC;AAEM,MAAM,oBAAoB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC3D,SAAS,MAAM,OAAO,CAAC,GAAG;QAC1B,QAAQ;QACR,UAAU;QACV,eAAe;QACf,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;IACrC,CAAC;AAGM,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE;IACzC,mBAAmB,CAAC,OAAS,SAAS;AACxC,GAAiD,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE;IACjE,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACnC,KAAK;gBACH,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACnC,KAAK;gBACH,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI;YACjC;gBACE,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QACrC;IACF;IAEA,OAAO;QACL,SAAS;QACT,YAAY;QACZ,KAAK,MAAM,OAAO,CAAC;QACnB,SAAS,MAAM,OAAO,CAAC,KAAK;QAC5B,cAAc,MAAM,KAAK,CAAC,YAAY;QACtC,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UACpC,wBACA;QACJ,iBAAiB;YACf,OAAO;YACP,QAAQ;YACR,cAAc;YACd,iBAAiB;YACjB,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,EAAE,iBAAiB,EAAE,CAAC;QAC3F;QACA,kBAAkB;YAChB,UAAU;YACV,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;YACnC,YAAY;QACd;QACA,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,kBAAkB;gBAChB,SAAS;YACX;YACA,SAAS,MAAM,OAAO,CAAC;QACzB;IACF;AACF;AAGO,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAClD,SAAS;QACT,YAAY;QACZ,QAAQ;QACR,SAAS,MAAM,OAAO,CAAC,KAAK;QAC5B,cAAc,MAAM,KAAK,CAAC,YAAY;QACtC,YAAY;QACZ,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;YAC3C,WAAW;QACb;IACF,CAAC;AAEM,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,sMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,YAAY;QACZ,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,SAAS;QACX;IACF,CAAC;AAEM,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,sMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QACnC,UAAU;QACV,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;YAC9B,SAAS;QACX;IACF,CAAC", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/header/header.tsx"], "sourcesContent": ["// import React from \"react\";\r\n// import { IconButton } from \"@mui/material\";\r\n// import { faBars } from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n// import { Icon } from \"../icon\";\r\n// import ThemeToggle from \"../themeToggel\";\r\n// import {\r\n//   HeaderActions,\r\n//   HeaderContainer,\r\n//   HeaderContent,\r\n//   AppTitle,\r\n// } from \"./header.style\";\r\n\r\n// interface HeaderProps {\r\n//   title?: string;\r\n//   onToggleSidebar?: () => void;\r\n// }\r\n\r\n// export const Header: React.FC<HeaderProps> = ({ title, onToggleSidebar }) => {\r\n//   return (\r\n//     <HeaderContainer>\r\n//       <HeaderContent>\r\n//         <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n//           <IconButton\r\n//             edge=\"start\"\r\n//             color=\"inherit\"\r\n//             aria-label=\"menu\"\r\n//             onClick={onToggleSidebar}\r\n//           >\r\n//             <Icon icon={faBars} size=\"medium\" onlyIcon />\r\n//           </IconButton>\r\n//           <AppTitle variant=\"h6\">Decision Support System</AppTitle>\r\n//         </div>\r\n//         <HeaderActions>\r\n//           <ThemeToggle />\r\n//         </HeaderActions>\r\n//       </HeaderContent>\r\n//     </HeaderContainer>\r\n//   );\r\n// };\r\n\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { IconButton, Menu, MenuItem, Avatar, Typography, Box, Divider, ListItemIcon, ListItemText, Collapse, useTheme as useMuiTheme, Tooltip, Fade } from \"@mui/material\";\r\nimport {\r\n  faBars,\r\n  faUser,\r\n  faSignOutAlt,\r\n  faCog,\r\n  faSun,\r\n  faMoon,\r\n  faLaptop,\r\n  faChevronRight,\r\n  faChevronDown,\r\n  faPalette,\r\n  faCheck,\r\n  faShieldVirus,\r\n  faBell,\r\n  faSearch,\r\n  faCircleCheck\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Icon } from \"../icon\";\r\nimport { useTheme } from \"@/context/ThemeContext\";\r\nimport { ThemeToggleEnum } from \"../themeToggel/enum\";\r\nimport { RoutePathEnum } from \"@/enum\";\r\nimport { DEFAULT_THEME_LIST } from \"@/constants/theme\";\r\nimport {\r\n  HeaderActions,\r\n  HeaderContainer,\r\n  HeaderContent,\r\n  AppTitle,\r\n  NotificationBell,\r\n  AnimatedBadge,\r\n  SearchContainer,\r\n  SearchInput,\r\n  SearchIconWrapper,\r\n  StatusIndicator\r\n} from \"./header.style\";\r\nimport styled from \"@emotion/styled\";\r\n\r\nconst MenuItemContent = styled(Box)`\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n`;\r\n\r\ninterface HeaderProps {\r\n  title?: string;\r\n  onToggleSidebar?: () => void;\r\n  username?: string;\r\n  role?: string;\r\n}\r\n\r\nexport const Header: React.FC<HeaderProps> = ({\r\n  title,\r\n  onToggleSidebar,\r\n  username = \"<EMAIL>\",\r\n  role = \"Admin\",\r\n}) => {\r\n  const router = useRouter();\r\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n  const [notificationAnchorEl, setNotificationAnchorEl] = useState<null | HTMLElement>(null);\r\n  const open = Boolean(anchorEl);\r\n  const notificationsOpen = Boolean(notificationAnchorEl);\r\n  const { mode, setTheme } = useTheme();\r\n  const muiTheme = useMuiTheme();\r\n  const [themeMenuOpen, setThemeMenuOpen] = useState(false);\r\n  const [searchValue, setSearchValue] = useState(\"\");\r\n  const [systemStatus, setSystemStatus] = useState<'online' | 'warning' | 'offline'>('online');\r\n  const [notificationCount, setNotificationCount] = useState(3);\r\n  const [notifications, setNotifications] = useState([\r\n    { id: 1, title: \"New phishing attempt detected\", time: \"5 minutes ago\", read: false },\r\n    { id: 2, title: \"System update available\", time: \"1 hour ago\", read: false },\r\n    { id: 3, title: \"License expiring soon\", time: \"2 days ago\", read: false },\r\n  ]);\r\n\r\n  // Simulate status changes for demo purposes\r\n  useEffect(() => {\r\n    const statusInterval = setInterval(() => {\r\n      // Randomly change status for demonstration\r\n      const statuses: Array<'online' | 'warning' | 'offline'> = ['online', 'warning', 'online'];\r\n      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];\r\n      setSystemStatus(randomStatus);\r\n    }, 30000); // Change every 30 seconds\r\n\r\n    return () => clearInterval(statusInterval);\r\n  }, []);\r\n\r\n  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleMenuClose = () => {\r\n    setAnchorEl(null);\r\n  };\r\n\r\n  const handleNotificationOpen = (event: React.MouseEvent<HTMLElement>) => {\r\n    setNotificationAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleNotificationClose = () => {\r\n    setNotificationAnchorEl(null);\r\n  };\r\n\r\n  const handleNotificationRead = (id: number) => {\r\n    setNotifications(prev =>\r\n      prev.map(notification =>\r\n        notification.id === id ? { ...notification, read: true } : notification\r\n      )\r\n    );\r\n    setNotificationCount(prev => Math.max(0, prev - 1));\r\n  };\r\n\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchValue(e.target.value);\r\n  };\r\n\r\n  const handleSearchSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (searchValue.trim()) {\r\n      // Implement search functionality\r\n      console.log(\"Searching for:\", searchValue);\r\n      // Clear search after submission\r\n      setSearchValue(\"\");\r\n    }\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    // Clear token and other auth data from localStorage\r\n    import(\"@/utills/storage.utill\").then(({ StorageHelper }) => {\r\n      StorageHelper.clearLocalStorage();\r\n\r\n      // Close the menu\r\n      handleMenuClose();\r\n\r\n      // Redirect to login page\r\n      import(\"@/enum\").then(({ RoutePathEnum }) => {\r\n        window.location.href = RoutePathEnum.ADMIN_LOGIN;\r\n      });\r\n    });\r\n  };\r\n\r\n  const handleProfileSettings = () => {\r\n    handleMenuClose();\r\n    // Redirect to profile page using direct URL for consistency with sidebar\r\n    window.location.href = \"http://localhost:3000/profile\";\r\n  };\r\n\r\n  const handleThemeToggle = () => {\r\n    // Toggle the theme submenu\r\n    setThemeMenuOpen(!themeMenuOpen);\r\n  };\r\n\r\n  const handleThemeSelect = (themeValue: string) => {\r\n    // Set the selected theme\r\n    setTheme(themeValue as \"light\" | \"dark\" | \"system\");\r\n    // Don't close the menu so user can see the theme change\r\n  };\r\n\r\n  const getStatusText = () => {\r\n    switch (systemStatus) {\r\n      case 'online':\r\n        return 'System Online';\r\n      case 'warning':\r\n        return 'Performance Issues';\r\n      case 'offline':\r\n        return 'System Offline';\r\n      default:\r\n        return 'System Online';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <HeaderContainer>\r\n      <HeaderContent>\r\n        <div style={{ display: \"flex\", alignItems: \"center\" }}>\r\n          <Box\r\n            component=\"button\"\r\n            onClick={(e) => {\r\n              // Add a visual feedback class to the button\r\n              const button = e.currentTarget;\r\n              button.classList.add('menu-button-active');\r\n\r\n              // Remove the class after the animation completes\r\n              setTimeout(() => {\r\n                button.classList.remove('menu-button-active');\r\n              }, 300);\r\n\r\n              // Call the toggle function\r\n              onToggleSidebar?.();\r\n            }}\r\n            className=\"menu-button\"\r\n            sx={{\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              width: 40,\r\n              height: 40,\r\n              padding: 0,\r\n              border: 'none',\r\n              background: 'transparent',\r\n              cursor: 'pointer',\r\n              color: 'inherit',\r\n              borderRadius: '4px',\r\n              transition: 'all 0.2s cubic-bezier(0.25, 1, 0.5, 1)',\r\n              '&:hover': {\r\n                backgroundColor: 'rgba(0, 0, 0, 0.04)',\r\n              },\r\n              '&:active, &.menu-button-active': {\r\n                backgroundColor: 'rgba(0, 0, 0, 0.08)',\r\n                transform: 'scale(0.95)',\r\n              },\r\n              '&:focus': {\r\n                outline: 'none',\r\n              },\r\n              zIndex: 1200 // Ensure it's above other elements\r\n            }}\r\n            aria-label=\"menu\"\r\n          >\r\n            <Icon\r\n              icon={faBars}\r\n              size=\"medium\"\r\n              onlyIcon\r\n              sx={{\r\n                transition: 'transform 0.3s cubic-bezier(0.25, 1, 0.5, 1)',\r\n                '.menu-button-active &': {\r\n                  transform: 'rotate(90deg) scale(1.1)',\r\n                }\r\n              }}\r\n            />\r\n          </Box>\r\n          <AppTitle variant=\"h6\">\r\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n              <Icon icon={faShieldVirus} size=\"small\" onlyIcon color={muiTheme.palette.primary.main} />\r\n              {title || \"Phishing Dashboard\"}\r\n            </Box>\r\n          </AppTitle>\r\n        </div>\r\n\r\n        <HeaderActions>\r\n          <Box sx={{\r\n            display: \"flex\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"flex-end\",\r\n            gap: 2 // Add gap between elements\r\n          }}>\r\n\r\n            {/* User Profile Section */}\r\n            <Box sx={{\r\n              display: \"flex\",\r\n              alignItems: \"center\"\r\n            }}>\r\n              <Box sx={{ textAlign: \"right\", mr: 1 }}>\r\n                <Typography variant=\"subtitle2\" color=\"inherit\">\r\n                  {username}\r\n                </Typography>\r\n                <Typography variant=\"caption\" color=\"gray\">\r\n                  {role}\r\n                </Typography>\r\n              </Box>\r\n              <IconButton\r\n                onClick={handleMenuOpen}\r\n                sx={{\r\n                  ml: 0.5,\r\n                  transition: 'transform 0.2s',\r\n                  '&:hover': {\r\n                    transform: 'scale(1.05)'\r\n                  }\r\n                }}\r\n              >\r\n                <Avatar sx={{\r\n                  width: 32,\r\n                  height: 32,\r\n                  bgcolor: \"#1976d2\",\r\n                  boxShadow: '0 2px 5px rgba(0,0,0,0.2)'\r\n                }}>\r\n                  {username[0]}\r\n                </Avatar>\r\n              </IconButton>\r\n\r\n              <Menu\r\n                anchorEl={anchorEl}\r\n                open={open}\r\n                onClose={handleMenuClose}\r\n                PaperProps={{\r\n                  elevation: 4,\r\n                  sx: {\r\n                    minWidth: 200,\r\n                    borderRadius: 2,\r\n                    mt: 1,\r\n                    overflow: 'visible',\r\n                    filter: 'drop-shadow(0px 3px 8px rgba(0,0,0,0.15))',\r\n                    '&:before': {\r\n                      content: '\"\"',\r\n                      display: 'block',\r\n                      position: 'absolute',\r\n                      top: 0,\r\n                      right: 14,\r\n                      width: 10,\r\n                      height: 10,\r\n                      bgcolor: 'background.paper',\r\n                      transform: 'translateY(-50%) rotate(45deg)',\r\n                      zIndex: 0,\r\n                    },\r\n                  },\r\n                }}\r\n                transformOrigin={{ horizontal: 'right', vertical: 'top' }}\r\n                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}\r\n              >\r\n                <Box sx={{ p: 2, pb: 1 }}>\r\n                  <Avatar sx={{\r\n                    width: 50,\r\n                    height: 50,\r\n                    mb: 1,\r\n                    mx: 'auto',\r\n                    bgcolor: \"#1976d2\",\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.2)'\r\n                  }}>\r\n                    {username[0]}\r\n                  </Avatar>\r\n                  <Typography variant=\"body1\" align=\"center\" fontWeight=\"bold\">{username}</Typography>\r\n                  <Typography variant=\"caption\" align=\"center\" display=\"block\" color=\"text.secondary\">{role}</Typography>\r\n                </Box>\r\n\r\n                <Divider />\r\n\r\n                <MenuItem onClick={handleProfileSettings} sx={{ py: 1.5 }}>\r\n                  <MenuItemContent>\r\n                    <Icon icon={faUser} size=\"small\" onlyIcon />\r\n                    <Typography variant=\"body2\">Profile</Typography>\r\n                  </MenuItemContent>\r\n                </MenuItem>\r\n\r\n                <MenuItem onClick={handleProfileSettings} sx={{ py: 1.5 }}>\r\n                  <MenuItemContent>\r\n                    <Icon icon={faCog} size=\"small\" onlyIcon />\r\n                    <Typography variant=\"body2\">Settings</Typography>\r\n                  </MenuItemContent>\r\n                </MenuItem>\r\n\r\n                {/* Theme Menu Item with Dropdown */}\r\n                <MenuItem onClick={handleThemeToggle} sx={{ py: 1.5 }}>\r\n                  <MenuItemContent>\r\n                    <Icon icon={faPalette} size=\"small\" onlyIcon />\r\n                    <Typography variant=\"body2\" sx={{ flex: 1 }}>Theme</Typography>\r\n                    <Icon\r\n                      icon={themeMenuOpen ? faChevronDown : faChevronRight}\r\n                      size=\"small\"\r\n                      onlyIcon\r\n                      sx={{ color: 'text.secondary', fontSize: '0.75rem' }}\r\n                    />\r\n                  </MenuItemContent>\r\n                </MenuItem>\r\n\r\n                {/* Theme Submenu */}\r\n                <Collapse in={themeMenuOpen} timeout=\"auto\" unmountOnExit>\r\n                  <Box sx={{ pl: 2 }}>\r\n                    {/* Light Theme Option */}\r\n                    <MenuItem\r\n                      onClick={() => handleThemeSelect('light')}\r\n                      sx={{\r\n                        py: 1.5,\r\n                        bgcolor: mode === 'light' ? 'action.selected' : 'transparent'\r\n                      }}\r\n                    >\r\n                      <MenuItemContent>\r\n                        <Icon icon={faSun} size=\"small\" onlyIcon sx={{ color: 'warning.main' }} />\r\n                        <Typography variant=\"body2\" sx={{ flex: 1 }}>Light theme</Typography>\r\n                        {mode === 'light' && (\r\n                          <Icon icon={faCheck} size=\"small\" onlyIcon sx={{ color: 'primary.main', fontSize: '0.75rem' }} />\r\n                        )}\r\n                      </MenuItemContent>\r\n                    </MenuItem>\r\n\r\n                    {/* Dark Theme Option */}\r\n                    <MenuItem\r\n                      onClick={() => handleThemeSelect('dark')}\r\n                      sx={{\r\n                        py: 1.5,\r\n                        bgcolor: mode === 'dark' ? 'action.selected' : 'transparent'\r\n                      }}\r\n                    >\r\n                      <MenuItemContent>\r\n                        <Icon icon={faMoon} size=\"small\" onlyIcon sx={{ color: 'info.main' }} />\r\n                        <Typography variant=\"body2\" sx={{ flex: 1 }}>Dark theme</Typography>\r\n                        {mode === 'dark' && (\r\n                          <Icon icon={faCheck} size=\"small\" onlyIcon sx={{ color: 'primary.main', fontSize: '0.75rem' }} />\r\n                        )}\r\n                      </MenuItemContent>\r\n                    </MenuItem>\r\n\r\n                    {/* Device Default Option */}\r\n                    <MenuItem\r\n                      onClick={() => handleThemeSelect('system')}\r\n                      sx={{\r\n                        py: 1.5,\r\n                        bgcolor: mode === 'system' ? 'action.selected' : 'transparent'\r\n                      }}\r\n                    >\r\n                      <MenuItemContent>\r\n                        <Icon icon={faLaptop} size=\"small\" onlyIcon sx={{ color: 'success.main' }} />\r\n                        <Typography variant=\"body2\" sx={{ flex: 1 }}>Device default</Typography>\r\n                        {mode === 'system' && (\r\n                          <Icon icon={faCheck} size=\"small\" onlyIcon sx={{ color: 'primary.main', fontSize: '0.75rem' }} />\r\n                        )}\r\n                      </MenuItemContent>\r\n                    </MenuItem>\r\n                  </Box>\r\n                </Collapse>\r\n\r\n                <Divider />\r\n\r\n                <MenuItem onClick={handleLogout} sx={{ py: 1.5 }}>\r\n                  <MenuItemContent>\r\n                    <Icon icon={faSignOutAlt} size=\"small\" onlyIcon />\r\n                    <Typography variant=\"body2\">Logout</Typography>\r\n                  </MenuItemContent>\r\n                </MenuItem>\r\n              </Menu>\r\n            </Box>\r\n          </Box>\r\n        </HeaderActions>\r\n      </HeaderContent>\r\n    </HeaderContainer>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B,8CAA8C;AAC9C,8DAA8D;AAE9D,kCAAkC;AAClC,4CAA4C;AAC5C,WAAW;AACX,mBAAmB;AACnB,qBAAqB;AACrB,mBAAmB;AACnB,cAAc;AACd,2BAA2B;AAE3B,0BAA0B;AAC1B,oBAAoB;AACpB,kCAAkC;AAClC,IAAI;AAEJ,iFAAiF;AACjF,aAAa;AACb,wBAAwB;AACxB,wBAAwB;AACxB,kEAAkE;AAClE,wBAAwB;AACxB,2BAA2B;AAC3B,8BAA8B;AAC9B,gCAAgC;AAChC,wCAAwC;AACxC,cAAc;AACd,4DAA4D;AAC5D,0BAA0B;AAC1B,sEAAsE;AACtE,iBAAiB;AACjB,0BAA0B;AAC1B,4BAA4B;AAC5B,2BAA2B;AAC3B,yBAAyB;AACzB,yBAAyB;AACzB,OAAO;AACP,KAAK;;;;;AAGL;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAiBA;AACA;AAAA;AACA;AAIA;AAYA;;;;;;;;;;AAEA,MAAM,kBAAkB,CAAA,GAAA,sLAAA,CAAA,UAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,CAAC,CAAC;;;;AAIpC,CAAC;AASM,MAAM,SAAgC,CAAC,EAC5C,KAAK,EACL,eAAe,EACf,WAAW,sBAAsB,EACjC,OAAO,OAAO,EACf;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACrF,MAAM,OAAO,QAAQ;IACrB,MAAM,oBAAoB,QAAQ;IAClC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAClC,MAAM,WAAW,CAAA,GAAA,8LAAA,CAAA,WAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IACnF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD;YAAE,IAAI;YAAG,OAAO;YAAiC,MAAM;YAAiB,MAAM;QAAM;QACpF;YAAE,IAAI;YAAG,OAAO;YAA2B,MAAM;YAAc,MAAM;QAAM;QAC3E;YAAE,IAAI;YAAG,OAAO;YAAyB,MAAM;YAAc,MAAM;QAAM;KAC1E;IAED,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB,YAAY;YACjC,2CAA2C;YAC3C,MAAM,WAAoD;gBAAC;gBAAU;gBAAW;aAAS;YACzF,MAAM,eAAe,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;YAC1E,gBAAgB;QAClB,GAAG,QAAQ,0BAA0B;QAErC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,YAAY,MAAM,aAAa;IACjC;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,yBAAyB,CAAC;QAC9B,wBAAwB,MAAM,aAAa;IAC7C;IAEA,MAAM,0BAA0B;QAC9B,wBAAwB;IAC1B;IAEA,MAAM,yBAAyB,CAAC;QAC9B,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAAK;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAAI;QAG/D,qBAAqB,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;IAClD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC,KAAK;IAC/B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,iCAAiC;YACjC,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,gCAAgC;YAChC,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,oDAAoD;QACpD,+HAAiC,IAAI,CAAC,CAAC,EAAE,aAAa,EAAE;YACtD,cAAc,iBAAiB;YAE/B,iBAAiB;YACjB;YAEA,yBAAyB;YACzB,qHAAiB,IAAI,CAAC,CAAC,EAAE,aAAa,EAAE;gBACtC,OAAO,QAAQ,CAAC,IAAI,GAAG,cAAc,WAAW;YAClD;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B;QACA,yEAAyE;QACzE,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,oBAAoB;QACxB,2BAA2B;QAC3B,iBAAiB,CAAC;IACpB;IAEA,MAAM,oBAAoB,CAAC;QACzB,yBAAyB;QACzB,SAAS;IACT,wDAAwD;IAC1D;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,wJAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,wJAAA,CAAA,gBAAa;;8BACZ,8OAAC;oBAAI,OAAO;wBAAE,SAAS;wBAAQ,YAAY;oBAAS;;sCAClD,8OAAC,iLAAA,CAAA,MAAG;4BACF,WAAU;4BACV,SAAS,CAAC;gCACR,4CAA4C;gCAC5C,MAAM,SAAS,EAAE,aAAa;gCAC9B,OAAO,SAAS,CAAC,GAAG,CAAC;gCAErB,iDAAiD;gCACjD,WAAW;oCACT,OAAO,SAAS,CAAC,MAAM,CAAC;gCAC1B,GAAG;gCAEH,2BAA2B;gCAC3B;4BACF;4BACA,WAAU;4BACV,IAAI;gCACF,SAAS;gCACT,YAAY;gCACZ,gBAAgB;gCAChB,OAAO;gCACP,QAAQ;gCACR,SAAS;gCACT,QAAQ;gCACR,YAAY;gCACZ,QAAQ;gCACR,OAAO;gCACP,cAAc;gCACd,YAAY;gCACZ,WAAW;oCACT,iBAAiB;gCACnB;gCACA,kCAAkC;oCAChC,iBAAiB;oCACjB,WAAW;gCACb;gCACA,WAAW;oCACT,SAAS;gCACX;gCACA,QAAQ,KAAK,mCAAmC;4BAClD;4BACA,cAAW;sCAEX,cAAA,8OAAC,4IAAA,CAAA,OAAI;gCACH,MAAM,wKAAA,CAAA,SAAM;gCACZ,MAAK;gCACL,QAAQ;gCACR,IAAI;oCACF,YAAY;oCACZ,yBAAyB;wCACvB,WAAW;oCACb;gCACF;;;;;;;;;;;sCAGJ,8OAAC,wJAAA,CAAA,WAAQ;4BAAC,SAAQ;sCAChB,cAAA,8OAAC,iLAAA,CAAA,MAAG;gCAAC,IAAI;oCAAE,SAAS;oCAAQ,YAAY;oCAAU,KAAK;gCAAE;;kDACvD,8OAAC,4IAAA,CAAA,OAAI;wCAAC,MAAM,wKAAA,CAAA,gBAAa;wCAAE,MAAK;wCAAQ,QAAQ;wCAAC,OAAO,SAAS,OAAO,CAAC,OAAO,CAAC,IAAI;;;;;;oCACpF,SAAS;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC,wJAAA,CAAA,gBAAa;8BACZ,cAAA,8OAAC,iLAAA,CAAA,MAAG;wBAAC,IAAI;4BACP,SAAS;4BACT,YAAY;4BACZ,gBAAgB;4BAChB,KAAK,EAAE,2BAA2B;wBACpC;kCAGE,cAAA,8OAAC,iLAAA,CAAA,MAAG;4BAAC,IAAI;gCACP,SAAS;gCACT,YAAY;4BACd;;8CACE,8OAAC,iLAAA,CAAA,MAAG;oCAAC,IAAI;wCAAE,WAAW;wCAAS,IAAI;oCAAE;;sDACnC,8OAAC,sMAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAY,OAAM;sDACnC;;;;;;sDAEH,8OAAC,sMAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAU,OAAM;sDACjC;;;;;;;;;;;;8CAGL,8OAAC,sMAAA,CAAA,aAAU;oCACT,SAAS;oCACT,IAAI;wCACF,IAAI;wCACJ,YAAY;wCACZ,WAAW;4CACT,WAAW;wCACb;oCACF;8CAEA,cAAA,8OAAC,0LAAA,CAAA,SAAM;wCAAC,IAAI;4CACV,OAAO;4CACP,QAAQ;4CACR,SAAS;4CACT,WAAW;wCACb;kDACG,QAAQ,CAAC,EAAE;;;;;;;;;;;8CAIhB,8OAAC,oLAAA,CAAA,OAAI;oCACH,UAAU;oCACV,MAAM;oCACN,SAAS;oCACT,YAAY;wCACV,WAAW;wCACX,IAAI;4CACF,UAAU;4CACV,cAAc;4CACd,IAAI;4CACJ,UAAU;4CACV,QAAQ;4CACR,YAAY;gDACV,SAAS;gDACT,SAAS;gDACT,UAAU;gDACV,KAAK;gDACL,OAAO;gDACP,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,WAAW;gDACX,QAAQ;4CACV;wCACF;oCACF;oCACA,iBAAiB;wCAAE,YAAY;wCAAS,UAAU;oCAAM;oCACxD,cAAc;wCAAE,YAAY;wCAAS,UAAU;oCAAS;;sDAExD,8OAAC,iLAAA,CAAA,MAAG;4CAAC,IAAI;gDAAE,GAAG;gDAAG,IAAI;4CAAE;;8DACrB,8OAAC,0LAAA,CAAA,SAAM;oDAAC,IAAI;wDACV,OAAO;wDACP,QAAQ;wDACR,IAAI;wDACJ,IAAI;wDACJ,SAAS;wDACT,WAAW;oDACb;8DACG,QAAQ,CAAC,EAAE;;;;;;8DAEd,8OAAC,sMAAA,CAAA,aAAU;oDAAC,SAAQ;oDAAQ,OAAM;oDAAS,YAAW;8DAAQ;;;;;;8DAC9D,8OAAC,sMAAA,CAAA,aAAU;oDAAC,SAAQ;oDAAU,OAAM;oDAAS,SAAQ;oDAAQ,OAAM;8DAAkB;;;;;;;;;;;;sDAGvF,8OAAC,6LAAA,CAAA,UAAO;;;;;sDAER,8OAAC,gMAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAuB,IAAI;gDAAE,IAAI;4CAAI;sDACtD,cAAA,8OAAC;;kEACC,8OAAC,4IAAA,CAAA,OAAI;wDAAC,MAAM,wKAAA,CAAA,SAAM;wDAAE,MAAK;wDAAQ,QAAQ;;;;;;kEACzC,8OAAC,sMAAA,CAAA,aAAU;wDAAC,SAAQ;kEAAQ;;;;;;;;;;;;;;;;;sDAIhC,8OAAC,gMAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAuB,IAAI;gDAAE,IAAI;4CAAI;sDACtD,cAAA,8OAAC;;kEACC,8OAAC,4IAAA,CAAA,OAAI;wDAAC,MAAM,wKAAA,CAAA,QAAK;wDAAE,MAAK;wDAAQ,QAAQ;;;;;;kEACxC,8OAAC,sMAAA,CAAA,aAAU;wDAAC,SAAQ;kEAAQ;;;;;;;;;;;;;;;;;sDAKhC,8OAAC,gMAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAmB,IAAI;gDAAE,IAAI;4CAAI;sDAClD,cAAA,8OAAC;;kEACC,8OAAC,4IAAA,CAAA,OAAI;wDAAC,MAAM,wKAAA,CAAA,YAAS;wDAAE,MAAK;wDAAQ,QAAQ;;;;;;kEAC5C,8OAAC,sMAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,IAAI;4DAAE,MAAM;wDAAE;kEAAG;;;;;;kEAC7C,8OAAC,4IAAA,CAAA,OAAI;wDACH,MAAM,gBAAgB,wKAAA,CAAA,gBAAa,GAAG,wKAAA,CAAA,iBAAc;wDACpD,MAAK;wDACL,QAAQ;wDACR,IAAI;4DAAE,OAAO;4DAAkB,UAAU;wDAAU;;;;;;;;;;;;;;;;;sDAMzD,8OAAC,gMAAA,CAAA,WAAQ;4CAAC,IAAI;4CAAe,SAAQ;4CAAO,aAAa;sDACvD,cAAA,8OAAC,iLAAA,CAAA,MAAG;gDAAC,IAAI;oDAAE,IAAI;gDAAE;;kEAEf,8OAAC,gMAAA,CAAA,WAAQ;wDACP,SAAS,IAAM,kBAAkB;wDACjC,IAAI;4DACF,IAAI;4DACJ,SAAS,SAAS,UAAU,oBAAoB;wDAClD;kEAEA,cAAA,8OAAC;;8EACC,8OAAC,4IAAA,CAAA,OAAI;oEAAC,MAAM,wKAAA,CAAA,QAAK;oEAAE,MAAK;oEAAQ,QAAQ;oEAAC,IAAI;wEAAE,OAAO;oEAAe;;;;;;8EACrE,8OAAC,sMAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,IAAI;wEAAE,MAAM;oEAAE;8EAAG;;;;;;gEAC5C,SAAS,yBACR,8OAAC,4IAAA,CAAA,OAAI;oEAAC,MAAM,wKAAA,CAAA,UAAO;oEAAE,MAAK;oEAAQ,QAAQ;oEAAC,IAAI;wEAAE,OAAO;wEAAgB,UAAU;oEAAU;;;;;;;;;;;;;;;;;kEAMlG,8OAAC,gMAAA,CAAA,WAAQ;wDACP,SAAS,IAAM,kBAAkB;wDACjC,IAAI;4DACF,IAAI;4DACJ,SAAS,SAAS,SAAS,oBAAoB;wDACjD;kEAEA,cAAA,8OAAC;;8EACC,8OAAC,4IAAA,CAAA,OAAI;oEAAC,MAAM,wKAAA,CAAA,SAAM;oEAAE,MAAK;oEAAQ,QAAQ;oEAAC,IAAI;wEAAE,OAAO;oEAAY;;;;;;8EACnE,8OAAC,sMAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,IAAI;wEAAE,MAAM;oEAAE;8EAAG;;;;;;gEAC5C,SAAS,wBACR,8OAAC,4IAAA,CAAA,OAAI;oEAAC,MAAM,wKAAA,CAAA,UAAO;oEAAE,MAAK;oEAAQ,QAAQ;oEAAC,IAAI;wEAAE,OAAO;wEAAgB,UAAU;oEAAU;;;;;;;;;;;;;;;;;kEAMlG,8OAAC,gMAAA,CAAA,WAAQ;wDACP,SAAS,IAAM,kBAAkB;wDACjC,IAAI;4DACF,IAAI;4DACJ,SAAS,SAAS,WAAW,oBAAoB;wDACnD;kEAEA,cAAA,8OAAC;;8EACC,8OAAC,4IAAA,CAAA,OAAI;oEAAC,MAAM,wKAAA,CAAA,WAAQ;oEAAE,MAAK;oEAAQ,QAAQ;oEAAC,IAAI;wEAAE,OAAO;oEAAe;;;;;;8EACxE,8OAAC,sMAAA,CAAA,aAAU;oEAAC,SAAQ;oEAAQ,IAAI;wEAAE,MAAM;oEAAE;8EAAG;;;;;;gEAC5C,SAAS,0BACR,8OAAC,4IAAA,CAAA,OAAI;oEAAC,MAAM,wKAAA,CAAA,UAAO;oEAAE,MAAK;oEAAQ,QAAQ;oEAAC,IAAI;wEAAE,OAAO;wEAAgB,UAAU;oEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOtG,8OAAC,6LAAA,CAAA,UAAO;;;;;sDAER,8OAAC,gMAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAc,IAAI;gDAAE,IAAI;4CAAI;sDAC7C,cAAA,8OAAC;;kEACC,8OAAC,4IAAA,CAAA,OAAI;wDAAC,MAAM,wKAAA,CAAA,eAAY;wDAAE,MAAK;wDAAQ,QAAQ;;;;;;kEAC/C,8OAAC,sMAAA,CAAA,aAAU;wDAAC,SAAQ;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/sidebar/sidebar.style.ts"], "sourcesContent": ["import { styled } from \"@mui/material\";\r\nimport { List, ListItem, ListItemIcon, ListItemText, Badge, Tooltip, Box, Typography, Divider } from \"@mui/material\";\r\n\r\nexport const SidebarContainer = styled(\"aside\")(({ theme }) => ({\r\n  width: \"240px\",\r\n  height: \"calc(100vh - 64px)\",\r\n  position: \"fixed\",\r\n  top: \"64px\",\r\n  left: 0,\r\n  backgroundColor: theme.palette.background.paper, // Keep white background\r\n  color: theme.palette.text.primary,\r\n  borderRight: `1px solid ${theme.palette.divider}`,\r\n  overflowY: \"auto\",\r\n  overflowX: \"hidden\",\r\n  // No transition by default for better performance during normal use\r\n  transition: \"none\",\r\n  transform: \"translateZ(0) translateX(0)\", // Force hardware acceleration\r\n  backfaceVisibility: \"hidden\", // Optimize rendering\r\n  zIndex: theme.zIndex.drawer,\r\n  boxShadow: \"0 0 10px rgba(0,0,0,0.05)\",\r\n  // Only apply transitions when the with-transition class is present\r\n  \"&.with-transition\": {\r\n    transition: \"all 0.45s cubic-bezier(0.25, 1, 0.5, 1)\", // Improved easing for smoother motion\r\n    willChange: \"width, opacity, transform\", // Hint to browser for optimization\r\n  },\r\n  // Add subtle opacity transition for smoother appearance\r\n  \"&.with-transition:not(.icon-only)\": {\r\n    opacity: 1,\r\n  },\r\n  \"&.with-transition.icon-only\": {\r\n    opacity: 0.97, // Subtle opacity change for smoother transition\r\n  },\r\n  \"&.icon-only\": {\r\n    width: \"64px\",\r\n    \"& .MuiListItemIcon-root\": {\r\n      transform: \"translateX(0)\", // Reset icon position when collapsed\r\n    },\r\n  },\r\n  // Add sliding animation for content inside sidebar\r\n  \"& .MuiListItemIcon-root\": {\r\n    transform: \"translateX(0)\",\r\n    transition: \"none\",\r\n  },\r\n  \"&.with-transition .MuiListItemIcon-root\": {\r\n    transition: \"transform 0.45s cubic-bezier(0.25, 1, 0.5, 1), color 0.3s ease\", // Match main transition\r\n  },\r\n  \"&.with-transition.icon-only .MuiListItemIcon-root\": {\r\n    transform: \"translateX(8px)\", // Slide icons slightly when collapsed\r\n  },\r\n  // Add subtle scale effect to icons during transition\r\n  \"&.with-transition:not(.icon-only) .MuiListItemIcon-root svg\": {\r\n    transform: \"scale(1)\",\r\n    transition: \"transform 0.45s cubic-bezier(0.25, 1, 0.5, 1)\",\r\n  },\r\n  \"&.with-transition.icon-only .MuiListItemIcon-root svg\": {\r\n    transform: \"scale(1.1)\", // Slightly enlarge icons when collapsed\r\n    transition: \"transform 0.45s cubic-bezier(0.25, 1, 0.5, 1)\",\r\n  },\r\n  // Custom scrollbar\r\n  \"&::-webkit-scrollbar\": {\r\n    width: \"6px\",\r\n  },\r\n  \"&::-webkit-scrollbar-track\": {\r\n    background: \"transparent\",\r\n  },\r\n  \"&::-webkit-scrollbar-thumb\": {\r\n    background: theme.palette.mode === 'light'\r\n      ? \"rgba(255, 255, 255, 0.3)\"\r\n      : \"rgba(255, 255, 255, 0.2)\",\r\n    borderRadius: \"3px\",\r\n  },\r\n  \"&::-webkit-scrollbar-thumb:hover\": {\r\n    background: theme.palette.mode === 'light'\r\n      ? \"rgba(255, 255, 255, 0.5)\"\r\n      : \"rgba(255, 255, 255, 0.3)\",\r\n  },\r\n}));\r\n\r\nexport const SidebarHeader = styled(Box)(({ theme }) => ({\r\n  padding: theme.spacing(2),\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  borderBottom: `1px solid ${theme.palette.divider}`,\r\n  marginBottom: theme.spacing(1),\r\n  \".icon-only &\": {\r\n    justifyContent: \"center\",\r\n    padding: theme.spacing(1, 0),\r\n  },\r\n}));\r\n\r\nexport const LogoText = styled(Typography)(({ theme }) => ({\r\n  fontWeight: 700,\r\n  fontSize: \"1.2rem\",\r\n  color: theme.palette.text.primary,\r\n  marginLeft: theme.spacing(1),\r\n  \".icon-only &\": {\r\n    display: \"none\",\r\n  },\r\n}));\r\n\r\nexport const SidebarContent = styled(\"div\")(({ theme }) => ({\r\n  padding: theme.spacing(1, 0),\r\n}));\r\n\r\nexport const SidebarSection = styled(\"div\")(({ theme }) => ({\r\n  marginBottom: theme.spacing(1),\r\n}));\r\n\r\nexport const SectionDivider = styled(Divider)(({ theme }) => ({\r\n  margin: theme.spacing(1, 2),\r\n  backgroundColor: theme.palette.divider,\r\n  \".icon-only &\": {\r\n    margin: theme.spacing(1, 0.5),\r\n  },\r\n}));\r\n\r\nexport const StyledListItem = styled(ListItem)(({ theme }) => ({\r\n  padding: theme.spacing(1, 2),\r\n  cursor: \"pointer\",\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  transition: \"background-color 0.15s ease\", // Only transition what's necessary\r\n  position: \"relative\",\r\n  margin: theme.spacing(0.5, 0.75),\r\n  borderRadius: \"8px\",\r\n  color: theme.palette.text.primary,\r\n  transform: \"translateX(0)\",\r\n  \".with-transition &\": {\r\n    transition: \"transform 0.45s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.3s ease, color 0.3s ease\",\r\n  },\r\n  \".with-transition.icon-only &\": {\r\n    transform: \"translateX(-8px)\", // Slide items when sidebar collapses\r\n  },\r\n  // Add staggered animation for list items\r\n  \"&:nth-of-type(1)\": { transitionDelay: \"0.02s\" },\r\n  \"&:nth-of-type(2)\": { transitionDelay: \"0.04s\" },\r\n  \"&:nth-of-type(3)\": { transitionDelay: \"0.06s\" },\r\n  \"&:nth-of-type(4)\": { transitionDelay: \"0.08s\" },\r\n  \"&:nth-of-type(5)\": { transitionDelay: \"0.1s\" },\r\n  \"&:nth-of-type(6)\": { transitionDelay: \"0.12s\" },\r\n  \"&:nth-of-type(7)\": { transitionDelay: \"0.14s\" },\r\n  \"&:nth-of-type(8)\": { transitionDelay: \"0.16s\" },\r\n  \"&:hover\": {\r\n    backgroundColor: theme.palette.action.hover,\r\n    \"& .MuiListItemIcon-root\": {\r\n      color: theme.palette.primary.main, // Use color change instead of transform\r\n    },\r\n  },\r\n  \"&.primary-item\": {\r\n    \"& .MuiTypography-root\": {\r\n      fontWeight: 500,\r\n    },\r\n  },\r\n  \"&.selected\": {\r\n    backgroundColor: theme.palette.primary.main + \"15\",\r\n    color: theme.palette.primary.main,\r\n    fontWeight: 600,\r\n    \"&::before\": {\r\n      content: '\"\"',\r\n      position: \"absolute\",\r\n      left: 0,\r\n      top: 0,\r\n      bottom: 0,\r\n      width: \"4px\",\r\n      backgroundColor: theme.palette.primary.main,\r\n      borderRadius: \"0 4px 4px 0\",\r\n    },\r\n  },\r\n  \".icon-only &\": {\r\n    padding: theme.spacing(1.5, 0),\r\n    justifyContent: \"center\",\r\n    margin: theme.spacing(0.5, 0.5),\r\n    \"&.selected::before\": {\r\n      width: \"3px\",\r\n    },\r\n  },\r\n}));\r\n\r\nexport const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({\r\n  minWidth: \"40px\",\r\n  // Remove the hardcoded color to allow custom colors to work\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  transition: \"color 0.15s ease\", // Change from transform to color for better performance\r\n  \"& svg\": {\r\n    fontSize: \"1.1rem\", // Smaller icons\r\n  },\r\n}));\r\n\r\nexport const StyledBadge = styled(Badge)(({ theme }) => ({\r\n  \"& .MuiBadge-badge\": {\r\n    backgroundColor: theme.palette.error.main,\r\n    color: \"#fff\",\r\n    fontWeight: \"bold\",\r\n    fontSize: \"0.7rem\",\r\n  },\r\n}));\r\n\r\n// Hide expand/collapse arrows in icon-only mode\r\nexport const ExpandIconStyle = styled(\"span\")(({ theme }) => ({\r\n  color: theme.palette.mode === 'dark' ? '#FFFFFF' : theme.palette.text.secondary,\r\n  transition: \"transform 0.3s ease, color 0.3s ease\",\r\n  \".open &\": {\r\n    transform: \"rotate(180deg)\",\r\n  },\r\n  \".icon-only &\": {\r\n    display: \"none\",\r\n  },\r\n}));\r\n\r\nexport const StyledListItemText = styled(ListItemText)(({ theme }) => ({\r\n  \"& .MuiTypography-root\": {\r\n    fontSize: \"0.9rem\",\r\n    fontWeight: 500,\r\n  },\r\n  \".icon-only &\": {\r\n    display: \"none\",\r\n  },\r\n}));\r\n\r\nexport const NestedList = styled(List)(({ theme }) => ({\r\n  paddingLeft: theme.spacing(2),\r\n  \".icon-only &\": {\r\n    display: \"none\",\r\n  },\r\n}));\r\n\r\nexport const SidebarFooter = styled(Box)(({ theme }) => ({\r\n  padding: theme.spacing(1.5),\r\n  borderTop: `1px solid ${theme.palette.divider}`,\r\n  marginTop: \"auto\",\r\n  fontSize: \"0.75rem\",\r\n  color: theme.palette.mode === 'dark' ? '#FFFFFF' : theme.palette.text.secondary,\r\n  textAlign: \"center\",\r\n  \".icon-only &\": {\r\n    padding: theme.spacing(1),\r\n    \"& > span\": {\r\n      display: \"none\",\r\n    },\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC9D,OAAO;QACP,QAAQ;QACR,UAAU;QACV,KAAK;QACL,MAAM;QACN,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,aAAa,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QACjD,WAAW;QACX,WAAW;QACX,oEAAoE;QACpE,YAAY;QACZ,WAAW;QACX,oBAAoB;QACpB,QAAQ,MAAM,MAAM,CAAC,MAAM;QAC3B,WAAW;QACX,mEAAmE;QACnE,qBAAqB;YACnB,YAAY;YACZ,YAAY;QACd;QACA,wDAAwD;QACxD,qCAAqC;YACnC,SAAS;QACX;QACA,+BAA+B;YAC7B,SAAS;QACX;QACA,eAAe;YACb,OAAO;YACP,2BAA2B;gBACzB,WAAW;YACb;QACF;QACA,mDAAmD;QACnD,2BAA2B;YACzB,WAAW;YACX,YAAY;QACd;QACA,2CAA2C;YACzC,YAAY;QACd;QACA,qDAAqD;YACnD,WAAW;QACb;QACA,qDAAqD;QACrD,+DAA+D;YAC7D,WAAW;YACX,YAAY;QACd;QACA,yDAAyD;YACvD,WAAW;YACX,YAAY;QACd;QACA,mBAAmB;QACnB,wBAAwB;YACtB,OAAO;QACT;QACA,8BAA8B;YAC5B,YAAY;QACd;QACA,8BAA8B;YAC5B,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,UAC/B,6BACA;YACJ,cAAc;QAChB;QACA,oCAAoC;YAClC,YAAY,MAAM,OAAO,CAAC,IAAI,KAAK,UAC/B,6BACA;QACN;IACF,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,SAAS,MAAM,OAAO,CAAC;QACvB,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,cAAc,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QAClD,cAAc,MAAM,OAAO,CAAC;QAC5B,gBAAgB;YACd,gBAAgB;YAChB,SAAS,MAAM,OAAO,CAAC,GAAG;QAC5B;IACF,CAAC;AAEM,MAAM,WAAW,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,sMAAA,CAAA,aAAU,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACzD,YAAY;QACZ,UAAU;QACV,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,YAAY,MAAM,OAAO,CAAC;QAC1B,gBAAgB;YACd,SAAS;QACX;IACF,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC1D,SAAS,MAAM,OAAO,CAAC,GAAG;IAC5B,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC1D,cAAc,MAAM,OAAO,CAAC;IAC9B,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,6LAAA,CAAA,UAAO,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC5D,QAAQ,MAAM,OAAO,CAAC,GAAG;QACzB,iBAAiB,MAAM,OAAO,CAAC,OAAO;QACtC,gBAAgB;YACd,QAAQ,MAAM,OAAO,CAAC,GAAG;QAC3B;IACF,CAAC;AAEM,MAAM,iBAAiB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,gMAAA,CAAA,WAAQ,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC7D,SAAS,MAAM,OAAO,CAAC,GAAG;QAC1B,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,QAAQ,MAAM,OAAO,CAAC,KAAK;QAC3B,cAAc;QACd,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,WAAW;QACX,sBAAsB;YACpB,YAAY;QACd;QACA,gCAAgC;YAC9B,WAAW;QACb;QACA,yCAAyC;QACzC,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,oBAAoB;YAAE,iBAAiB;QAAO;QAC9C,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,oBAAoB;YAAE,iBAAiB;QAAQ;QAC/C,WAAW;YACT,iBAAiB,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK;YAC3C,2BAA2B;gBACzB,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACnC;QACF;QACA,kBAAkB;YAChB,yBAAyB;gBACvB,YAAY;YACd;QACF;QACA,cAAc;YACZ,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,GAAG;YAC9C,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YACjC,YAAY;YACZ,aAAa;gBACX,SAAS;gBACT,UAAU;gBACV,MAAM;gBACN,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,iBAAiB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;gBAC3C,cAAc;YAChB;QACF;QACA,gBAAgB;YACd,SAAS,MAAM,OAAO,CAAC,KAAK;YAC5B,gBAAgB;YAChB,QAAQ,MAAM,OAAO,CAAC,KAAK;YAC3B,sBAAsB;gBACpB,OAAO;YACT;QACF;IACF,CAAC;AAEM,MAAM,qBAAqB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,4MAAA,CAAA,eAAY,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACrE,UAAU;QACV,4DAA4D;QAC5D,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,YAAY;QACZ,SAAS;YACP,UAAU;QACZ;IACF,CAAC;AAEM,MAAM,cAAc,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,uLAAA,CAAA,QAAK,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,qBAAqB;YACnB,iBAAiB,MAAM,OAAO,CAAC,KAAK,CAAC,IAAI;YACzC,OAAO;YACP,YAAY;YACZ,UAAU;QACZ;IACF,CAAC;AAGM,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC5D,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QAC/E,YAAY;QACZ,WAAW;YACT,WAAW;QACb;QACA,gBAAgB;YACd,SAAS;QACX;IACF,CAAC;AAEM,MAAM,qBAAqB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,4MAAA,CAAA,eAAY,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACrE,yBAAyB;YACvB,UAAU;YACV,YAAY;QACd;QACA,gBAAgB;YACd,SAAS;QACX;IACF,CAAC;AAEM,MAAM,aAAa,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,oLAAA,CAAA,OAAI,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACrD,aAAa,MAAM,OAAO,CAAC;QAC3B,gBAAgB;YACd,SAAS;QACX;IACF,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACvD,SAAS,MAAM,OAAO,CAAC;QACvB,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QAC/C,WAAW;QACX,UAAU;QACV,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QAC/E,WAAW;QACX,gBAAgB;YACd,SAAS,MAAM,OAAO,CAAC;YACvB,YAAY;gBACV,SAAS;YACX;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 1761, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/json/sidebarData/admin.ts"], "sourcesContent": ["import { IMenuItem } from \"@/interfaces\";\r\nimport {\r\n  faDashboard,\r\n  faUserShield,\r\n  faUsers,\r\n  faUserTie,\r\n  faPlugCircleBolt,\r\n  faEnvelopeCircleCheck,\r\n  faGavel,\r\n  faChartLine,\r\n  faShieldVirus,\r\n  faFolderClosed,\r\n  faDatabase,\r\n  faTriangleExclamation,\r\n  faBug,\r\n  faUser,\r\n  faKey,\r\n  faLaptop,\r\n  faFileLines,\r\n  faPlay,\r\n  faCheck,\r\n  faLink,\r\n  faGlobe,\r\n  faEnvelope,\r\n  faClipboardList,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\n\r\n\r\n// Admin menu items\r\nexport const adminMenuItems: IMenuItem[] = [\r\n  {\r\n    title: \"Dashboard\",\r\n    path: \"/dashboard\",\r\n    icon: faDashboard,\r\n  },\r\n  {\r\n    title: \"Super Admin\",\r\n    icon: faUserTie,\r\n    submenu: [\r\n      {\r\n        title: \"Dashboard\",\r\n        path: \"/super-admin/dashboard\",\r\n        icon: faDashboard,\r\n      },\r\n      {\r\n        title: \"Customers\",\r\n        path: \"/super-admin/customers\",\r\n        icon: faUsers,\r\n      },\r\n      {\r\n        title: \"Users\",\r\n        path: \"/super-admin/users\",\r\n        icon: faUser,\r\n      },\r\n      {\r\n        title: \"Licenses\",\r\n        path: \"/super-admin/licenses\",\r\n        icon: faKey,\r\n      },\r\n      {\r\n        title: \"All Logs Report\",\r\n        path: \"/super-admin/logs-report\",\r\n        icon: faClipboardList,\r\n      },\r\n      {\r\n        title: \"Profile\",\r\n        path: \"/super-admin/profile\",\r\n        icon: faUser,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Plugin\",\r\n    icon: faPlugCircleBolt,\r\n    submenu: [\r\n      {\r\n        title: \"Available License\",\r\n        path: \"/plugin/available-license\",\r\n        icon: faKey,\r\n      },\r\n      {\r\n        title: \"Allocated License\",\r\n        path: \"/plugin/allocated-license\",\r\n        icon: faKey,\r\n      },\r\n      {\r\n        title: \"Agent Installed\",\r\n        path: \"/plugin/agent-installed\",\r\n        icon: faLaptop,\r\n      },\r\n      {\r\n        title: \"License Report\",\r\n        path: \"/plugin/license-report\",\r\n        icon: faFileLines,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Phishing\",\r\n    path: \"/phishing\",\r\n    icon: faEnvelopeCircleCheck,\r\n  },\r\n  {\r\n    title: \"Disputes\",\r\n    icon: faGavel,\r\n    submenu: [\r\n      {\r\n        title: \"Pending Disputes\",\r\n        path: \"/disputes/pending\",\r\n        icon: faTriangleExclamation,\r\n      },\r\n      {\r\n        title: \"Resolved Disputes\",\r\n        path: \"/disputes/resolved\",\r\n        icon: faCheck,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Reports\",\r\n    path: \"/reports\",\r\n    icon: faChartLine,\r\n  },\r\n  {\r\n    title: \"Sandbox\",\r\n    icon: faShieldVirus,\r\n    submenu: [\r\n      {\r\n        title: \"Running Sandbox\",\r\n        path: \"/sandbox/running\",\r\n        icon: faPlay,\r\n      },\r\n      {\r\n        title: \"Completed Sandbox\",\r\n        path: \"/sandbox/completed\",\r\n        icon: faCheck,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Quarantine\",\r\n    path: \"/quarantine\",\r\n    icon: faFolderClosed,\r\n  },\r\n  {\r\n    title: \"Rogue DB\",\r\n    icon: faDatabase,\r\n    submenu: [\r\n      {\r\n        title: \"URL\",\r\n        path: \"/rogue-db/url\",\r\n        icon: faLink,\r\n      },\r\n      {\r\n        title: \"Domain\",\r\n        path: \"/rogue-db/domain\",\r\n        icon: faGlobe,\r\n      },\r\n      {\r\n        title: \"Mail\",\r\n        path: \"/rogue-db/mail\",\r\n        icon: faEnvelope,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Logs\",\r\n    icon: faClipboardList,\r\n    submenu: [\r\n      {\r\n        title: \"Exception Logs\",\r\n        path: \"/exception-logs\",\r\n        icon: faTriangleExclamation,\r\n      },\r\n      {\r\n        title: \"Error Logs\",\r\n        path: \"/error-logs\",\r\n        icon: faBug,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Profile\",\r\n    path: \"/profile\",\r\n    icon: faUser,\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;AACA;;AA4BO,MAAM,iBAA8B;IACzC;QACE,OAAO;QACP,MAAM;QACN,MAAM,wKAAA,CAAA,cAAW;IACnB;IACA;QACE,OAAO;QACP,MAAM,wKAAA,CAAA,YAAS;QACf,SAAS;YACP;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,cAAW;YACnB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,UAAO;YACf;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,SAAM;YACd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,QAAK;YACb;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,kBAAe;YACvB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,SAAM;YACd;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM,wKAAA,CAAA,mBAAgB;QACtB,SAAS;YACP;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,QAAK;YACb;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,QAAK;YACb;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,WAAQ;YAChB;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,cAAW;YACnB;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,wKAAA,CAAA,wBAAqB;IAC7B;IACA;QACE,OAAO;QACP,MAAM,wKAAA,CAAA,UAAO;QACb,SAAS;YACP;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,wBAAqB;YAC7B;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,UAAO;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,wKAAA,CAAA,cAAW;IACnB;IACA;QACE,OAAO;QACP,MAAM,wKAAA,CAAA,gBAAa;QACnB,SAAS;YACP;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,SAAM;YACd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,UAAO;YACf;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,wKAAA,CAAA,iBAAc;IACtB;IACA;QACE,OAAO;QACP,MAAM,wKAAA,CAAA,aAAU;QAChB,SAAS;YACP;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,SAAM;YACd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,UAAO;YACf;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,aAAU;YAClB;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM,wKAAA,CAAA,kBAAe;QACrB,SAAS;YACP;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,wBAAqB;YAC7B;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,MAAM,wKAAA,CAAA,QAAK;YACb;SACD;IACH;IACA;QACE,OAAO;QACP,MAAM;QACN,MAAM,wKAAA,CAAA,SAAM;IACd;CACD", "debugId": null}}, {"offset": {"line": 1930, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/sidebar/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { List, Collapse, Tooltip, useTheme } from \"@mui/material\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { ExpandLess, ExpandMore } from \"@mui/icons-material\";\r\nimport Link from \"next/link\";\r\n\r\nimport { Icon } from \"@/components/common/icon\";\r\nimport {\r\n  SidebarContainer,\r\n  SidebarContent,\r\n  StyledListItem,\r\n  StyledListItemIcon,\r\n  StyledListItemText,\r\n  ExpandIconStyle,\r\n  NestedList,\r\n  SidebarSection,\r\n  SectionDivider,\r\n  StyledBadge,\r\n  SidebarFooter,\r\n} from \"./sidebar.style\";\r\nimport { adminMenuItems } from \"@/json/sidebarData/admin\";\r\nimport { IMenuItem } from \"@/interfaces\";\r\n\r\nexport const Sidebar: React.FC<{ isOpen?: boolean }> = ({ isOpen = true }) => {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const theme = useTheme(); // Import theme to detect dark/light mode\r\n  const [openSubMenu, setOpenSubMenu] = useState<string | null>(null);\r\n  const [selectedItem, setSelectedItem] = useState<string | null>(null);\r\n  const [menuItems, setMenuItems] = useState<IMenuItem[]>([]);\r\n  const [hiddenBadges, setHiddenBadges] = useState<{ [key: string]: boolean }>({});\r\n  const timeoutRefs = React.useRef<{ [key: string]: NodeJS.Timeout }>({});\r\n\r\n  useEffect(() => {\r\n    // Get user role from localStorage Basu\r\n    // const userRole = localStorage.getItem(\"userRole\");\r\n\r\n    // Use admin menu items\r\n    setMenuItems(adminMenuItems);\r\n\r\n    // Determine which menu to show based on user role\r\n    // if (userRole === \"admin\" || userRole === \"approver\") {\r\n    //   setMenuItems(adminMenuItems);\r\n    // } else if (userRole === \"executor\") {\r\n    //   setMenuItems(executorMenuItems);\r\n    // }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!pathname) return; // Guard against null pathname\r\n\r\n    // Check if the current path is a details page\r\n    const isExceptionDetailsPage = pathname.startsWith('/exception-logs/details/');\r\n    const isErrorDetailsPage = pathname.startsWith('/error-logs/details/');\r\n\r\n    // Special handling for profile paths to distinguish between regular profile and super admin profile\r\n    const isSuperAdminProfile = pathname === '/super-admin/profile';\r\n    const isRegularProfile = pathname === '/profile';\r\n\r\n    // If it's a details page, set the parent path as selected\r\n    if (isExceptionDetailsPage) {\r\n      setSelectedItem('/exception-logs');\r\n    } else if (isErrorDetailsPage) {\r\n      setSelectedItem('/error-logs');\r\n    } else {\r\n      setSelectedItem(pathname);\r\n    }\r\n\r\n    menuItems.forEach((item) => {\r\n      if (item.submenu) {\r\n        const hasSelectedChild = item.submenu.some(\r\n          (subItem) => {\r\n            // Special handling for profile paths\r\n            if (subItem.path === '/super-admin/profile' && isSuperAdminProfile) {\r\n              return true;\r\n            }\r\n            if (subItem.path === '/profile' && isRegularProfile) {\r\n              return true;\r\n            }\r\n\r\n            // Check if the current path starts with the submenu path\r\n            // This handles both exact matches and detail pages\r\n            if (subItem.path && pathname.startsWith(subItem.path) &&\r\n                // Exclude profile paths from the startsWith check to prevent overlap\r\n                subItem.path !== '/profile' &&\r\n                !pathname.startsWith('/super-admin/profile')) {\r\n              return true;\r\n            }\r\n\r\n            // Also check for exact matches\r\n            return subItem.path === pathname;\r\n          }\r\n        );\r\n        if (hasSelectedChild) {\r\n          setOpenSubMenu(item.title);\r\n        }\r\n      }\r\n    });\r\n  }, [pathname, menuItems]);\r\n\r\n  // Cleanup timeouts when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      // Clear all timeouts when component unmounts\r\n      Object.values(timeoutRefs.current).forEach(timeout => {\r\n        clearTimeout(timeout);\r\n      });\r\n    };\r\n  }, []);\r\n\r\n  const handleClick = (path?: string, title?: string) => {\r\n    if (path) {\r\n      setSelectedItem(path);\r\n      // Use router.push for all navigation to prevent page reload\r\n      router.push(path);\r\n    } else if (title) {\r\n      setOpenSubMenu(openSubMenu === title ? null : title);\r\n    }\r\n\r\n    // If the clicked item has a notification badge, hide it temporarily\r\n    if (title && getNotificationCount(title) !== null) {\r\n      // Update hidden badges state\r\n      setHiddenBadges(prev => ({ ...prev, [title]: true }));\r\n\r\n      // Clear any existing timeout for this item\r\n      if (timeoutRefs.current[title]) {\r\n        clearTimeout(timeoutRefs.current[title]);\r\n      }\r\n\r\n      // Set a timeout to make the badge reappear after 2-3 minutes\r\n      const timeoutDuration = Math.floor(Math.random() * (180000 - 120000 + 1)) + 120000; // Random time between 2-3 minutes\r\n      timeoutRefs.current[title] = setTimeout(() => {\r\n        setHiddenBadges(prev => ({ ...prev, [title]: false }));\r\n        delete timeoutRefs.current[title];\r\n      }, timeoutDuration);\r\n    }\r\n  };\r\n\r\n  // Function to get notification count for menu items\r\n  const getNotificationCount = (title: string): number | null => {\r\n    // Always return null to hide all badges\r\n    return null;\r\n  };\r\n\r\n  // Function to determine if a menu item should have the primary color\r\n  const shouldUsePrimaryColor = (_title: string): boolean => {\r\n    return true; // Return true for all menu items to make all icons use the primary color\r\n  };\r\n\r\n  // Function to get the icon color based on the menu item and theme\r\n  const getIconColor = (_title: string, _icon: any, isSelected: boolean): string => {\r\n    if (isSelected) {\r\n      return theme.palette.primary.main;\r\n    }\r\n    // Return white color for dark mode, black for light mode\r\n    return theme.palette.mode === 'dark' ? '#FFFFFF' : '#000000';\r\n  };\r\n\r\n  const renderMenuItem = (item: IMenuItem) => {\r\n    const hasSubmenu = item.submenu && item.submenu.length > 0;\r\n    const isOpen = openSubMenu === item.title;\r\n\r\n    // Special handling for profile paths\r\n    const isSuperAdminProfile = pathname === '/super-admin/profile';\r\n    const isRegularProfile = pathname === '/profile';\r\n\r\n    // Check if this menu item should be selected\r\n    // For items with submenu, check if any child path is selected\r\n    let isSelected = false;\r\n\r\n    // Handle the main profile item (not in submenu)\r\n    if (item.path === '/profile' && isRegularProfile && !isSuperAdminProfile) {\r\n      isSelected = true;\r\n    } else if (item.path !== '/profile') {\r\n      // For non-profile items, use the normal selection logic\r\n      isSelected = item.path === selectedItem;\r\n    }\r\n\r\n    // For parent items with submenu, they should be selected if any of their children are selected\r\n    if (hasSubmenu && pathname && item.submenu) {\r\n      const hasSelectedChild = item.submenu.some(\r\n        subItem => {\r\n          // Special handling for profile paths in submenu\r\n          if (subItem.path === '/super-admin/profile' && isSuperAdminProfile) {\r\n            return true;\r\n          }\r\n          if (subItem.path === '/profile' && isRegularProfile && !isSuperAdminProfile) {\r\n            return true;\r\n          }\r\n\r\n          return subItem.path === selectedItem ||\r\n                 (subItem.path && pathname.startsWith(`${subItem.path}/details/`));\r\n        }\r\n      );\r\n      if (hasSelectedChild) {\r\n        isSelected = true;\r\n      }\r\n    }\r\n\r\n    const notificationCount = getNotificationCount(item.title);\r\n    const usePrimaryColor = shouldUsePrimaryColor(item.title);\r\n\r\n    // Wrap with tooltip when in collapsed mode\r\n    const menuItem = (\r\n      <React.Fragment>\r\n        {item.path && !hasSubmenu ? (\r\n          <Link href={item.path} passHref style={{ textDecoration: 'none', color: 'inherit' }}>\r\n            <StyledListItem\r\n              onClick={() => {\r\n                setSelectedItem(item.path || '');\r\n\r\n                // If the clicked item has a notification badge, hide it temporarily\r\n                if (notificationCount !== null) {\r\n                  // Update hidden badges state\r\n                  setHiddenBadges(prev => ({ ...prev, [item.title]: true }));\r\n\r\n                  // Clear any existing timeout for this item\r\n                  if (timeoutRefs.current[item.title]) {\r\n                    clearTimeout(timeoutRefs.current[item.title]);\r\n                  }\r\n\r\n                  // Set a timeout to make the badge reappear after 2-3 minutes\r\n                  const timeoutDuration = Math.floor(Math.random() * (180000 - 120000 + 1)) + 120000; // Random time between 2-3 minutes\r\n                  timeoutRefs.current[item.title] = setTimeout(() => {\r\n                    setHiddenBadges(prev => ({ ...prev, [item.title]: false }));\r\n                    delete timeoutRefs.current[item.title];\r\n                  }, timeoutDuration);\r\n                }\r\n              }}\r\n              className={`${isSelected ? \"selected\" : \"\"} ${usePrimaryColor ? \"primary-item\" : \"\"}`}\r\n            >\r\n              <StyledListItemIcon>\r\n                {notificationCount ? (\r\n                  <StyledBadge badgeContent={notificationCount} color=\"error\">\r\n                    <Icon\r\n                      icon={item.icon}\r\n                      size=\"small\"\r\n                      onlyIcon\r\n                      color={getIconColor(item.title, item.icon, isSelected)}\r\n                    />\r\n                  </StyledBadge>\r\n                ) : (\r\n                  <Icon\r\n                    icon={item.icon}\r\n                    size=\"small\"\r\n                    onlyIcon\r\n                    color={getIconColor(item.title, item.icon, isSelected)}\r\n                  />\r\n                )}\r\n              </StyledListItemIcon>\r\n              <StyledListItemText primary={item.title} />\r\n            </StyledListItem>\r\n          </Link>\r\n        ) : (\r\n          <StyledListItem\r\n            onClick={() => handleClick(item.path, item.title)}\r\n            className={`${isSelected ? \"selected\" : \"\"} ${isOpen ? \"open\" : \"\"} ${usePrimaryColor ? \"primary-item\" : \"\"}`}\r\n          >\r\n            <StyledListItemIcon>\r\n              {notificationCount ? (\r\n                <StyledBadge badgeContent={notificationCount} color=\"error\">\r\n                  <Icon\r\n                    icon={item.icon}\r\n                    size=\"small\"\r\n                    onlyIcon\r\n                    color={getIconColor(item.title, item.icon, isSelected)}\r\n                  />\r\n                </StyledBadge>\r\n              ) : (\r\n                <Icon\r\n                  icon={item.icon}\r\n                  size=\"small\"\r\n                  onlyIcon\r\n                  color={getIconColor(item.title, item.icon, isSelected)}\r\n                />\r\n              )}\r\n            </StyledListItemIcon>\r\n            <StyledListItemText primary={item.title} />\r\n            {hasSubmenu && (\r\n              <ExpandIconStyle className={isOpen ? \"open\" : \"\"}>\r\n                {isOpen ? <ExpandLess /> : <ExpandMore />}\r\n              </ExpandIconStyle>\r\n            )}\r\n          </StyledListItem>\r\n        )}\r\n\r\n        {hasSubmenu && (\r\n          <Collapse in={isOpen} timeout=\"auto\" unmountOnExit>\r\n            <NestedList>\r\n              {item.submenu?.map((subItem) => {\r\n                // Special handling for profile paths\r\n                const isSuperAdminProfile = pathname === '/super-admin/profile';\r\n                const isRegularProfile = pathname === '/profile';\r\n\r\n                // Check if this submenu item should be selected\r\n                // This handles both exact matches and detail pages\r\n                let isSubItemSelected = false;\r\n\r\n                // Special handling for profile paths in submenu\r\n                if (subItem.path === '/super-admin/profile' && isSuperAdminProfile) {\r\n                  isSubItemSelected = true;\r\n                } else if (subItem.path === '/profile' && isRegularProfile && !isSuperAdminProfile) {\r\n                  isSubItemSelected = true;\r\n                } else {\r\n                  isSubItemSelected = Boolean(\r\n                    subItem.path === selectedItem ||\r\n                    (pathname && subItem.path && pathname.startsWith(`${subItem.path}/details/`))\r\n                  );\r\n                }\r\n\r\n                const subNotificationCount = getNotificationCount(subItem.title);\r\n                // Pass the primary color to submenu items if parent has it\r\n                const subUsePrimaryColor = usePrimaryColor;\r\n\r\n                return (\r\n                  <Link\r\n                    key={subItem.title}\r\n                    href={subItem.path || ''}\r\n                    passHref\r\n                    style={{ textDecoration: 'none', color: 'inherit' }}\r\n                  >\r\n                    <StyledListItem\r\n                      onClick={() => {\r\n                        setSelectedItem(subItem.path || '');\r\n\r\n                        // If the clicked submenu item has a notification badge, hide it temporarily\r\n                        if (subNotificationCount !== null) {\r\n                          // Update hidden badges state\r\n                          setHiddenBadges(prev => ({ ...prev, [subItem.title]: true }));\r\n\r\n                          // Clear any existing timeout for this item\r\n                          if (timeoutRefs.current[subItem.title]) {\r\n                            clearTimeout(timeoutRefs.current[subItem.title]);\r\n                          }\r\n\r\n                          // Set a timeout to make the badge reappear after 2-3 minutes\r\n                          const timeoutDuration = Math.floor(Math.random() * (180000 - 120000 + 1)) + 120000; // Random time between 2-3 minutes\r\n                          timeoutRefs.current[subItem.title] = setTimeout(() => {\r\n                            setHiddenBadges(prev => ({ ...prev, [subItem.title]: false }));\r\n                            delete timeoutRefs.current[subItem.title];\r\n                          }, timeoutDuration);\r\n                        }\r\n                      }}\r\n                      className={`${isSubItemSelected ? \"selected\" : \"\"} ${subUsePrimaryColor ? \"primary-item\" : \"\"}`}\r\n                    >\r\n                      <StyledListItemIcon>\r\n                        {subNotificationCount ? (\r\n                          <StyledBadge badgeContent={subNotificationCount} color=\"error\">\r\n                            <Icon\r\n                              icon={subItem.icon}\r\n                              size=\"small\"\r\n                              onlyIcon\r\n                              color={getIconColor(subItem.title, subItem.icon, isSubItemSelected)}\r\n                            />\r\n                          </StyledBadge>\r\n                        ) : (\r\n                          <Icon\r\n                            icon={subItem.icon}\r\n                            size=\"small\"\r\n                            onlyIcon\r\n                            color={getIconColor(subItem.title, subItem.icon, isSubItemSelected)}\r\n                          />\r\n                        )}\r\n                      </StyledListItemIcon>\r\n                      <StyledListItemText primary={subItem.title} />\r\n                    </StyledListItem>\r\n                  </Link>\r\n                );\r\n              })}\r\n            </NestedList>\r\n          </Collapse>\r\n        )}\r\n      </React.Fragment>\r\n    );\r\n\r\n    // Add tooltip for collapsed sidebar\r\n    return !isOpen ? (\r\n      <Tooltip\r\n        title={item.title}\r\n        placement=\"right\"\r\n        arrow\r\n        disableHoverListener={!isOpen || Boolean(item.submenu)}\r\n      >\r\n        <div>{menuItem}</div>\r\n      </Tooltip>\r\n    ) : menuItem;\r\n  };\r\n\r\n  // Group menu items by category for better organization\r\n  const groupedMenuItems = () => {\r\n    const groups = [\r\n      { title: \"Main\", items: menuItems.filter(item => [\"Dashboard\", \"Profile\"].includes(item.title)) },\r\n      { title: \"Management\", items: menuItems.filter(item => [\"Super Admin\", \"Approver\", \"Executor\", \"Plugin\", \"Phishing\", \"Disputes\", \"Reports\"].includes(item.title)) },\r\n      { title: \"Security\", items: menuItems.filter(item => [\"Sandbox\", \"Quarantine\", \"Rogue DB\"].includes(item.title)) },\r\n      { title: \"System\", items: menuItems.filter(item => [\"Logs\"].includes(item.title)) },\r\n    ];\r\n\r\n    return groups.filter(group => group.items.length > 0);\r\n  };\r\n\r\n  return (\r\n    <SidebarContainer className={`sidebar-container ${isOpen ? \"\" : \"icon-only\"}`}>\r\n      {/* Header section removed */}\r\n\r\n      <SidebarContent>\r\n        {/* Render menu items by category */}\r\n        {groupedMenuItems().map((group, index) => (\r\n          <SidebarSection key={group.title}>\r\n            {index > 0 && <SectionDivider />}\r\n            <List component=\"nav\">\r\n              {group.items.map((item) => (\r\n                <React.Fragment key={item.title}>\r\n                  {renderMenuItem(item)}\r\n                </React.Fragment>\r\n              ))}\r\n            </List>\r\n          </SidebarSection>\r\n        ))}\r\n      </SidebarContent>\r\n\r\n      {/* Footer with version info */}\r\n      <SidebarFooter>\r\n        <span>v2.0.0 • © 2023</span>\r\n      </SidebarFooter>\r\n    </SidebarContainer>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;AAaA;AAtBA;;;;;;;;;;;AAyBO,MAAM,UAA0C,CAAC,EAAE,SAAS,IAAI,EAAE;IACvE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,WAAQ,AAAD,KAAK,yCAAyC;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAC9E,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAoC,CAAC;IAErE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAuC;QACvC,qDAAqD;QAErD,uBAAuB;QACvB,aAAa,mIAAA,CAAA,iBAAc;IAE3B,kDAAkD;IAClD,yDAAyD;IACzD,kCAAkC;IAClC,wCAAwC;IACxC,qCAAqC;IACrC,IAAI;IACN,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU,QAAQ,8BAA8B;QAErD,8CAA8C;QAC9C,MAAM,yBAAyB,SAAS,UAAU,CAAC;QACnD,MAAM,qBAAqB,SAAS,UAAU,CAAC;QAE/C,oGAAoG;QACpG,MAAM,sBAAsB,aAAa;QACzC,MAAM,mBAAmB,aAAa;QAEtC,0DAA0D;QAC1D,IAAI,wBAAwB;YAC1B,gBAAgB;QAClB,OAAO,IAAI,oBAAoB;YAC7B,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;QAEA,UAAU,OAAO,CAAC,CAAC;YACjB,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,mBAAmB,KAAK,OAAO,CAAC,IAAI,CACxC,CAAC;oBACC,qCAAqC;oBACrC,IAAI,QAAQ,IAAI,KAAK,0BAA0B,qBAAqB;wBAClE,OAAO;oBACT;oBACA,IAAI,QAAQ,IAAI,KAAK,cAAc,kBAAkB;wBACnD,OAAO;oBACT;oBAEA,yDAAyD;oBACzD,mDAAmD;oBACnD,IAAI,QAAQ,IAAI,IAAI,SAAS,UAAU,CAAC,QAAQ,IAAI,KAChD,qEAAqE;oBACrE,QAAQ,IAAI,KAAK,cACjB,CAAC,SAAS,UAAU,CAAC,yBAAyB;wBAChD,OAAO;oBACT;oBAEA,+BAA+B;oBAC/B,OAAO,QAAQ,IAAI,KAAK;gBAC1B;gBAEF,IAAI,kBAAkB;oBACpB,eAAe,KAAK,KAAK;gBAC3B;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,6CAA6C;YAC7C,OAAO,MAAM,CAAC,YAAY,OAAO,EAAE,OAAO,CAAC,CAAA;gBACzC,aAAa;YACf;QACF;IACF,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC,MAAe;QAClC,IAAI,MAAM;YACR,gBAAgB;YAChB,4DAA4D;YAC5D,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,OAAO;YAChB,eAAe,gBAAgB,QAAQ,OAAO;QAChD;QAEA,oEAAoE;QACpE,IAAI,SAAS,qBAAqB,WAAW,MAAM;;QAenD;IACF;IAEA,oDAAoD;IACpD,MAAM,uBAAuB,CAAC;QAC5B,wCAAwC;QACxC,OAAO;IACT;IAEA,qEAAqE;IACrE,MAAM,wBAAwB,CAAC;QAC7B,OAAO,MAAM,yEAAyE;IACxF;IAEA,kEAAkE;IAClE,MAAM,eAAe,CAAC,QAAgB,OAAY;QAChD,IAAI,YAAY;YACd,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QACnC;QACA,yDAAyD;QACzD,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,SAAS,YAAY;IACrD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG;QACzD,MAAM,SAAS,gBAAgB,KAAK,KAAK;QAEzC,qCAAqC;QACrC,MAAM,sBAAsB,aAAa;QACzC,MAAM,mBAAmB,aAAa;QAEtC,6CAA6C;QAC7C,8DAA8D;QAC9D,IAAI,aAAa;QAEjB,gDAAgD;QAChD,IAAI,KAAK,IAAI,KAAK,cAAc,oBAAoB,CAAC,qBAAqB;YACxE,aAAa;QACf,OAAO,IAAI,KAAK,IAAI,KAAK,YAAY;YACnC,wDAAwD;YACxD,aAAa,KAAK,IAAI,KAAK;QAC7B;QAEA,+FAA+F;QAC/F,IAAI,cAAc,YAAY,KAAK,OAAO,EAAE;YAC1C,MAAM,mBAAmB,KAAK,OAAO,CAAC,IAAI,CACxC,CAAA;gBACE,gDAAgD;gBAChD,IAAI,QAAQ,IAAI,KAAK,0BAA0B,qBAAqB;oBAClE,OAAO;gBACT;gBACA,IAAI,QAAQ,IAAI,KAAK,cAAc,oBAAoB,CAAC,qBAAqB;oBAC3E,OAAO;gBACT;gBAEA,OAAO,QAAQ,IAAI,KAAK,gBAChB,QAAQ,IAAI,IAAI,SAAS,UAAU,CAAC,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC;YACxE;YAEF,IAAI,kBAAkB;gBACpB,aAAa;YACf;QACF;QAEA,MAAM,oBAAoB,qBAAqB,KAAK,KAAK;QACzD,MAAM,kBAAkB,sBAAsB,KAAK,KAAK;QAExD,2CAA2C;QAC3C,MAAM,yBACJ,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;gBACZ,KAAK,IAAI,IAAI,CAAC,2BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAM,KAAK,IAAI;oBAAE,QAAQ;oBAAC,OAAO;wBAAE,gBAAgB;wBAAQ,OAAO;oBAAU;8BAChF,cAAA,8OAAC,0JAAA,CAAA,iBAAc;wBACb,SAAS;4BACP,gBAAgB,KAAK,IAAI,IAAI;4BAE7B,oEAAoE;4BACpE,uCAAgC;;4BAehC;wBACF;wBACA,WAAW,GAAG,aAAa,aAAa,GAAG,CAAC,EAAE,uCAAkB,uDAAqB;;0CAErF,8OAAC,0JAAA,CAAA,qBAAkB;0CAChB,2FAUC,8OAAC,4IAAA,CAAA,OAAI;oCACH,MAAM,KAAK,IAAI;oCACf,MAAK;oCACL,QAAQ;oCACR,OAAO,aAAa,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE;;;;;;;;;;;0CAIjD,8OAAC,0JAAA,CAAA,qBAAkB;gCAAC,SAAS,KAAK,KAAK;;;;;;;;;;;;;;;;yCAI3C,8OAAC,0JAAA,CAAA,iBAAc;oBACb,SAAS,IAAM,YAAY,KAAK,IAAI,EAAE,KAAK,KAAK;oBAChD,WAAW,GAAG,aAAa,aAAa,GAAG,CAAC,EAAE,SAAS,SAAS,GAAG,CAAC,EAAE,uCAAkB,uDAAqB;;sCAE7G,8OAAC,0JAAA,CAAA,qBAAkB;sCAChB,2FAUC,8OAAC,4IAAA,CAAA,OAAI;gCACH,MAAM,KAAK,IAAI;gCACf,MAAK;gCACL,QAAQ;gCACR,OAAO,aAAa,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE;;;;;;;;;;;sCAIjD,8OAAC,0JAAA,CAAA,qBAAkB;4BAAC,SAAS,KAAK,KAAK;;;;;;wBACtC,4BACC,8OAAC,0JAAA,CAAA,kBAAe;4BAAC,WAAW,SAAS,SAAS;sCAC3C,uBAAS,8OAAC,wJAAA,CAAA,UAAU;;;;qDAAM,8OAAC,wJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;gBAM7C,4BACC,8OAAC,gMAAA,CAAA,WAAQ;oBAAC,IAAI;oBAAQ,SAAQ;oBAAO,aAAa;8BAChD,cAAA,8OAAC,0JAAA,CAAA,aAAU;kCACR,KAAK,OAAO,EAAE,IAAI,CAAC;4BAClB,qCAAqC;4BACrC,MAAM,sBAAsB,aAAa;4BACzC,MAAM,mBAAmB,aAAa;4BAEtC,gDAAgD;4BAChD,mDAAmD;4BACnD,IAAI,oBAAoB;4BAExB,gDAAgD;4BAChD,IAAI,QAAQ,IAAI,KAAK,0BAA0B,qBAAqB;gCAClE,oBAAoB;4BACtB,OAAO,IAAI,QAAQ,IAAI,KAAK,cAAc,oBAAoB,CAAC,qBAAqB;gCAClF,oBAAoB;4BACtB,OAAO;gCACL,oBAAoB,QAClB,QAAQ,IAAI,KAAK,gBAChB,YAAY,QAAQ,IAAI,IAAI,SAAS,UAAU,CAAC,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC;4BAE/E;4BAEA,MAAM,uBAAuB,qBAAqB,QAAQ,KAAK;4BAC/D,2DAA2D;4BAC3D,MAAM,qBAAqB;4BAE3B,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,QAAQ,IAAI,IAAI;gCACtB,QAAQ;gCACR,OAAO;oCAAE,gBAAgB;oCAAQ,OAAO;gCAAU;0CAElD,cAAA,8OAAC,0JAAA,CAAA,iBAAc;oCACb,SAAS;wCACP,gBAAgB,QAAQ,IAAI,IAAI;wCAEhC,4EAA4E;wCAC5E,uCAAmC;;wCAenC;oCACF;oCACA,WAAW,GAAG,oBAAoB,aAAa,GAAG,CAAC,EAAE,uCAAqB,uDAAqB;;sDAE/F,8OAAC,0JAAA,CAAA,qBAAkB;sDAChB,2FAUC,8OAAC,4IAAA,CAAA,OAAI;gDACH,MAAM,QAAQ,IAAI;gDAClB,MAAK;gDACL,QAAQ;gDACR,OAAO,aAAa,QAAQ,KAAK,EAAE,QAAQ,IAAI,EAAE;;;;;;;;;;;sDAIvD,8OAAC,0JAAA,CAAA,qBAAkB;4CAAC,SAAS,QAAQ,KAAK;;;;;;;;;;;;+BAhDvC,QAAQ,KAAK;;;;;wBAoDxB;;;;;;;;;;;;;;;;;QAOV,oCAAoC;QACpC,OAAO,CAAC,uBACN,8OAAC,6LAAA,CAAA,UAAO;YACN,OAAO,KAAK,KAAK;YACjB,WAAU;YACV,KAAK;YACL,sBAAsB,CAAC,UAAU,QAAQ,KAAK,OAAO;sBAErD,cAAA,8OAAC;0BAAK;;;;;;;;;;mBAEN;IACN;IAEA,uDAAuD;IACvD,MAAM,mBAAmB;QACvB,MAAM,SAAS;YACb;gBAAE,OAAO;gBAAQ,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ;wBAAC;wBAAa;qBAAU,CAAC,QAAQ,CAAC,KAAK,KAAK;YAAG;YAChG;gBAAE,OAAO;gBAAc,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ;wBAAC;wBAAe;wBAAY;wBAAY;wBAAU;wBAAY;wBAAY;qBAAU,CAAC,QAAQ,CAAC,KAAK,KAAK;YAAG;YAClK;gBAAE,OAAO;gBAAY,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ;wBAAC;wBAAW;wBAAc;qBAAW,CAAC,QAAQ,CAAC,KAAK,KAAK;YAAG;YACjH;gBAAE,OAAO;gBAAU,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ;wBAAC;qBAAO,CAAC,QAAQ,CAAC,KAAK,KAAK;YAAG;SACnF;QAED,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,KAAK,CAAC,MAAM,GAAG;IACrD;IAEA,qBACE,8OAAC,0JAAA,CAAA,mBAAgB;QAAC,WAAW,CAAC,kBAAkB,EAAE,SAAS,KAAK,aAAa;;0BAG3E,8OAAC,0JAAA,CAAA,iBAAc;0BAEZ,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,8OAAC,0JAAA,CAAA,iBAAc;;4BACZ,QAAQ,mBAAK,8OAAC,0JAAA,CAAA,iBAAc;;;;;0CAC7B,8OAAC,oLAAA,CAAA,OAAI;gCAAC,WAAU;0CACb,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,qBAChB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;kDACZ,eAAe;uCADG,KAAK,KAAK;;;;;;;;;;;uBAJhB,MAAM,KAAK;;;;;;;;;;0BAcpC,8OAAC,0JAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC;8BAAK;;;;;;;;;;;;;;;;;AAId", "debugId": null}}, {"offset": {"line": 2409, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/footer/footer.style.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { styled } from \"@mui/material\";\r\n\r\n// Use a more specific class name to avoid hydration mismatches\r\nexport const FooterContainer = styled(\"footer\")(({ theme }) => ({\r\n  position: \"fixed\",\r\n  bottom: 0,\r\n  right: 0,\r\n  left: 0,\r\n  height: \"40px\",\r\n  color: theme.palette.text.primary,\r\n  backgroundColor: theme.palette.background.paper,\r\n  borderTop: `1px solid ${theme.palette.divider}`,\r\n  zIndex: theme.zIndex.appBar,\r\n  width: \"100%\",\r\n  display: \"flex\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  transition: \"all 0.2s ease-in-out\",\r\n}));\r\n\r\nexport const FooterContent = styled(\"div\")({\r\n  margin: 0,\r\n  padding: 0,\r\n  textAlign: \"center\",\r\n});\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;AAKO,MAAM,kBAAkB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QAC9D,UAAU;QACV,QAAQ;QACR,OAAO;QACP,MAAM;QACN,QAAQ;QACR,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO;QACjC,iBAAiB,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK;QAC/C,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;QAC/C,QAAQ,MAAM,MAAM,CAAC,MAAM;QAC3B,OAAO;QACP,SAAS;QACT,YAAY;QACZ,gBAAgB;QAChB,YAAY;IACd,CAAC;AAEM,MAAM,gBAAgB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,OAAO;IACzC,QAAQ;IACR,SAAS;IACT,WAAW;AACb", "debugId": null}}, {"offset": {"line": 2443, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/footer/footer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { FooterContainer, FooterContent } from \"./footer.style\";\r\n\r\nexport const Footer = () => {\r\n  return (\r\n    <FooterContainer>\r\n      <FooterContent>\r\n        © {new Date().getFullYear()} Ekvayu Tech Private Limited\r\n      </FooterContent>\r\n    </FooterContainer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKO,MAAM,SAAS;IACpB,qBACE,8OAAC,wJAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,wJAAA,CAAA,gBAAa;;gBAAC;gBACV,IAAI,OAAO,WAAW;gBAAG;;;;;;;;;;;;AAIpC;uCAEe", "debugId": null}}, {"offset": {"line": 2478, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/loading/loading.style.tsx"], "sourcesContent": ["import { styled } from '@mui/material/styles';\nimport { Box } from '@mui/material';\n\nexport const LoadingWrapper = styled(Box)(({ theme }) => ({\n  display: 'flex',\n  flexDirection: 'column',\n  alignItems: 'center',\n  justifyContent: 'center',\n  height: '100%',\n  width: '100%',\n  position: 'relative',\n\n  '& .lottieBox': {\n    width: '120px',\n    height: '120px',\n    position: 'absolute',\n    top: 'calc(50% - 100px)',\n    left: '50%',\n    transform: 'translateX(-50%)',\n  },\n\n  '& .MuiTypography-h4': {\n    color: theme.palette.mode === 'light' ? '#3A52A6' : '#7582EB',\n    fontWeight: 500,\n    position: 'absolute',\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    margin: 0,\n    width: 'auto',\n    textAlign: 'center',\n  },\n\n  '& .spinner': {\n    width: '50px',\n    height: '50px',\n    border: `4px solid ${theme.palette.mode === 'light' ? '#f3f3f3' : '#333'}`,\n    borderTop: `4px solid ${theme.palette.mode === 'light' ? '#3A52A6' : '#7582EB'}`,\n    borderRadius: '50%',\n    animation: 'spin 1s linear infinite',\n    margin: '20px 0',\n  },\n\n  '@keyframes spin': {\n    '0%': {\n      transform: 'rotate(0deg)',\n    },\n    '100%': {\n      transform: 'rotate(360deg)',\n    },\n  },\n\n  '& .progress-bar': {\n    width: '120px',\n    height: '3px',\n    backgroundColor: theme.palette.mode === 'light' ? '#f3f3f3' : '#333',\n    borderRadius: '2px',\n    overflow: 'hidden',\n    position: 'absolute',\n    left: '50%',\n    transform: 'translateX(-50%)',\n    margin: 0,\n\n    '&::after': {\n      content: '\"\"',\n      position: 'absolute',\n      top: 0,\n      left: 0,\n      height: '100%',\n      width: '30%',\n      backgroundColor: theme.palette.mode === 'light' ? '#3A52A6' : '#7582EB',\n      animation: 'progress 1.5s ease-in-out infinite',\n    },\n  },\n\n  '@keyframes progress': {\n    '0%': {\n      left: '-30%',\n    },\n    '100%': {\n      left: '100%',\n    },\n  },\n}));\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,iBAAiB,CAAA,GAAA,0MAAA,CAAA,SAAM,AAAD,EAAE,iLAAA,CAAA,MAAG,EAAE,CAAC,EAAE,KAAK,EAAE,GAAK,CAAC;QACxD,SAAS;QACT,eAAe;QACf,YAAY;QACZ,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,UAAU;QAEV,gBAAgB;YACd,OAAO;YACP,QAAQ;YACR,UAAU;YACV,KAAK;YACL,MAAM;YACN,WAAW;QACb;QAEA,uBAAuB;YACrB,OAAO,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,YAAY;YACpD,YAAY;YACZ,UAAU;YACV,KAAK;YACL,MAAM;YACN,WAAW;YACX,QAAQ;YACR,OAAO;YACP,WAAW;QACb;QAEA,cAAc;YACZ,OAAO;YACP,QAAQ;YACR,QAAQ,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,YAAY,QAAQ;YAC1E,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,YAAY,WAAW;YAChF,cAAc;YACd,WAAW;YACX,QAAQ;QACV;QAEA,mBAAmB;YACjB,MAAM;gBACJ,WAAW;YACb;YACA,QAAQ;gBACN,WAAW;YACb;QACF;QAEA,mBAAmB;YACjB,OAAO;YACP,QAAQ;YACR,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,YAAY;YAC9D,cAAc;YACd,UAAU;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,QAAQ;YAER,YAAY;gBACV,SAAS;gBACT,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,OAAO;gBACP,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,UAAU,YAAY;gBAC9D,WAAW;YACb;QACF;QAEA,uBAAuB;YACrB,MAAM;gBACJ,MAAM;YACR;YACA,QAAQ;gBACN,MAAM;YACR;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 2575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/loading/loading.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { ReactElement, useEffect, useState } from \"react\";\nimport dynamic from \"next/dynamic\";\nimport { Backdrop, Typography, useTheme } from \"@mui/material\";\n\nimport { LoadingWrapper } from \"./loading.style\";\n\n// Dynamically import Lottie with no SSR\nconst Lottie = dynamic(() => import(\"lottie-react\"), { ssr: false });\n\n// Import loading animation data\nimport loadingAnimationLight from \"./loadingAnimation.json\";\nimport loadingAnimationDark from \"./loadingAnimationDark.json\";\n\ninterface ILoadingProps {\n  isLoading: boolean;\n  message?: string;\n  fullScreen?: boolean;\n}\n\n/**\n * Loading Component\n * @param {ILoadingProps} props\n * @return {ReactElement}\n */\nexport function Loading({\n  isLoading,\n  message = \"Loading\",\n  fullScreen = true,\n}: ILoadingProps): ReactElement {\n  // Use state to track client-side rendering\n  const [isMounted, setIsMounted] = useState(false);\n  const theme = useTheme();\n  const isDarkMode = theme.palette.mode === 'dark';\n\n  // Only render on client-side\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  // Don't render anything during server-side rendering or if not loading\n  if (!isMounted || !isLoading) {\n    // Return an empty fragment that won't cause hydration issues\n    return <></>;\n  }\n\n  // Only create content when mounted on client-side\n  const content = (\n    <LoadingWrapper>\n      <Lottie\n        animationData={isDarkMode ? loadingAnimationDark : loadingAnimationLight}\n        loop\n        autoplay\n        className=\"lottieBox\"\n      />\n      <Typography variant=\"h4\">{message}</Typography>\n      <div className=\"progress-bar\" style={{ position: 'absolute', top: 'calc(50% + 30px)' }} />\n    </LoadingWrapper>\n  );\n\n  if (fullScreen) {\n    return (\n      <Backdrop\n        open={isLoading}\n        sx={(theme) => ({\n          backgroundColor: theme.palette.mode === 'dark'\n            ? 'rgba(0, 0, 0, 0.85)'\n            : 'rgba(255, 255, 255, 0.9)',\n          zIndex: 9999,\n          backdropFilter: 'blur(5px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        })}\n      >\n        {content}\n      </Backdrop>\n    );\n  }\n\n  return content;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AAKA,gCAAgC;AAChC;AACA;;AAbA;;;;;;AAQA,wCAAwC;AACxC,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAAkC,KAAK;;;;AAiBrD,SAAS,QAAQ,EACtB,SAAS,EACT,UAAU,SAAS,EACnB,aAAa,IAAI,EACH;IACd,2CAA2C;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,QAAQ,CAAA,GAAA,8LAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,aAAa,MAAM,OAAO,CAAC,IAAI,KAAK;IAE1C,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,uEAAuE;IACvE,IAAI,CAAC,aAAa,CAAC,WAAW;QAC5B,6DAA6D;QAC7D,qBAAO;IACT;IAEA,kDAAkD;IAClD,MAAM,wBACJ,8OAAC,2JAAA,CAAA,iBAAc;;0BACb,8OAAC;gBACC,eAAe,aAAa,sIAAA,CAAA,UAAoB,GAAG,kIAAA,CAAA,UAAqB;gBACxE,IAAI;gBACJ,QAAQ;gBACR,WAAU;;;;;;0BAEZ,8OAAC,sMAAA,CAAA,aAAU;gBAAC,SAAQ;0BAAM;;;;;;0BAC1B,8OAAC;gBAAI,WAAU;gBAAe,OAAO;oBAAE,UAAU;oBAAY,KAAK;gBAAmB;;;;;;;;;;;;IAIzF,IAAI,YAAY;QACd,qBACE,8OAAC,gMAAA,CAAA,WAAQ;YACP,MAAM;YACN,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,SACpC,wBACA;oBACJ,QAAQ;oBACR,gBAAgB;oBAChB,SAAS;oBACT,YAAY;oBACZ,gBAAgB;gBAClB,CAAC;sBAEA;;;;;;IAGP;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/components/common/loading/index.ts"], "sourcesContent": ["export * from './loading';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2702, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/dss-v2/dss-v2/src/app/%28main%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { usePathname, useSearchParams } from \"next/navigation\";\r\n\r\nimport { LayoutContainer, MainContent } from \"./layout.style\"\r\nimport { Header } from \"@/components/common/header/header\";\r\nimport { Sidebar } from \"@/components/common/sidebar/sidebar\";\r\nimport { Footer } from \"@/components/common/footer/footer\";\r\nimport { Loading } from \"@/components/common/loading\";\r\n\r\ninterface ExecutorLayoutProps {\r\n  children: React.ReactNode;\r\n  title?: string;\r\n}\r\n\r\nexport default function ExecutorLayout({\r\n  children,\r\n  title,\r\n}: ExecutorLayoutProps) {\r\n  const [sidebarOpen, setSidebarOpen] = useState(true);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isMounted, setIsMounted] = useState(false);\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n\r\n  // Create a stable reference to the toggle function that doesn't depend on state\r\n  // This ensures it never changes and never causes re-renders\r\n  const toggleSidebarRef = React.useRef(() => {\r\n    // Use direct DOM manipulation for maximum performance\r\n    const sidebar = document.querySelector('.sidebar-container');\r\n    const mainContent = document.querySelector('.main-content');\r\n\r\n    if (sidebar && mainContent) {\r\n      // Add transition class to enable smooth animations\r\n      sidebar.classList.add('with-transition');\r\n      mainContent.classList.add('with-transition');\r\n\r\n      if (sidebar.classList.contains('icon-only')) {\r\n        sidebar.classList.remove('icon-only');\r\n        mainContent.classList.remove('sidebar-icon-only');\r\n        // Update state without causing re-renders in the toggle function\r\n        setTimeout(() => setSidebarOpen(true), 0);\r\n      } else {\r\n        sidebar.classList.add('icon-only');\r\n        mainContent.classList.add('sidebar-icon-only');\r\n        // Update state without causing re-renders in the toggle function\r\n        setTimeout(() => setSidebarOpen(false), 0);\r\n      }\r\n\r\n      // Remove transition class after animation completes to prevent lag during scrolling\r\n      setTimeout(() => {\r\n        sidebar.classList.remove('with-transition');\r\n        mainContent.classList.remove('with-transition');\r\n      }, 500); // Slightly longer than the transition duration (0.45s)\r\n    }\r\n  });\r\n\r\n  // Expose a stable function that never changes\r\n  const toggleSidebar = React.useCallback(() => {\r\n    toggleSidebarRef.current();\r\n  }, []);\r\n\r\n  // First, mark when component is mounted on client\r\n  useEffect(() => {\r\n    setIsMounted(true);\r\n  }, []);\r\n\r\n  // Show loading indicator when route changes\r\n  useEffect(() => {\r\n    // Only run on client side\r\n    if (!isMounted) return;\r\n\r\n    // Set a timeout to simulate loading and then hide it\r\n    if (isLoading) {\r\n      const timer = setTimeout(() => {\r\n        setIsLoading(false);\r\n      }, 800); // Adjust timing as needed\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [isLoading, isMounted]);\r\n\r\n  // Detect route changes - only run on client side\r\n  useEffect(() => {\r\n    // Only run on client side\r\n    if (!isMounted) return;\r\n\r\n    setIsLoading(true);\r\n    // Loading state will be cleared by the timer in the other effect\r\n  }, [pathname, searchParams, isMounted]);\r\n\r\n  return (\r\n    <LayoutContainer suppressHydrationWarning>\r\n      <Header title={title} onToggleSidebar={toggleSidebar} />\r\n      <Sidebar isOpen={sidebarOpen} />\r\n      <MainContent\r\n        className={`main-content ${sidebarOpen ? '' : 'sidebar-icon-only'}`}\r\n        suppressHydrationWarning\r\n      >\r\n        <div className=\"content-wrapper\">\r\n          {children}\r\n        </div>\r\n      </MainContent>\r\n      <Footer />\r\n      {isMounted && <Loading isLoading={isLoading} message=\"Loading\" />}\r\n    </LayoutContainer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AATA;;;;;;;;;AAgBe,SAAS,eAAe,EACrC,QAAQ,EACR,KAAK,EACe;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,gFAAgF;IAChF,4DAA4D;IAC5D,MAAM,mBAAmB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;QACpC,sDAAsD;QACtD,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,MAAM,cAAc,SAAS,aAAa,CAAC;QAE3C,IAAI,WAAW,aAAa;YAC1B,mDAAmD;YACnD,QAAQ,SAAS,CAAC,GAAG,CAAC;YACtB,YAAY,SAAS,CAAC,GAAG,CAAC;YAE1B,IAAI,QAAQ,SAAS,CAAC,QAAQ,CAAC,cAAc;gBAC3C,QAAQ,SAAS,CAAC,MAAM,CAAC;gBACzB,YAAY,SAAS,CAAC,MAAM,CAAC;gBAC7B,iEAAiE;gBACjE,WAAW,IAAM,eAAe,OAAO;YACzC,OAAO;gBACL,QAAQ,SAAS,CAAC,GAAG,CAAC;gBACtB,YAAY,SAAS,CAAC,GAAG,CAAC;gBAC1B,iEAAiE;gBACjE,WAAW,IAAM,eAAe,QAAQ;YAC1C;YAEA,oFAAoF;YACpF,WAAW;gBACT,QAAQ,SAAS,CAAC,MAAM,CAAC;gBACzB,YAAY,SAAS,CAAC,MAAM,CAAC;YAC/B,GAAG,MAAM,uDAAuD;QAClE;IACF;IAEA,8CAA8C;IAC9C,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QACtC,iBAAiB,OAAO;IAC1B,GAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,IAAI,CAAC,WAAW;QAEhB,qDAAqD;QACrD,IAAI,WAAW;YACb,MAAM,QAAQ,WAAW;gBACvB,aAAa;YACf,GAAG,MAAM,0BAA0B;YAEnC,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAW;KAAU;IAEzB,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,IAAI,CAAC,WAAW;QAEhB,aAAa;IACb,iEAAiE;IACnE,GAAG;QAAC;QAAU;QAAc;KAAU;IAEtC,qBACE,8OAAC,yIAAA,CAAA,kBAAe;QAAC,wBAAwB;;0BACvC,8OAAC,gJAAA,CAAA,SAAM;gBAAC,OAAO;gBAAO,iBAAiB;;;;;;0BACvC,8OAAC,kJAAA,CAAA,UAAO;gBAAC,QAAQ;;;;;;0BACjB,8OAAC,yIAAA,CAAA,cAAW;gBACV,WAAW,CAAC,aAAa,EAAE,cAAc,KAAK,qBAAqB;gBACnE,wBAAwB;0BAExB,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAGL,8OAAC,gJAAA,CAAA,SAAM;;;;;YACN,2BAAa,8OAAC,kJAAA,CAAA,UAAO;gBAAC,WAAW;gBAAW,SAAQ;;;;;;;;;;;;AAG3D", "debugId": null}}]}