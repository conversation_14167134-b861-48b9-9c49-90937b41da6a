import React from 'react';
import { Box, Button, Tooltip } from '@mui/material';

interface ActionButtonsProps {
  value: string; // This comes from the table
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  [key: string]: any; // Allow other props from table
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({ value, onEdit, onDelete, ...rest }) => {
  const handleEdit = () => {
    if (onEdit) {
      onEdit(value);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(value);
    }
  };

  return (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <Tooltip title="Edit">
        <Button
          onClick={handleEdit}
          size="small"
          variant="contained"
          sx={{
            bgcolor: '#3A52A6',
            '&:hover': {
              bgcolor: '#2A3F8F',
            },
            fontSize: '12px',
            padding: '4px 12px',
          }}
        >
          Edit
        </Button>
      </Tooltip>
      <Tooltip title="Delete">
        <Button
          onClick={handleDelete}
          size="small"
          variant="contained"
          color="error"
          sx={{
            fontSize: '12px',
            padding: '4px 12px',
          }}
        >
          Delete
        </Button>
      </Tooltip>
    </Box>
  );
};

export default ActionButtons;
