import React, { useState, KeyboardEvent, ChangeEvent, ReactNode, useEffect } from "react";
import {
  Popover,
  FormControl,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Box,
  Button,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import ViewColumnIcon from "@mui/icons-material/ViewColumn";
import {
  SearchContainer,
  SearchPaper,
  SearchInputBase,
  SearchIconButton,
  FilterIconButton,
  FilterPopoverBox,
} from "@/components/common/searchBar/searchBar.style";
import { ColumnVisibilityMenu } from "@/components/common";

interface ErrorLogsSearchBarProps {
  onSearch: (searchTerm: string, filters: string[]) => void;
  placeholder?: string;
  actionButton?: ReactNode;
  onColumnVisibilityChange?: (visibleColumns: string[]) => void;
  initialVisibleColumns?: string[];
}

export const SearchBar: React.FC<ErrorLogsSearchBarProps> = ({
  onSearch,
  placeholder = "Search...",
  actionButton,
  onColumnVisibilityChange,
  initialVisibleColumns = ['sn', 'user', 'path', 'method', 'errorType', 'errorMessage', 'timestamp', 'traceback'],
}) => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterAnchorEl, setFilterAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [columnsAnchorEl, setColumnsAnchorEl] = useState<HTMLButtonElement | null>(null);

  const [selectedFilters, setSelectedFilters] = useState<string[]>([
    'user', 'path', 'method', 'errorType', 'errorMessage', 'timestamp'
  ]);

  const [visibleColumns, setVisibleColumns] = useState<string[]>(initialVisibleColumns);

  // We don't need to call onColumnVisibilityChange on mount
  // This will be called only when the user toggles a column

  const filterOptions = [
    { value: 'user', label: 'User' },
    { value: 'path', label: 'Path' },
    { value: 'method', label: 'Method' },
    { value: 'errorType', label: 'Error Type' },
    { value: 'errorMessage', label: 'Error Message' },
    { value: 'timestamp', label: 'Timestamp' },
  ];

  const columnOptions = [
    { value: 'sn', label: 'SN' },
    { value: 'user', label: 'User' },
    { value: 'path', label: 'Path' },
    { value: 'method', label: 'Method' },
    { value: 'errorType', label: 'Error Type' },
    { value: 'errorMessage', label: 'Error Message' },
    { value: 'timestamp', label: 'Timestamp' },
    { value: 'traceback', label: 'Traceback' },
  ];

  const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleColumnsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setColumnsAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleColumnsClose = () => {
    setColumnsAnchorEl(null);
  };

  const handleFilterToggle = (value: string) => {
    setSelectedFilters((prev) => {
      if (prev.includes(value)) {
        return prev.filter((item) => item !== value);
      } else {
        return [...prev, value];
      }
    });
  };

  const handleColumnToggle = (value: string) => {
    setVisibleColumns((prev) => {
      const newVisibleColumns = prev.includes(value)
        ? prev.filter((item) => item !== value)
        : [...prev, value];

      // Call the callback if provided
      if (onColumnVisibilityChange) {
        onColumnVisibilityChange(newVisibleColumns);
      }

      return newVisibleColumns;
    });
  };

  const handleSearchClick = () => {
    onSearch(searchTerm, selectedFilters);
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearchClick();
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);

    // If the input is cleared (empty), trigger search to show all data
    if (newValue === "") {
      onSearch("", selectedFilters);
    }
  };

  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
      <SearchContainer>
        <SearchPaper
          onSubmit={(e: React.FormEvent) => {
            e.preventDefault();
          }}
        >
          <SearchInputBase
            placeholder={placeholder}
            value={searchTerm}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
          />
          <FilterIconButton onClick={handleFilterClick} size="small">
            <FilterListIcon />
          </FilterIconButton>
          <SearchIconButton onClick={handleSearchClick} size="small">
            <SearchIcon />
          </SearchIconButton>
        </SearchPaper>

        <Popover
          open={Boolean(filterAnchorEl)}
          anchorEl={filterAnchorEl}
          onClose={handleFilterClose}
          anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
          }}
          transformOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          <FilterPopoverBox>
            <FormControl component="fieldset">
              <FormGroup>
                {filterOptions.map((option) => (
                  <FormControlLabel
                    key={option.value}
                    control={
                      <Checkbox
                        checked={selectedFilters.includes(option.value)}
                        onChange={() => handleFilterToggle(option.value)}
                        size="small"
                      />
                    }
                    label={option.label}
                  />
                ))}
              </FormGroup>
            </FormControl>
          </FilterPopoverBox>
        </Popover>
      </SearchContainer>

      <Button
        variant="outlined"
        startIcon={<ViewColumnIcon />}
        onClick={handleColumnsClick}
        size="small"
        sx={{
          textTransform: 'none',
          borderColor: '#e0e0e0',
          color: 'text.primary',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
            borderColor: '#d5d5d5'
          }
        }}
      >
        Columns
      </Button>

      <ColumnVisibilityMenu
        anchorEl={columnsAnchorEl}
        open={Boolean(columnsAnchorEl)}
        onClose={handleColumnsClose}
        columnOptions={columnOptions}
        visibleColumns={visibleColumns}
        onColumnToggle={handleColumnToggle}
        variant="popover"
      />

      {actionButton}
    </Box>
  );
};

export default SearchBar;
