"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay } from "@/components/common/breadcrumb";

// Mock data for the exception details
const mockExceptionData = {
  id: '1',
  user: 'Anonymous',
  path: '/media/cdr_files/cdr_19536bd19a90863f_dummy-pdf_2.pdf',
  method: 'GET',
  exceptionType: 'Http404',
  exceptionMessage: '"/app/media/cdr_files/cdr_19536bd19a90863f_dummy-pdf_2.pdf" does not exist',
  timestamp: '26-02-2025 01:25 PM',
  traceback: `Traceback (most recent call last):
  File "/usr/local/lib/python3.10/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/usr/local/lib/python3.10/site-packages/django/views/static.py", line 40, in serve
    raise Http404(_("%(path)s does not exist") % {"path": fullpath})
django.http.Http404: "/app/media/attachments/..." does not exist`,
};

interface IExceptionDetailsController {
  getters: {
    loading: boolean;
    exceptionData: any;
    breadcrumbs: IBreadcrumbDisplay[];
  };
  handlers: {};
}

/**
 * Exception Details Controller
 * @param {string} exceptionId - The ID of the exception to display
 * @return {IExceptionDetailsController} Controller with getters and handlers
 */
export const ExceptionDetailsController = (exceptionId: string): IExceptionDetailsController => {
  const [loading, setLoading] = useState(true);
  const [exceptionData, setExceptionData] = useState<any>(null);

  // In a real application, you would fetch the exception data based on the exceptionId
  useEffect(() => {
    // Simulate API call
    const fetchExceptionData = async () => {
      setLoading(true);
      try {
        // In a real app, you would fetch data from an API
        // const response = await fetch(`/api/exceptions/${exceptionId}`);
        // const data = await response.json();

        // For now, we'll use mock data
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update the mock data with the provided ID
        const data = { ...mockExceptionData, id: exceptionId };
        setExceptionData(data);
      } catch (error) {
        console.error("Error fetching exception data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchExceptionData();
  }, [exceptionId]);

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Logs",
      path: "", // No direct path for Logs category
      forwardParam: false,
    },
    {
      name: "Exception Logs",
      path: RoutePathEnum.EXCEPTION_LOGS,
      forwardParam: false,
      clickable: true,
    },
    {
      name: `Exception Details #${exceptionId}`,
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  return {
    getters: {
      loading,
      exceptionData,
      breadcrumbs,
    },
    handlers: {},
  };
};

export default ExceptionDetailsController;
