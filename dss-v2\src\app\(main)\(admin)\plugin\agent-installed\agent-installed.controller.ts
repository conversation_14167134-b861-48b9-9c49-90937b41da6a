"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import useMeasure from "react-use-measure";
import { Theme, useMediaQuery, useTheme } from "@mui/material";

import {
  IBreadcrumbDisplay,
  IHeader,
  TableComponentEnum,
} from "@/components/common";
import { RoutePathEnum, SnackbarTypeEnum } from "@/enum";
import { MeasureRefType } from "@/interfaces";
import {
  GET_TABLE_PAGINATION_DATA,
  tableActions,
  useAppDispatch,
  useAppSelector,
} from "@/redux";
import { useAppSnackbar } from "@/hooks/snackbar.hook";

import { AvatarMenu } from "@/components/common";
import ViewReportButton from "./components/viewReportButton";
// We no longer need to import SystemDetailsModal

export const AgentInstalledController = () => {
  // We no longer need the system details modal state as we're using a separate page
  // Static data for the table
  const staticContactInfo = [
    {
      sn: 1,
      licenseId: "LIC-00003",
      allocatedTo: "<EMAIL>",
      macAddress: "e4:7d:7f:13:c9:a1",
      allocationDate: "12-05-2023 06:31 PM",
      osType: "Windows_NT",
      osPlatform: "win32",
      chrome: "136.0.7103.93",
      firefox: "138.0.1 (64-en-US)",
      edge: "136.0.3240.64",
      lastChecked: "13-05-2023 12:07 PM",
      report: "View",
    },
    {
      sn: 2,
      licenseId: "LIC-2023-002",
      allocatedTo: "Sarah Johnson",
      macAddress: "00:2B:3C:4D:5E:6F",
      allocationDate: "05/05/2023",
      osType: "MacOS",
      osPlatform: "MacOS Monterey",
      chrome: "Yes",
      firefox: "Yes",
      edge: "No",
      lastChecked: "03/08/2023",
      report: "View",
    },
    {
      sn: 3,
      licenseId: "LIC-2023-003",
      allocatedTo: "Michael Brown",
      macAddress: "00:3C:4D:5E:6F:7G",
      allocationDate: "06/05/2023",
      osType: "Linux",
      osPlatform: "Ubuntu 22.04",
      chrome: "Yes",
      firefox: "Yes",
      edge: "No",
      lastChecked: "04/08/2023",
      report: "View",
    },
    {
      sn: 4,
      licenseId: "LIC-2023-004",
      allocatedTo: "Emily Davis",
      macAddress: "00:4D:5E:6F:7G:8H",
      allocationDate: "07/05/2023",
      osType: "Windows",
      osPlatform: "Windows 11",
      chrome: "Yes",
      firefox: "No",
      edge: "Yes",
      lastChecked: "05/08/2023",
      report: "View",
    },
    {
      sn: 5,
      licenseId: "LIC-2023-005",
      allocatedTo: "David Wilson",
      macAddress: "00:5E:6F:7G:8H:9I",
      allocationDate: "08/05/2023",
      osType: "MacOS",
      osPlatform: "MacOS Ventura",
      chrome: "Yes",
      firefox: "Yes",
      edge: "No",
      lastChecked: "06/08/2023",
      report: "View",
    },
    {
      sn: 6,
      licenseId: "LIC-2023-006",
      allocatedTo: "Jennifer Taylor",
      macAddress: "00:6F:7G:8H:9I:0J",
      allocationDate: "09/05/2023",
      osType: "Windows",
      osPlatform: "Windows 10",
      chrome: "No",
      firefox: "Yes",
      edge: "Yes",
      lastChecked: "07/08/2023",
      report: "View",
    },
    {
      sn: 7,
      licenseId: "LIC-2023-007",
      allocatedTo: "Robert Anderson",
      macAddress: "00:7G:8H:9I:0J:1K",
      allocationDate: "10/05/2023",
      osType: "Linux",
      osPlatform: "Fedora 38",
      chrome: "Yes",
      firefox: "Yes",
      edge: "No",
      lastChecked: "08/08/2023",
      report: "View",
    },
    {
      sn: 8,
      licenseId: "LIC-2023-008",
      allocatedTo: "Lisa Martinez",
      macAddress: "00:8H:9I:0J:1K:2L",
      allocationDate: "11/05/2023",
      osType: "Windows",
      osPlatform: "Windows 11",
      chrome: "Yes",
      firefox: "No",
      edge: "Yes",
      lastChecked: "09/08/2023",
      report: "View",
    },
    {
      sn: 9,
      licenseId: "LIC-2023-009",
      allocatedTo: "James Thompson",
      macAddress: "00:9I:0J:1K:2L:3M",
      allocationDate: "12/05/2023",
      osType: "MacOS",
      osPlatform: "MacOS Monterey",
      chrome: "Yes",
      firefox: "Yes",
      edge: "No",
      lastChecked: "10/08/2023",
      report: "View",
    },
    {
      sn: 10,
      licenseId: "LIC-2023-010",
      allocatedTo: "Patricia Garcia",
      macAddress: "00:0J:1K:2L:3M:4N",
      allocationDate: "13/05/2023",
      osType: "Windows",
      osPlatform: "Windows 10",
      chrome: "Yes",
      firefox: "Yes",
      edge: "Yes",
      lastChecked: "11/08/2023",
      report: "View",
    },
    {
      sn: 11,
      licenseId: "LIC-2023-011",
      allocatedTo: "Thomas Rodriguez",
      macAddress: "00:1K:2L:3M:4N:5O",
      allocationDate: "14/05/2023",
      osType: "Linux",
      osPlatform: "Debian 11",
      chrome: "Yes",
      firefox: "Yes",
      edge: "No",
      lastChecked: "12/08/2023",
      report: "View",
    },
    {
      sn: 12,
      licenseId: "LIC-2023-012",
      allocatedTo: "Nancy Lee",
      macAddress: "00:2L:3M:4N:5O:6P",
      allocationDate: "15/05/2023",
      osType: "Windows",
      osPlatform: "Windows 11",
      chrome: "Yes",
      firefox: "No",
      edge: "Yes",
      lastChecked: "13/08/2023",
      report: "View",
    },
  ];

  const FILTER_OPTIONS = [
    { label: "License ID", value: "licenseId" },
    { label: "Allocated To", value: "allocatedTo" },
    { label: "MAC Address", value: "macAddress" },
    { label: "OS Type", value: "osType" },
    { label: "OS Platform", value: "osPlatform" },
  ];

  const [ref, { height }] = useMeasure();
  const theme: Theme = useTheme();
  const isMobileView: boolean = useMediaQuery(theme.breakpoints.down("sm"));
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<string[]>([
    "licenseId",
    "allocatedTo",
    "macAddress",
    "osType",
    "osPlatform",
  ]);

  // Column options for the ActiveColumn component
  const COLUMN_OPTIONS = [
    { label: "SN", value: "sn" },
    { label: "LICENSE ID", value: "licenseId" },
    { label: "ALLOCATED TO", value: "allocatedTo" },
    { label: "MAC ADDRESS", value: "macAddress" },
    { label: "ALLOCATION DATE", value: "allocationDate" },
    { label: "OS TYPE", value: "osType" },
    { label: "OS PLATFORM", value: "osPlatform" },
    { label: "CHROME", value: "chrome" },
    { label: "FIREFOX", value: "firefox" },
    { label: "EDGE", value: "edge" },
    { label: "LAST CHECKED", value: "lastChecked" },
    { label: "REPORT", value: "report" },
  ];

  // Initially visible columns
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "sn", "licenseId", "allocatedTo", "macAddress", "allocationDate",
    "osType", "osPlatform", "chrome", "firefox", "edge", "lastChecked", "report"
  ]);

  /**
   * @funtions {changeRows} - Update Rows to Show
   * @param {number} newRows
   */
  const changeRows = useCallback(
    (newRows: number): void => {
      dispatch(tableActions.setTableRows(newRows));
    },
    [dispatch]
  );

  /**
   * @functions {changePage} - Update Active Page
   * @param {number} newPage
   */
  const changePage = useCallback(
    (newPage: number): void => {
      dispatch(tableActions.setTablePage(newPage));
    },
    [dispatch]
  );

  const filteredData = useMemo(
    () =>
      staticContactInfo.filter((item) => {
        if (!searchTerm) return true;

        const searchLower = searchTerm.toLowerCase();
        return activeFilters.some((filter) => {
          const value = item[filter as keyof typeof item];
          return value?.toString().toLowerCase().includes(searchLower);
        });
      }),
    [searchTerm, activeFilters]
  );

  const newApplication = useMemo(
    () =>
      filteredData.map((item) => ({
        sn: item.sn,
        licenseId: item.licenseId,
        allocatedTo: item.allocatedTo,
        macAddress: item.macAddress,
        allocationDate: item.allocationDate,
        osType: item.osType,
        osPlatform: item.osPlatform,
        chrome: item.chrome,
        firefox: item.firefox,
        edge: item.edge,
        lastChecked: item.lastChecked,
        report: item.licenseId, // Pass licenseId as the value for the report button
      })),
    [filteredData]
  );

  const tablePaginationData = useAppSelector(GET_TABLE_PAGINATION_DATA);
  // Static pagination data
  const { page, limit, isLoading } = tablePaginationData;

  const contactPagination = {
    totalCount: filteredData.length,
  };

  // Table headers
  const headers: IHeader[] = [
    {
      id: "sn",
      name: "SN",
      hidden: false,
      width: 60,
      type: TableComponentEnum.STRING,
    },
    {
      id: "licenseId",
      name: "LICENSE ID",
      hidden: false,
      width: 120,
      type: TableComponentEnum.STRING,
    },
    {
      id: "allocatedTo",
      name: "ALLOCATED TO",
      hidden: false,
      width: 250, // Increased width for email addresses
      type: TableComponentEnum.COMPONENT,
      component: AvatarMenu,
    },
    {
      id: "macAddress",
      name: "MAC ADDRESS",
      hidden: false,
      width: 160, // Increased width for MAC addresses
      type: TableComponentEnum.STRING,
    },
    {
      id: "allocationDate",
      name: "ALLOCATION DATE",
      hidden: false,
      width: 180, // Increased width for date with time
      type: TableComponentEnum.DATE,
    },
    {
      id: "osType",
      name: "OS TYPE",
      hidden: false,
      width: 120, // Increased width
      type: TableComponentEnum.STRING,
    },
    {
      id: "osPlatform",
      name: "OS PLATFORM",
      hidden: false,
      width: 120, // Adjusted width
      type: TableComponentEnum.STRING,
    },
    {
      id: "chrome",
      name: "CHROME",
      hidden: false,
      width: 160, // Increased width for version numbers
      type: TableComponentEnum.STRING,
    },
    {
      id: "firefox",
      name: "FIREFOX",
      hidden: false,
      width: 180, // Increased width for longer version strings
      type: TableComponentEnum.STRING,
    },
    {
      id: "edge",
      name: "EDGE",
      hidden: false,
      width: 160, // Increased width for version numbers
      type: TableComponentEnum.STRING,
    },
    {
      id: "lastChecked",
      name: "LAST CHECKED",
      hidden: false,
      width: 180, // Increased width for date with time
      type: TableComponentEnum.DATE,
    },
    {
      id: "report",
      name: "REPORT",
      hidden: false,
      width: 100,
      type: TableComponentEnum.COMPONENT,
      component: ViewReportButton,
    },
  ];

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Plugin",
      path: RoutePathEnum.PLUGIN,
      forwardParam: false,
    },
    {
      name: "Agent Installed",
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  const handleSearch = useCallback(
    (term: string, filters: string[]) => {
      setSearchTerm(term);
      setActiveFilters(filters);
      dispatch(tableActions.setTablePage(0));
    },
    [dispatch]
  );

  const { enqueueSnackbar } = useAppSnackbar();

  const handleViewReport = useCallback(
    (licenseId: string) => {
      // For licenses other than LIC-00003, show a snackbar notification
      if (licenseId !== "LIC-00003") {
        enqueueSnackbar(`No records found for license id: ${licenseId} in the last 7 days`, SnackbarTypeEnum.INFO);
      }
      // Navigation is now handled in the ViewReportButton component
    },
    [enqueueSnackbar]
  );

  /**
   * Handle column visibility changes
   * @param {string[]} newVisibleColumns - The new array of visible column IDs
   */
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  return {
    getters: {
      breadcrumbs,
      headers,
      contactPagination,
      tablePaginationData,
      isLoading: false,
      isOpenDrawer: false,
      filter: {} as any,
      newApplication,
      height,
      isMobileView,
      FILTER_OPTIONS,
      COLUMN_OPTIONS,
      visibleColumns,

      searchTerm,
    },
    handlers: {
      changePage,
      changeRows,
      handleSearch,
      handleViewReport,
      handleColumnVisibilityChange,
      getContactInformation: async () => () => {},
    },

    ref: ref as MeasureRefType,
  };
};
