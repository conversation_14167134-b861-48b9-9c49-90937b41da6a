"use client";

import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  InputAdornment,
  useTheme,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import EmailIcon from "@mui/icons-material/Email";
import VpnKeyIcon from "@mui/icons-material/VpnKey";
import LaptopIcon from "@mui/icons-material/Laptop";

interface RevokeLicenseModalProps {
  open: boolean;
  onClose: () => void;
  onRevoke: (licenseId: string) => void;
  licenseData: {
    licenseId: string;
    email: string;
    macAddress: string;
  };
}

const RevokeLicenseModal: React.FC<RevokeLicenseModalProps> = ({
  open,
  onClose,
  onRevoke,
  licenseData,
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === "dark";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onRevoke(licenseData.licenseId);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 1,
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        pb: 2
      }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          Revoke License
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 3 }}>
        <Typography variant="body1" sx={{ mb: 3 }}>
          Are you sure you want to revoke the following license?
        </Typography>
        
        <Box sx={{ mb: 3 }}>
          <TextField
            fullWidth
            label="License ID"
            value={licenseData.licenseId}
            InputProps={{
              readOnly: true,
              startAdornment: (
                <InputAdornment position="start">
                  <VpnKeyIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              mb: 3,
              "& .MuiInputBase-root": {
                backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.5)" : "rgba(255, 255, 255, 0.5)",
                backdropFilter: "blur(8px)",
              },
            }}
          />
          
          <TextField
            fullWidth
            label="Email Address"
            value={licenseData.email}
            InputProps={{
              readOnly: true,
              startAdornment: (
                <InputAdornment position="start">
                  <EmailIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              mb: 3,
              "& .MuiInputBase-root": {
                backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.5)" : "rgba(255, 255, 255, 0.5)",
                backdropFilter: "blur(8px)",
              },
            }}
          />
          
          <TextField
            fullWidth
            label="MAC Address"
            value={licenseData.macAddress}
            InputProps={{
              readOnly: true,
              startAdornment: (
                <InputAdornment position="start">
                  <LaptopIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              "& .MuiInputBase-root": {
                backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.5)" : "rgba(255, 255, 255, 0.5)",
                backdropFilter: "blur(8px)",
              },
            }}
            onKeyDown={handleKeyDown}
          />
        </Box>
        
        <Typography variant="body2" color="error" sx={{ mt: 2 }}>
          This action cannot be undone. The license will be revoked immediately.
        </Typography>
      </DialogContent>
      <DialogActions sx={{ 
        px: 3, 
        pb: 3,
        borderTop: '1px solid',
        borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
        pt: 2
      }}>
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit} 
          variant="contained" 
          color="error"
          sx={{ ml: 2 }}
        >
          Revoke License
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RevokeLicenseModal;
