"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import useMeasure from "react-use-measure";
import { Theme, useMediaQuery, useTheme } from "@mui/material";

import {
  IBreadcrumbDisplay,
  IHeader,
  TableComponentEnum,
} from "@/components/common";
import { RoutePathEnum } from "@/enum";
import { MeasureRefType } from "@/interfaces";
import {
  GET_TABLE_PAGINATION_DATA,
  tableActions,
  useAppDispatch,
  useAppSelector,
} from "@/redux";

import { ViewDetailsButton } from "./components/viewDetailsButton";

/**
 * Phishing Controller
 * @return {Object} Controller with getters and handlers
 */
export const PhishingController = () => {
  // Static data for the table
  const staticPhishingInfo = [
    {
      sn: 1,
      messageId: "f9b4c3e-da5c-1...",
      sendersEmail: "<EMAIL>...",
      receiversEmail: "<EMAIL>",
      subject: "Subject 278",
      createTime: "01-01-2025 12:00 AM",
    },
    {
      sn: 2,
      messageId: "f9b4c38b-da5c-1...",
      sendersEmail: "<EMAIL>...",
      receiversEmail: "<EMAIL>",
      subject: "Subject 412",
      createTime: "01-12-2025 12:00 AM",
    },
    {
      sn: 3,
      messageId: "f9b4c355-da5c-1...",
      sendersEmail: "<EMAIL>...",
      receiversEmail: "<EMAIL>",
      subject: "Subject 324",
      createTime: "01-11-2025 12:00 AM",
    },
    {
      sn: 4,
      messageId: "f9b4c5e8-da5c-1...",
      sendersEmail: "<EMAIL>...",
      receiversEmail: "<EMAIL>",
      subject: "Subject 105",
      createTime: "01-09-2025 12:00 AM",
    },
    {
      sn: 5,
      messageId: "TestingbyMehtab...",
      sendersEmail: "<EMAIL>",
      receiversEmail: "neerajgupta@ekvayus.c...",
      subject: "Checking multiple ur...",
      createTime: "28-04-2025 05:23 PM",
    },
    {
      sn: 6,
      messageId: "193e5872f710ad...",
      sendersEmail: "<EMAIL>",
      receiversEmail: "neerajgupta@ekvayus.c...",
      subject: "checkinggggggggggggg",
      createTime: "26-04-2025 05:45 PM",
    },
    {
      sn: 7,
      messageId: "1957bbe47c9e04...",
      sendersEmail: "<EMAIL>",
      receiversEmail: "neerajgupta@ekvayus.c...",
      subject: "+UTF-8?B*UnJRdEZvbG...",
      createTime: "26-04-2025 04:55 PM",
    },
    {
      sn: 8,
      messageId: "19566c55e71784...",
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "Re: Sample PPTX (Wit...",
      createTime: "26-04-2025 04:45 PM",
    },
    {
      sn: 9,
      messageId: "MSG1234r7787776...",
      sendersEmail: "<EMAIL>",
      receiversEmail: "tarun.kamble@ekvayus...",
      subject: "Advanced SQL techniq...",
      createTime: "25-04-2025 06:25 PM",
    },
    {
      sn: 10,
      messageId: "TestingbymeHtab...",
      sendersEmail: "<EMAIL>",
      receiversEmail: "neerajgupta@ekvayus.c...",
      subject: "Fwd: Release Note ...",
      createTime: "25-04-2025 05:54 PM",
    },
  ];

  const [ref, { height }] = useMeasure();
  const theme: Theme = useTheme();
  const isMobileView: boolean = useMediaQuery(theme.breakpoints.down("sm"));
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const FILTER_OPTIONS = [
    { label: "Message ID", value: "messageId", type: "text" },
    { label: "Sender's Email", value: "sendersEmail", type: "email" },
    { label: "Receiver's Email", value: "receiversEmail", type: "email" },
    { label: "Subject", value: "subject", type: "text" },
    { label: "Create Time", value: "createTime", type: "date" },
  ];

  const [activeFilters, setActiveFilters] = useState<string[]>([
    "messageId",
    "sendersEmail",
    "receiversEmail",
    "subject",
  ]);

  // Column options for the ActiveColumn component
  const COLUMN_OPTIONS = [
    { label: "SN", value: "sn" },
    { label: "Message ID", value: "messageId" },
    { label: "Sender's Email", value: "sendersEmail" },
    { label: "Receiver's Email", value: "receiversEmail" },
    { label: "Subject", value: "subject" },
    { label: "Create Time", value: "createTime" },
    { label: "Details", value: "details" },
  ];

  // Initially visible columns
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "sn", "messageId", "sendersEmail", "receiversEmail", "subject", "createTime", "details"
  ]);

  /**
   * @funtions {changeRows} - Update Rows to Show
   * @param {number} newRows
   */
  const changeRows = useCallback(
    (newRows: number): void => {
      dispatch(tableActions.setTableRows(newRows));
    },
    [dispatch]
  );

  /**
   * @functions {changePage} - Update Active Page
   * @param {number} newPage
   */
  const changePage = useCallback(
    (newPage: number): void => {
      dispatch(tableActions.setTablePage(newPage));
    },
    [dispatch]
  );

  // The handleViewDetails function is now handled directly in the ViewDetailsButton component
  // which navigates to the email details page

  const filteredData = useMemo(
    () =>
      staticPhishingInfo.filter((item) => {
        if (!searchTerm) return true;

        const searchLower = searchTerm.toLowerCase();
        return activeFilters.some((filter) => {
          const value = item[filter as keyof typeof item];
          return value?.toString().toLowerCase().includes(searchLower);
        });
      }),
    [searchTerm, activeFilters]
  );

  const newApplication = useMemo(
    () =>
      filteredData.map((item) => ({
        sn: item.sn,
        messageId: item.messageId,
        sendersEmail: item.sendersEmail,
        receiversEmail: item.receiversEmail,
        subject: item.subject,
        createTime: item.createTime,
        details: item.messageId,
      })),
    [filteredData]
  );

  const tablePaginationData = useAppSelector(GET_TABLE_PAGINATION_DATA);
  // Static pagination data
  const { page, limit, isLoading } = tablePaginationData;

  const contactPagination = {
    totalCount: filteredData.length,
    totalPages: 79, // From the image reference
  };

  // Table headers
  const headers: IHeader[] = [
    {
      id: "sn",
      name: "SN",
      hidden: false,
      width: 60,
      type: TableComponentEnum.STRING,
    },
    {
      id: "messageId",
      name: "MESSAGE ID",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "sendersEmail",
      name: "SENDER'S EMAIL",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "receiversEmail",
      name: "RECEIVER'S EMAIL",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "subject",
      name: "SUBJECT",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "createTime",
      name: "CREATE TIME",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "details",
      name: "DETAILS",
      hidden: false,
      width: 100,
      type: TableComponentEnum.COMPONENT,
      component: ViewDetailsButton,
    },
  ];

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Phishing",
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  const handleSearch = useCallback(
    (term: string, filters: string[]) => {
      setSearchTerm(term);
      setActiveFilters(filters);
      // Reset to first page when searching
      dispatch(tableActions.setTablePage(0));
    },
    [dispatch]
  );

  /**
   * @function {handleColumnVisibilityChange} - Handle column visibility changes
   * @param {string[]} newVisibleColumns - New array of visible column IDs
   */
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  return {
    getters: {
      breadcrumbs,
      headers,
      contactPagination,
      tablePaginationData,
      isLoading: false,
      isOpenDrawer: false,
      filter: {} as any,
      newApplication,
      height,
      isMobileView,
      FILTER_OPTIONS,
      COLUMN_OPTIONS,
      visibleColumns,
      searchTerm,
    },
    handlers: {
      changePage,
      changeRows,
      handleSearch,
      handleColumnVisibilityChange,
    },

    ref: ref as MeasureRefType,
  };
};
