import React from 'react';
import { Button } from '@mui/material';
import { useRouter } from 'next/navigation';
import { RoutePathEnum } from '@/enum';

interface ViewDetailsButtonProps {
  onClick?: (value: string) => void;
  value: string;
  index: number;
  useDirectNavigation?: boolean;
  source?: string;
}

export const ViewDetailsButton: React.FC<ViewDetailsButtonProps> = ({
  onClick,
  value,
  index,
  useDirectNavigation = true,
  source = 'pending'
}) => {
  const router = useRouter();

  const handleClick = () => {
    if (useDirectNavigation) {
      // Navigate directly to the details page with source parameter
      router.push(`/disputes/details/${value}?source=${source}`);
    } else if (onClick) {
      // Use the provided onClick handler (for backward compatibility)
      onClick(value);
    }
  };

  return (
    <Button onClick={handleClick} size="small" variant="contained">
      View
    </Button>
  );
};

export default ViewDetailsButton;
