"use client";

import React, { useState, useMemo, useCallback } from "react";
import {
  Box,
  Paper,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  useTheme,
  IconButton,
  Autocomplete,
  Chip,
  InputAdornment
} from "@mui/material";
import { PageHeader, SearchBar, TableComponent, ActiveColumn } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import { Icon } from "@/components/common/icon";
import {
  faPlus,
  faCalendarAlt,
  faClock,
  faBuilding,
  faKey
} from "@fortawesome/free-solid-svg-icons";
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DatePicker, TimePicker } from '@mui/x-date-pickers';
import { format, parse } from 'date-fns';

// Mock data for customers (for dropdown selection)
const mockCustomers = [
  { id: 1, name: "Oracle" },
  { id: 2, name: "LinkedIn" },
  { id: 3, name: "Twitter" },
  { id: 4, name: "Tesla" },
  { id: 5, name: "IBM" },
  { id: 6, name: "Microsoft" },
  { id: 7, name: "Apple" },
  { id: 8, name: "Google" },
  { id: 9, name: "Facebook" },
  { id: 10, name: "Amazon" }
];

// Mock data for licenses
const mockLicenses = [
  {
    id: 1,
    sn: 1,
    licenseId: "LIC-00001",
    email: "<EMAIL>",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Active",
    customerId: 1
  },
  {
    id: 2,
    sn: 2,
    licenseId: "LIC-00002",
    email: "<EMAIL>",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Active",
    customerId: 2
  },
  {
    id: 3,
    sn: 3,
    licenseId: "LIC-00003",
    email: "<EMAIL>",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Active",
    customerId: 3
  },
  {
    id: 4,
    sn: 4,
    licenseId: "LIC-00004",
    email: "<EMAIL>",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Active",
    customerId: 4
  },
  {
    id: 5,
    sn: 5,
    licenseId: "LIC-00005",
    email: "<EMAIL>",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Active",
    customerId: 5
  },
  {
    id: 6,
    sn: 6,
    licenseId: "LIC-00006",
    email: "-",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Inactive",
    customerId: 6
  },
  {
    id: 7,
    sn: 7,
    licenseId: "LIC-00007",
    email: "-",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Inactive",
    customerId: 7
  },
  {
    id: 8,
    sn: 8,
    licenseId: "LIC-00008",
    email: "-",
    validityFrom: "02-05-2025 12:00 AM",
    validityTill: "02-05-2026 11:59 PM",
    status: "Inactive",
    customerId: 8
  },
  {
    id: 9,
    sn: 9,
    licenseId: "LIC-00009",
    email: "-",
    validityFrom: "06-05-2025 12:00 AM",
    validityTill: "06-05-2026 11:59 PM",
    status: "Inactive",
    customerId: 9
  },
  {
    id: 10,
    sn: 10,
    licenseId: "LIC-00010",
    email: "-",
    validityFrom: "06-05-2025 12:00 AM",
    validityTill: "06-05-2026 11:59 PM",
    status: "Inactive",
    customerId: 10
  }
];

const LicensesPage = () => {
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [addLicenseDialogOpen, setAddLicenseDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "sn",
    "licenseId",
    "email",
    "validityFrom",
    "validityTill",
    "status",
    "actions",
  ]);

  // Form state for add license
  const [formData, setFormData] = useState({
    customer: null as any,
    numberOfLicenses: 1,
    validFromDate: new Date(),
    validFromTime: new Date(),
    validTillDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    validTillTime: new Date(new Date().setHours(23, 59, 0, 0))
  });

  // Column options for the ActiveColumn component
  const columnOptions = [
    { value: "sn", label: "SN" },
    { value: "licenseId", label: "License ID" },
    { value: "email", label: "Email" },
    { value: "validityFrom", label: "Validity From" },
    { value: "validityTill", label: "Validity Till" },
    { value: "status", label: "Status" },
    { value: "actions", label: "Actions" },
  ];

  // Filter options for the SearchBar component
  const filterOptions = [
    { value: "licenseId", label: "License ID" },
    { value: "email", label: "Email" },
    { value: "status", label: "Status" },
  ];

  // Handle search
  const handleSearch = useCallback((value: string, filters: string[] = []) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page on search
  }, []);

  // Handle column visibility change
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  // Handle customer selection change
  const handleCustomerChange = useCallback((customer: any) => {
    setSelectedCustomer(customer);
    setPage(1); // Reset to first page on customer change
  }, []);

  // Handle page change
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Handle rows per page change
  const handleRowsPerPageChange = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1); // Reset to first page when changing rows per page
  }, []);

  // Handle add license dialog open
  const handleAddLicenseClick = useCallback(() => {
    setFormData({
      customer: null,
      numberOfLicenses: 1,
      validFromDate: new Date(),
      validFromTime: new Date(),
      validTillDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      validTillTime: new Date(new Date().setHours(23, 59, 0, 0))
    });
    setAddLicenseDialogOpen(true);
  }, []);

  // Handle form input change
  const handleFormInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name as string]: value
    }));
  }, []);

  // Handle customer selection in form
  const handleFormCustomerChange = useCallback((_: any, value: any) => {
    setFormData(prev => ({
      ...prev,
      customer: value
    }));
  }, []);

  // Handle date and time changes
  const handleDateChange = useCallback((name: string, date: Date | null) => {
    if (date) {
      setFormData(prev => ({
        ...prev,
        [name]: date
      }));
    }
  }, []);

  // Handle form submission
  const handleFormSubmit = useCallback(() => {
    // In a real app, you would save the changes here
    console.log("Form submitted:", formData);
    setAddLicenseDialogOpen(false);
  }, [formData]);

  // Handle unreserve license
  const handleUnreserve = useCallback((license: any) => {
    // In a real app, you would unreserve the license here
    console.log("Unreserve license:", license);
  }, []);

  // Filter data based on search term and selected customer
  const filteredData = useMemo(() => {
    let filtered = mockLicenses;

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(license =>
        license.licenseId.toLowerCase().includes(searchLower) ||
        license.email.toLowerCase().includes(searchLower) ||
        license.status.toLowerCase().includes(searchLower)
      );
    }

    // Filter by selected customer
    if (selectedCustomer) {
      filtered = filtered.filter(license => license.customerId === selectedCustomer.id);
    }

    return filtered;
  }, [mockLicenses, searchTerm, selectedCustomer]);

  // Calculate total pages
  const totalCount = filteredData.length;

  // Create table headers
  const headers = useMemo(() => [
    { id: "sn", label: "SN", sortable: true },
    { id: "licenseId", label: "LICENSE ID", sortable: true },
    { id: "email", label: "EMAIL", sortable: true },
    { id: "validityFrom", label: "VALIDITY FROM", sortable: true },
    { id: "validityTill", label: "VALIDITY TILL", sortable: true },
    { id: "status", label: "STATUS", sortable: true,
      renderCell: (row: any) => (
        <Chip
          label={row.status}
          color={row.status === "Active" ? "success" : "error"}
          size="small"
        />
      )
    },
    {
      id: "actions",
      label: "ACTIONS",
      sortable: false,
      renderCell: (row: any) => (
        <Button
          variant="text"
          color="error"
          size="small"
          onClick={() => handleUnreserve(row)}
        >
          Unreserve
        </Button>
      )
    },
  ], [handleUnreserve]);

  // Create header with search and column selector
  const header = useMemo(() => (
    <SuppressHydrationWarning>
      <PageHeader
        title="License Management"
        breadcrumbs={[
          { name: "Dashboard", path: "/dashboard", forwardParam: false },
          { name: "Super Admin", path: "/super-admin/dashboard", forwardParam: false },
          { name: "Licenses", path: "/super-admin/licenses", forwardParam: false },
        ]}
        actions={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SearchBar
              placeholder="Search licenses..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              onClear={() => handleSearch("")}
              sx={{ minWidth: 300 }}
            />
            <ActiveColumn
              columnOptions={columnOptions}
              visibleColumns={visibleColumns}
              onColumnVisibilityChange={handleColumnVisibilityChange}
              variant="popover"
            />
            <Autocomplete
              options={mockCustomers}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Select Customer"
                  variant="outlined"
                  size="small"
                />
              )}
              value={selectedCustomer}
              onChange={(_, newValue) => handleCustomerChange(newValue)}
              sx={{ minWidth: 200 }}
            />
            <Button
              variant="contained"
              startIcon={<Icon icon={faPlus} size="small" onlyIcon />}
              onClick={handleAddLicenseClick}
              size="small"
              sx={{
                bgcolor: '#3A52A6',
                '&:hover': {
                  bgcolor: '#2A3F8F',
                },
                padding: '6px 16px',
                fontWeight: 500,
              }}
            >
              Add License
            </Button>
          </Box>
        }
      />
    </SuppressHydrationWarning>
  ), [handleSearch, searchTerm, columnOptions, visibleColumns, handleColumnVisibilityChange, selectedCustomer, handleCustomerChange, handleAddLicenseClick]);

  // Create table component
  const table = useMemo(() => (
    <Paper elevation={0} sx={{ mt: 3, borderRadius: 1, overflow: 'hidden' }}>
      <SuppressHydrationWarning>
        <TableComponent<any>
          isLoading={false}
          headerField={headers}
          tableBody={filteredData}
          paginationData={{
            onPageChange: handlePageChange,
            onRowsPerPageChange: handleRowsPerPageChange,
            total: totalCount,
            page: page,
            limit: rowsPerPage,
          }}
          translation={{
            noDataTitle: "No Licenses Found",
          }}
          maxHeight={600}
          onRowClick={() => {}}
          visibleColumns={visibleColumns}
        />
      </SuppressHydrationWarning>
    </Paper>
  ), [
    headers,
    filteredData,
    handlePageChange,
    handleRowsPerPageChange,
    totalCount,
    page,
    rowsPerPage,
    visibleColumns,
  ]);

  return (
    <div>
      {header}
      {table}

      {/* Add License Dialog */}
      <Dialog open={addLicenseDialogOpen} onClose={() => setAddLicenseDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Typography variant="h6" component="div" sx={{ textAlign: 'center', fontWeight: 'bold' }}>
            Create Licenses
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 1 }}>
            Fill in the details to create new licenses
          </Typography>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <Autocomplete
                  options={mockCustomers}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Customer"
                      required
                      InputProps={{
                        ...params.InputProps,
                        startAdornment: (
                          <>
                            <Box sx={{ mr: 1, color: 'text.secondary' }}>
                              <Icon icon={faBuilding} size="small" onlyIcon />
                            </Box>
                            {params.InputProps.startAdornment}
                          </>
                        ),
                      }}
                    />
                  )}
                  value={formData.customer}
                  onChange={handleFormCustomerChange}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Number of Licenses"
                name="numberOfLicenses"
                type="number"
                value={formData.numberOfLicenses}
                onChange={handleFormInputChange}
                fullWidth
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Icon icon={faKey} size="small" onlyIcon />
                    </InputAdornment>
                  ),
                  inputProps: { min: 1 }
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom required>
                Valid From*
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    value={formData.validFromDate}
                    onChange={(date) => handleDateChange('validFromDate', date)}
                    format="dd-MM-yyyy"
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <Icon icon={faCalendarAlt} size="small" onlyIcon />
                            </InputAdornment>
                          )
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <TimePicker
                    value={formData.validFromTime}
                    onChange={(time) => handleDateChange('validFromTime', time)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <Icon icon={faClock} size="small" onlyIcon />
                            </InputAdornment>
                          )
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom required>
                Valid Till*
              </Typography>
              <Box sx={{ display: 'flex', gap: 2 }}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    value={formData.validTillDate}
                    onChange={(date) => handleDateChange('validTillDate', date)}
                    format="dd-MM-yyyy"
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <Icon icon={faCalendarAlt} size="small" onlyIcon />
                            </InputAdornment>
                          )
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <TimePicker
                    value={formData.validTillTime}
                    onChange={(time) => handleDateChange('validTillTime', time)}
                    slotProps={{
                      textField: {
                        fullWidth: true,
                        InputProps: {
                          startAdornment: (
                            <InputAdornment position="start">
                              <Icon icon={faClock} size="small" onlyIcon />
                            </InputAdornment>
                          )
                        }
                      }
                    }}
                  />
                </LocalizationProvider>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3, justifyContent: 'space-between' }}>
          <Button
            onClick={() => setAddLicenseDialogOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleFormSubmit}
            disabled={!formData.customer || formData.numberOfLicenses < 1}
            sx={{ minWidth: 150 }}
          >
            Create {formData.numberOfLicenses} {formData.numberOfLicenses === 1 ? 'License' : 'Licenses'}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default LicensesPage;