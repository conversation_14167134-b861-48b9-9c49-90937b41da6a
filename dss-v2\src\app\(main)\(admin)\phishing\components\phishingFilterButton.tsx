"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import PhishingIcon from '@mui/icons-material/Phishing';

interface PhishingFilterButtonProps {
  filterOptions: { label: string; value: string; type?: 'text' | 'date' | 'email' }[];
  onApplyFilters: (filters: string[]) => void;
}

const PhishingFilterButton: React.FC<PhishingFilterButtonProps> = ({
  filterOptions,
  onApplyFilters,
}) => {

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Phishing Filters"
      title="Phishing Filters"
      description="Apply filters to refine your phishing detection results"
      icon={<PhishingIcon />}
    />
  );
};

export default PhishingFilterButton;
