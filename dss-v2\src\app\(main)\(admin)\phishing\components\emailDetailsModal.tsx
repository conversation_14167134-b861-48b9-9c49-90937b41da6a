"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  Typography,
  IconButton,
  Box,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableRow,
  useTheme,
  Chip,
  Paper,
  Grid,
  Divider,
  Fade,
  Tooltip,
  alpha,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import SecurityIcon from '@mui/icons-material/Security';
import HistoryIcon from '@mui/icons-material/History';
import EmailIcon from '@mui/icons-material/Email';
import { useThemeWithToggle } from '@/context/ThemeContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`email-tabpanel-${index}`}
      aria-labelledby={`email-tab-${index}`}
      {...other}
      style={{ width: '100%' }}
    >
      <Fade in={value === index} timeout={300}>
        <Box sx={{ p: 1.5 }}>
          {children}
        </Box>
      </Fade>
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `email-tab-${index}`,
    'aria-controls': `email-tabpanel-${index}`,
  };
}

interface EmailDetailsModalProps {
  open: boolean;
  onClose: () => void;
  emailId: string;
}

interface EmailData {
  id: string;
  messageId: string;
  sendersEmail: string;
  receiversEmail: string;
  subject: string;
  createTime: string;
  securityStatus: {
    final: string;
    reason: string;
  };
  authentication: {
    dkim: boolean;
    spf: boolean;
    dmarc: boolean;
  };
  threatIntelligence: {
    databaseCheck: boolean;
  };
  contentAnalysis: {
    urls: Array<{
      url: string;
      status: string;
    }>;
    attachments: Array<{
      name: string;
      status: string;
    }>;
  };
  disputeHistory: Array<{
    date: string;
    userComment: string;
    adminResponse: string;
    adminStatus: string;
  }>;
  emailBody: string;
}

// Mock data for demonstration
const mockEmailData: EmailData = {
  id: "456",
  messageId: "f9b4c3e-da5c-1...",
  sendersEmail: "<EMAIL>",
  receiversEmail: "<EMAIL>",
  subject: "Subject 278",
  createTime: "01-01-2025 12:00 AM",
  securityStatus: {
    final: "UNSAFE",
    reason: "Status reflected by ai"
  },
  authentication: {
    dkim: true,
    spf: true,
    dmarc: true
  },
  threatIntelligence: {
    databaseCheck: true
  },
  contentAnalysis: {
    urls: [
      { url: "https://example.com/page123", status: "Malicious" },
      { url: "https://example.com/page456", status: "Safe" }
    ],
    attachments: [
      { name: "attachment/file_1.pdf", status: "Unsupported" },
      { name: "attachment/file_2.pdf", status: "Malicious" },
      { name: "attachment/file_3.pdf", status: "Safe" }
    ]
  },
  disputeHistory: [
    {
      date: "-",
      userComment: "No updates available",
      adminResponse: "No updates for this email",
      adminStatus: "None"
    }
  ],
  emailBody: "Email body content 123"
};

export const EmailDetailsModal: React.FC<EmailDetailsModalProps> = ({ open, onClose, emailId }) => {
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';

  // In a real application, you would fetch the email data based on the emailId
  const emailData = mockEmailData;

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusChip = (status: string) => {
    let color = "default";
    let icon = null;
    let bgColor = "";
    let textColor = "";

    switch(status.toLowerCase()) {
      case "malicious":
        color = "error";
        icon = <CancelIcon fontSize="small" />;
        bgColor = isDarkMode ? alpha('#f44336', 0.15) : alpha('#f44336', 0.08);
        textColor = isDarkMode ? '#ff7961' : '#d32f2f';
        break;
      case "safe":
        color = "success";
        icon = <CheckCircleIcon fontSize="small" />;
        bgColor = isDarkMode ? alpha('#4caf50', 0.15) : alpha('#4caf50', 0.08);
        textColor = isDarkMode ? '#80e27e' : '#2e7d32';
        break;
      case "unsupported":
        color = "warning";
        icon = <WarningAmberIcon fontSize="small" />;
        bgColor = isDarkMode ? alpha('#ff9800', 0.15) : alpha('#ff9800', 0.08);
        textColor = isDarkMode ? '#ffb74d' : '#e65100';
        break;
      default:
        color = "default";
        bgColor = isDarkMode ? alpha('#9e9e9e', 0.15) : alpha('#9e9e9e', 0.08);
        textColor = isDarkMode ? '#e0e0e0' : '#616161';
    }

    return (
      <Tooltip title={`Status: ${status}`} arrow placement="top">
        <Chip
          label={status}
          size="small"
          icon={icon}
          sx={{
            ml: 1,
            bgcolor: bgColor,
            color: textColor,
            fontWeight: 500,
            border: '1px solid',
            borderColor: 'transparent',
            '& .MuiChip-icon': {
              color: textColor
            },
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              boxShadow: `0 2px 4px ${isDarkMode ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.1)'}`,
              borderColor: textColor
            }
          }}
        />
      </Tooltip>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      TransitionComponent={Fade}
      transitionDuration={300}
      PaperProps={{
        sx: {
          borderRadius: 1,
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
          overflow: 'hidden',
          border: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)"}`,
        }
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
          pb: 1,
          pt: 1.5,
          px: 2,
          bgcolor: isDarkMode ? alpha('#3A52A6', 0.15) : alpha('#3A52A6', 0.05)
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SecurityIcon
            sx={{
              mr: 1.5,
              color: isDarkMode ? '#8c9eff' : '#3A52A6',
              fontSize: '1.5rem'
            }}
          />
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? "#fff" : "#000",
              fontSize: '1rem'
            }}
          >
            Email Details <Box component="span" sx={{ color: isDarkMode ? '#8c9eff' : '#3A52A6', ml: 0.5 }}>#{emailData.id}</Box>
          </Typography>
        </Box>
        <IconButton
          edge="end"
          size="small"
          onClick={onClose}
          aria-label="close"
          sx={{
            color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
            '&:hover': {
              bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            }
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{
          px: 2,
          pt: 1.5,
          pb: 0.5,
          display: 'flex',
          alignItems: 'center',
          borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)"}`,
        }}>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.6)",
              fontSize: '0.8rem',
              fontStyle: 'italic'
            }}
          >
            Detailed information about the selected email report
          </Typography>
          <Chip
            label={emailData.securityStatus.final}
            color={emailData.securityStatus.final === "UNSAFE" ? "error" : "success"}
            size="small"
            sx={{ ml: 'auto', height: '20px', fontSize: '0.7rem' }}
          />
        </Box>

        <Box sx={{ width: '100%', mt: 0 }}>
          <Box sx={{
            borderBottom: 1,
            borderColor: 'divider',
            bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : '#f5f5f5',
          }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="email details tabs"
              variant="fullWidth"
              sx={{
                minHeight: '42px',
                '& .MuiTab-root': {
                  textTransform: 'none',
                  minHeight: '42px',
                  py: 0.5,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                  fontSize: '0.85rem',
                  transition: 'all 0.2s',
                },
                '& .Mui-selected': {
                  fontWeight: 'bold',
                  color: isDarkMode ? '#fff' : '#3A52A6',
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: isDarkMode ? '#8c9eff' : '#3A52A6',
                  height: 3
                }
              }}
            >
              <Tab
                icon={<SecurityIcon fontSize="small" />}
                iconPosition="start"
                label="Details & Security"
                {...a11yProps(0)}
              />
              <Tab
                icon={<HistoryIcon fontSize="small" />}
                iconPosition="start"
                label="Dispute History"
                {...a11yProps(1)}
              />
              <Tab
                icon={<EmailIcon fontSize="small" />}
                iconPosition="start"
                label="Email Body"
                {...a11yProps(2)}
              />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={1.5}>
              {/* Left Column */}
              <Grid item xs={12} md={6}>
                {/* Basic Information */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 'bold',
                      fontSize: '0.85rem',
                      color: isDarkMode ? '#8c9eff' : '#3A52A6',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <EmailIcon fontSize="small" sx={{ mr: 0.5 }} /> Basic Information
                  </Typography>
                  <Divider sx={{ mb: 1, opacity: 0.5 }} />
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.8rem' }}>
                        <strong>Message ID:</strong> {emailData.messageId}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.8rem' }}>
                        <strong>From:</strong> {emailData.sendersEmail}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.8rem' }}>
                        <strong>To:</strong> {emailData.receiversEmail}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.8rem' }}>
                        <strong>Subject:</strong> {emailData.subject}
                      </Typography>
                      <Typography variant="body2" component="div" sx={{ fontSize: '0.8rem' }}>
                        <strong>Created:</strong> {emailData.createTime}
                      </Typography>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Authentication Checks */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 'bold',
                      fontSize: '0.85rem',
                      color: isDarkMode ? '#8c9eff' : '#3A52A6',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <SecurityIcon fontSize="small" sx={{ mr: 0.5 }} /> Authentication Checks
                  </Typography>
                  <Divider sx={{ mb: 1, opacity: 0.5 }} />
                  <Grid container spacing={1}>
                    <Grid item xs={4}>
                      <Tooltip title="DomainKeys Identified Mail">
                        <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.8rem' }}>
                          {emailData.authentication.dkim ?
                            <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} /> :
                            <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                          }
                          <strong>DKIM</strong>
                        </Typography>
                      </Tooltip>
                    </Grid>
                    <Grid item xs={4}>
                      <Tooltip title="Sender Policy Framework">
                        <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.8rem' }}>
                          {emailData.authentication.spf ?
                            <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} /> :
                            <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                          }
                          <strong>SPF</strong>
                        </Typography>
                      </Tooltip>
                    </Grid>
                    <Grid item xs={4}>
                      <Tooltip title="Domain-based Message Authentication, Reporting & Conformance">
                        <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.8rem' }}>
                          {emailData.authentication.dmarc ?
                            <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} /> :
                            <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                          }
                          <strong>DMARC</strong>
                        </Typography>
                      </Tooltip>
                    </Grid>
                  </Grid>
                </Paper>

                {/* Threat Intelligence */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 'bold',
                      fontSize: '0.85rem',
                      color: isDarkMode ? '#8c9eff' : '#3A52A6',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <SecurityIcon fontSize="small" sx={{ mr: 0.5 }} /> Threat Intelligence
                  </Typography>
                  <Divider sx={{ mb: 1, opacity: 0.5 }} />
                  <Typography variant="body2" component="div" sx={{ display: 'flex', alignItems: 'center', fontSize: '0.8rem' }}>
                    {emailData.threatIntelligence.databaseCheck ?
                      <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5 }} /> :
                      <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5 }} />
                    }
                    <strong>Database Check</strong>
                  </Typography>
                </Paper>
              </Grid>

              {/* Right Column */}
              <Grid item xs={12} md={6}>
                {/* Security Status */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ?
                      (emailData.securityStatus.final === "UNSAFE" ? alpha('#f44336', 0.1) : alpha('#4caf50', 0.1)) :
                      (emailData.securityStatus.final === "UNSAFE" ? alpha('#f44336', 0.05) : alpha('#4caf50', 0.05)),
                    borderLeft: `3px solid ${emailData.securityStatus.final === "UNSAFE" ?
                      (isDarkMode ? '#ff7961' : '#d32f2f') :
                      (isDarkMode ? '#80e27e' : '#2e7d32')}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 'bold',
                      fontSize: '0.85rem',
                      color: emailData.securityStatus.final === "UNSAFE" ?
                        (isDarkMode ? '#ff7961' : '#d32f2f') :
                        (isDarkMode ? '#80e27e' : '#2e7d32'),
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    {emailData.securityStatus.final === "UNSAFE" ?
                      <CancelIcon fontSize="small" sx={{ mr: 0.5 }} /> :
                      <CheckCircleIcon fontSize="small" sx={{ mr: 0.5 }} />
                    }
                    Security Status
                  </Typography>
                  <Divider sx={{ mb: 1, opacity: 0.5 }} />
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 1
                  }}>
                    <Typography variant="body2" component="div" sx={{ fontSize: '0.8rem' }}>
                      <strong>Final Status:</strong>
                    </Typography>
                    <Chip
                      label={emailData.securityStatus.final}
                      color={emailData.securityStatus.final === "UNSAFE" ? "error" : "success"}
                      size="small"
                      sx={{ height: '20px', fontSize: '0.7rem' }}
                    />
                  </Box>
                  <Typography variant="body2" component="div" sx={{ fontSize: '0.8rem' }}>
                    <strong>Status Reason:</strong> {emailData.securityStatus.reason}
                  </Typography>
                </Paper>

                {/* Content Analysis */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 1,
                      fontWeight: 'bold',
                      fontSize: '0.85rem',
                      color: isDarkMode ? '#8c9eff' : '#3A52A6',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <SecurityIcon fontSize="small" sx={{ mr: 0.5 }} /> Content Analysis
                  </Typography>
                  <Divider sx={{ mb: 1, opacity: 0.5 }} />

                  {/* URLs */}
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 0.5,
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}
                  >
                    URLs: {emailData.contentAnalysis.urls.length}
                  </Typography>
                  <Box sx={{
                    maxHeight: '80px',
                    overflowY: 'auto',
                    mb: 1.5,
                    pr: 1,
                    '&::-webkit-scrollbar': {
                      width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      background: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                      borderRadius: '10px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                      borderRadius: '10px',
                    },
                  }}>
                    {emailData.contentAnalysis.urls.map((url, index) => (
                      <Typography
                        key={index}
                        variant="body2"
                        component="div"
                        sx={{
                          mb: 0.5,
                          display: 'flex',
                          alignItems: 'center',
                          fontSize: '0.75rem',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}
                      >
                        <Box component="span" sx={{ mr: 0.5, color: 'primary.main', fontSize: '0.8rem' }}>→</Box>
                        <Box
                          component="span"
                          sx={{
                            maxWidth: '150px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            display: 'inline-block'
                          }}
                        >
                          {url.url}
                        </Box>
                        {getStatusChip(url.status)}
                      </Typography>
                    ))}
                  </Box>

                  {/* Attachments */}
                  <Typography
                    variant="subtitle2"
                    sx={{
                      mb: 0.5,
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}
                  >
                    Attachments: {emailData.contentAnalysis.attachments.length}
                  </Typography>
                  <Box sx={{
                    maxHeight: '80px',
                    overflowY: 'auto',
                    pr: 1,
                    '&::-webkit-scrollbar': {
                      width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      background: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                      borderRadius: '10px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                      borderRadius: '10px',
                    },
                  }}>
                    {emailData.contentAnalysis.attachments.map((attachment, index) => (
                      <Typography
                        key={index}
                        variant="body2"
                        component="div"
                        sx={{
                          mb: 0.5,
                          display: 'flex',
                          alignItems: 'center',
                          fontSize: '0.75rem'
                        }}
                      >
                        <Box component="span" sx={{ mr: 0.5, color: 'primary.main', fontSize: '0.8rem' }}>→</Box>
                        <Box
                          component="span"
                          sx={{
                            maxWidth: '150px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            display: 'inline-block'
                          }}
                        >
                          {attachment.name}
                        </Box>
                        {getStatusChip(attachment.status)}
                      </Typography>
                    ))}
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Paper
              elevation={0}
              sx={{
                borderRadius: '4px',
                overflow: 'hidden',
                border: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
              }}
            >
              <Box sx={{
                p: 1.5,
                bgcolor: isDarkMode ? alpha('#3A52A6', 0.15) : alpha('#3A52A6', 0.05),
                borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
                display: 'flex',
                alignItems: 'center'
              }}>
                <HistoryIcon
                  sx={{
                    mr: 1,
                    color: isDarkMode ? '#8c9eff' : '#3A52A6',
                    fontSize: '1.2rem'
                  }}
                />
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 'bold',
                    fontSize: '0.9rem',
                    color: isDarkMode ? '#fff' : '#000'
                  }}
                >
                  Dispute History
                </Typography>
              </Box>
              <Table size="small" sx={{ tableLayout: 'fixed' }}>
                <TableBody>
                  <TableRow sx={{
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : '#f5f5f5',
                    '& th': {
                      color: isDarkMode ? '#fff' : '#000',
                      fontWeight: 'bold',
                      fontSize: '0.75rem',
                      py: 1
                    }
                  }}>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '15%' }}>Date</TableCell>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '35%' }}>User Comment</TableCell>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '35%' }}>Admin Response</TableCell>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '15%' }}>Status</TableCell>
                  </TableRow>
                  {emailData.disputeHistory.map((dispute, index) => (
                    <TableRow key={index} sx={{
                      '&:nth-of-type(odd)': {
                        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)'
                      },
                      '& td': {
                        fontSize: '0.75rem',
                        py: 1,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.8)'
                      },
                      transition: 'background-color 0.2s',
                      '&:hover': {
                        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)'
                      }
                    }}>
                      <TableCell>{dispute.date}</TableCell>
                      <TableCell sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {dispute.userComment}
                      </TableCell>
                      <TableCell sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}>
                        {dispute.adminResponse}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={dispute.adminStatus}
                          size="small"
                          sx={{
                            height: '20px',
                            fontSize: '0.7rem',
                            bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)',
                            color: isDarkMode ? '#fff' : '#000'
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Paper>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Paper
              elevation={0}
              sx={{
                borderRadius: '4px',
                overflow: 'hidden',
                border: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
              }}
            >
              <Box sx={{
                p: 1.5,
                bgcolor: isDarkMode ? alpha('#3A52A6', 0.15) : alpha('#3A52A6', 0.05),
                borderBottom: `1px solid ${isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <EmailIcon
                    sx={{
                      mr: 1,
                      color: isDarkMode ? '#8c9eff' : '#3A52A6',
                      fontSize: '1.2rem'
                    }}
                  />
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 'bold',
                      fontSize: '0.9rem',
                      color: isDarkMode ? '#fff' : '#000'
                    }}
                  >
                    Email Body
                  </Typography>
                </Box>
                <Chip
                  label={emailData.securityStatus.final}
                  color={emailData.securityStatus.final === "UNSAFE" ? "error" : "success"}
                  size="small"
                  sx={{ height: '20px', fontSize: '0.7rem' }}
                />
              </Box>
              <Box sx={{
                p: 2,
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.01)',
                maxHeight: '300px',
                overflowY: 'auto',
                '&::-webkit-scrollbar': {
                  width: '6px',
                },
                '&::-webkit-scrollbar-track': {
                  background: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                  borderRadius: '10px',
                },
                '&::-webkit-scrollbar-thumb': {
                  background: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
                  borderRadius: '10px',
                },
              }}>
                <Typography
                  variant="body2"
                  sx={{
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'monospace',
                    fontSize: '0.8rem',
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)',
                    lineHeight: 1.5
                  }}
                >
                  {emailData.emailBody}
                </Typography>
              </Box>
            </Paper>
          </TabPanel>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default EmailDetailsModal;
