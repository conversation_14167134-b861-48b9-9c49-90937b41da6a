"use client";

import React, { JSX, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Box } from "@mui/material";

import { AgentInstalledController } from "./agent-installed.controller";
import AgentInstalledFilterButton from "./components/agentInstalledFilterButton";

import { PageHeader, SearchBar, TableComponent } from "@/components/common";
import { ActiveColumn } from "@/components/common/activeColumn";
// We no longer need to import SystemDetailsModal

/**
 * @page {Contacts} - Display Contacts Information
 * @return {JSX.Element}
 */
export default function AgentInstalled(): JSX.Element {
  const router = useRouter();
  const { getters, handlers, ref } = AgentInstalledController();
  const {
    headers,
    tablePaginationData,
    newApplication,
    contactPagination,
    height,
    breadcrumbs,
    FILTER_OPTIONS,
    COLUMN_OPTIONS,
    visibleColumns,

    searchTerm,
  } = getters;
  const {
    changePage,
    changeRows,
    handleSearch,
    handleViewReport,
    handleColumnVisibilityChange
  } = handlers;

  const table = useMemo(
    () => (
      <Box sx={{
        '& .MuiTableCell-root': {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          maxWidth: (theme) => theme.spacing(25), // Limit maximum width
        }
      }}>
        <TableComponent<any>
          isLoading={false}
          headerField={headers}
          tableBody={newApplication}
          paginationData={{
            onPageChange: changePage,
            onRowsPerPageChange: changeRows,
            total: contactPagination.totalCount,
            page: tablePaginationData.page,
            limit: tablePaginationData.limit,
          }}
          translation={{
            noDataTitle: "No Data Found",
          }}
          maxHeight={height}
          visibleColumns={visibleColumns}
          componentProps={{
            report: {
              onClick: handleViewReport
            }
          }}
        />
      </Box>
    ),
    [
      tablePaginationData.page,
      tablePaginationData.limit,
      headers,
      newApplication,
      changePage,
      changeRows,
      contactPagination.totalCount,
      height,
      handleViewReport,
      visibleColumns
    ]
  );

  const customSearchBar = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={handleSearch}
          placeholder="Search agent installed..."
          filterOptions={FILTER_OPTIONS}
          useAdvancedFilter={false}
        />
        <AgentInstalledFilterButton
          filterOptions={FILTER_OPTIONS.map(option => ({
            ...option,
            type: option.value.includes('date') ? 'date' : 'text'
          }))}
          onApplyFilters={(filters) => handleSearch(searchTerm, filters)}
          searchTerm={searchTerm}
        />
        <ActiveColumn
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          variant="popover"
        />
      </Box>
    );
  }, [handleSearch, FILTER_OPTIONS, COLUMN_OPTIONS, visibleColumns, handleColumnVisibilityChange, searchTerm]);

  const header = useMemo(
    () => (
      <div ref={ref}>
        <PageHeader
          title="Agent Installed"
          breadcrumbs={breadcrumbs}
          actions={customSearchBar}
        />
      </div>
    ),
    [breadcrumbs, ref, customSearchBar]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
}
