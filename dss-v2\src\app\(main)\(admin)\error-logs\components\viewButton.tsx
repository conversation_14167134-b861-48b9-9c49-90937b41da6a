import React from 'react';
import { Button, Tooltip } from '@mui/material';
import { useRouter } from 'next/navigation';
import { RoutePathEnum } from '@/enum';

interface ViewButtonProps {
  onClick?: () => void;
  errorId: string;
}

export const ViewButton: React.FC<ViewButtonProps> = ({ onClick, errorId }) => {
  const router = useRouter();

  const handleClick = () => {
    // Navigate to the error details page
    router.push(`/error-logs/details/${errorId}`);

    // Also call the original onClick handler if provided (for any additional logic)
    if (onClick) {
      onClick();
    }
  };

  return (
    <Tooltip title="View Traceback">
      <Button
        onClick={handleClick}
        size="small"
        variant="contained"
        color="primary"
      >
        View
      </Button>
    </Tooltip>
  );
};

export default ViewButton;
