"use client";

import React, { JSX, useMemo, useState } from "react";
import { Paper, Box } from "@mui/material";

import { PendingDisputesController } from "./pending-disputes.controller";
import { PendingDisputesTableContainer } from "./components/pendingDisputesTable.style";
import { PendingDisputesTable } from "./components/pendingDisputesTable";
import { PageHeader, SearchBar, ActiveColumn } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import PendingDisputesFilterButton from "./components/pendingDisputesFilterButton";

/**
 * @page {PendingDisputes} - Display Pending Disputes Information
 * @return {JSX.Element}
 */
export default function PendingDisputes(): JSX.Element {
  const { getters, handlers, ref } = PendingDisputesController();
  const {
    headers,
    tablePaginationData,
    newApplication,
    contactPagination,
    height,
    breadcrumbs,
    FILTER_OPTIONS,
    COLUMN_OPTIONS,
    visibleColumns,
    initialVisibleColumns,
    isDisputeDetailsModalOpen,
    selectedDisputeId,
  } = getters;
  const [searchTerm, setSearchTerm] = useState("");
  const {
    changePage,
    changeRows,
    handleSearch,
    handleViewDetails,
    handleColumnVisibilityChange,
    handleAdminStatusChange,
    handleCloseDisputeDetailsModal
  } = handlers;

  const customSearchBar = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={(term, filters) => {
            setSearchTerm(term);
            handleSearch(term, filters);
          }}
          placeholder="Search pending disputes..."
          filterOptions={FILTER_OPTIONS}
          useAdvancedFilter={false}
        />
        <PendingDisputesFilterButton
          filterOptions={FILTER_OPTIONS.map(option => ({
            ...option,
            type: option.value === 'createdAt' ? 'date' :
                  option.value.includes('Email') ? 'email' : 'text'
          }))}
          onApplyFilters={(filters) => handleSearch(searchTerm, filters)}
          searchTerm={searchTerm}
        />
        <ActiveColumn
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          variant="popover"
        />
      </Box>
    );
  }, [handleSearch, FILTER_OPTIONS, COLUMN_OPTIONS, visibleColumns, handleColumnVisibilityChange, searchTerm]);

  const table = useMemo(
    () => (
      <Paper elevation={0} sx={{ borderRadius: 1, overflow: 'hidden' }}>
        <PendingDisputesTableContainer>
          <SuppressHydrationWarning>
            <PendingDisputesTable
              data={newApplication}
              visibleColumns={visibleColumns}
              onViewDetails={handleViewDetails}
              onAdminStatusChange={handleAdminStatusChange}
            />
          </SuppressHydrationWarning>
        </PendingDisputesTableContainer>
      </Paper>
    ),
    [
      newApplication,
      handleViewDetails,
      visibleColumns,
      handleAdminStatusChange
    ]
  );

  const header = useMemo(
    () => (
      <SuppressHydrationWarning>
        <div ref={ref}>
          <PageHeader
            title="Pending Disputes"
            breadcrumbs={breadcrumbs}
            actions={customSearchBar}
          />
        </div>
      </SuppressHydrationWarning>
    ),
    [breadcrumbs, ref, customSearchBar]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
}
