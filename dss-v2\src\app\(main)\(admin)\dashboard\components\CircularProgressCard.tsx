import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Tooltip, Fade, useTheme, IconButton, Popover } from '@mui/material';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface StatusItem {
  label: string;
  value: number;
  color: string;
}

interface CircularProgressCardProps {
  title: string;
  percentage: number;
  color: string;
  items: StatusItem[];
  totalLabel: string;
  totalValue: number;
  description?: string;
  isRefreshing?: boolean;
}

const CircularProgressCard: React.FC<CircularProgressCardProps> = ({
  title,
  percentage,
  color,
  items,
  totalLabel,
  totalValue,
  description,
  isRefreshing = false,
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  const [infoAnchorEl, setInfoAnchorEl] = useState<HTMLElement | null>(null);
  const [animatedPercentage, setAnimatedPercentage] = useState(0);

  // Animate percentage on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedPercentage(percentage);
    }, 500);
    return () => clearTimeout(timer);
  }, [percentage]);

  // Calculate the angles for the pie chart segments
  const calculateSegments = () => {
    const total = items.reduce((sum, item) => sum + item.value, 0);
    let startAngle = 0;

    return items.map((item, index) => {
      const angle = (item.value / total) * 360;
      const segment = {
        startAngle,
        endAngle: startAngle + angle,
        color: item.color,
        index,
        value: item.value,
        label: item.label,
      };
      startAngle += angle;
      return segment;
    });
  };

  const segments = calculateSegments();

  // Function to create an SVG arc path
  const describeArc = (x: number, y: number, radius: number, startAngle: number, endAngle: number) => {
    const start = polarToCartesian(x, y, radius, endAngle);
    const end = polarToCartesian(x, y, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

    return [
      "M", start.x, start.y,
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
      "L", x, y,
      "Z"
    ].join(" ");
  };

  // Helper function to convert polar coordinates to cartesian
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  const handleInfoClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setInfoAnchorEl(event.currentTarget);
  };

  const handleInfoClose = () => {
    setInfoAnchorEl(null);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2.5,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: isDarkMode ? `1px solid ${color}30` : '1px solid #e0e0e0',
        borderRadius: '12px',
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        backgroundColor: isDarkMode ? `${color}08` : 'background.paper',
        transform: isRefreshing ? 'scale(0.98)' : 'scale(1)',
        opacity: isRefreshing ? 0.8 : 1,
        '&:hover': {
          boxShadow: isDarkMode
            ? `0 8px 24px -4px rgba(0,0,0,0.3), 0 0 10px ${color}30`
            : `0 12px 24px -4px rgba(0,0,0,0.1), 0 0 10px ${color}20`,
          transform: isRefreshing ? 'scale(0.98)' : 'translateY(-4px)',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '5px',
          background: `linear-gradient(90deg, ${color} 0%, ${color}CC 100%)`,
          boxShadow: `0 1px 8px ${color}40`,
        }
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2.5,
          pb: 1.5,
          borderBottom: isDarkMode ? `1px solid ${color}20` : '1px solid #f0f0f0',
          background: isDarkMode ? 'transparent' : `linear-gradient(to bottom, ${color}08 0%, transparent 100%)`,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 4,
              height: 20,
              backgroundColor: color,
              borderRadius: 1,
              mr: 1.5
            }}
          />
          <Typography
            variant="subtitle1"
            fontWeight="bold"
            sx={{
              color: isDarkMode ? color : 'text.primary',
              letterSpacing: '0.01em',
            }}
          >
            {title}
          </Typography>

          {description && (
            <IconButton
              size="small"
              onClick={handleInfoClick}
              sx={{
                ml: 0.5,
                color: color,
                backgroundColor: isDarkMode ? `${color}15` : `${color}10`,
                width: 22,
                height: 22,
                '&:hover': {
                  backgroundColor: isDarkMode ? `${color}25` : `${color}20`,
                }
              }}
            >
              <InfoOutlinedIcon sx={{ fontSize: '0.875rem' }} />
            </IconButton>
          )}
        </Box>

        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            backgroundColor: isDarkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.04)',
            px: 1.5,
            py: 0.5,
            borderRadius: 10,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              fontWeight: 'medium'
            }}
          >
            {totalLabel}: <Box component="span" sx={{ fontWeight: 'bold', color }}>{totalValue}</Box>
          </Typography>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', flexGrow: 1 }}>
        <Box
          sx={{
            position: 'relative',
            display: 'inline-flex',
            width: 140,
            height: 140,
            justifyContent: 'center',
            alignItems: 'center',
            ml: 1,
          }}
        >
          {/* Custom SVG Pie Chart */}
          <svg width="140" height="140" viewBox="0 0 140 140">
            {/* Background circle */}
            <circle
              cx="70"
              cy="70"
              r="60"
              fill={isDarkMode ? 'rgba(255,255,255,0.05)' : '#f5f5f5'}
              stroke={isDarkMode ? 'rgba(255,255,255,0.1)' : '#e0e0e0'}
              strokeWidth="1"
            />

            {/* Pie segments */}
            {segments.map((segment, index) => {
              const isHovered = hoveredItem === index;
              const radius = isHovered ? 62 : 60;

              // Calculate center offset direction for "pop out" effect
              const midAngle = (segment.startAngle + segment.endAngle) / 2;
              const offsetX = isHovered ? Math.cos((midAngle - 90) * Math.PI / 180) * 4 : 0;
              const offsetY = isHovered ? Math.sin((midAngle - 90) * Math.PI / 180) * 4 : 0;

              // Adjust angles slightly to create small gaps between segments
              const adjustedStartAngle = segment.startAngle + 1;
              const adjustedEndAngle = segment.endAngle - 1;

              return (
                <Tooltip
                  key={index}
                  title={
                    <Box sx={{ p: 0.5 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold', color: segment.color }}>
                        {segment.label}
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 0.5 }}>
                        Count: <Box component="span" sx={{ fontWeight: 'bold' }}>{segment.value}</Box>
                      </Typography>
                      <Typography variant="body2">
                        Percentage: <Box component="span" sx={{ fontWeight: 'bold' }}>{Math.round((segment.value / totalValue) * 100)}%</Box>
                      </Typography>
                    </Box>
                  }
                  placement="top"
                  arrow
                  TransitionComponent={Fade}
                  TransitionProps={{ timeout: 300 }}
                  componentsProps={{
                    tooltip: {
                      sx: {
                        backgroundColor: isDarkMode ? '#2d2d2d' : 'white',
                        color: isDarkMode ? 'white' : 'rgba(0,0,0,0.87)',
                        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
                        border: isDarkMode ? `1px solid ${segment.color}30` : `1px solid ${segment.color}20`,
                        borderRadius: '8px',
                        p: 1.5,
                      }
                    },
                    arrow: {
                      sx: {
                        color: isDarkMode ? '#2d2d2d' : 'white',
                        '&::before': {
                          border: isDarkMode ? `1px solid ${segment.color}30` : `1px solid ${segment.color}20`,
                          backgroundColor: isDarkMode ? '#2d2d2d' : 'white',
                        }
                      }
                    }
                  }}
                >
                  <g>
                    <defs>
                      <filter id={`shadow-${index}`} x="-20%" y="-20%" width="140%" height="140%">
                        <feDropShadow dx="0" dy="0" stdDeviation="2" floodColor={segment.color} floodOpacity="0.3" />
                      </filter>
                      <linearGradient id={`gradient-${index}`} x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor={segment.color} />
                        <stop offset="100%" stopColor={`${segment.color}CC`} />
                      </linearGradient>
                    </defs>
                    <path
                      d={describeArc(70 + offsetX, 70 + offsetY, radius, adjustedStartAngle, adjustedEndAngle)}
                      fill={`url(#gradient-${index})`}
                      stroke={isDarkMode ? 'rgba(0,0,0,0.3)' : 'white'}
                      strokeWidth="1.5"
                      onMouseEnter={() => setHoveredItem(index)}
                      onMouseLeave={() => setHoveredItem(null)}
                      style={{
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        filter: isHovered ? `url(#shadow-${index})` : 'none',
                        opacity: isHovered ? 1 : 0.9,
                      }}
                    />
                  </g>
                </Tooltip>
              );
            })}

            {/* Inner circle with percentage */}
            <circle
              cx="70"
              cy="70"
              r="45"
              fill={isDarkMode ? 'rgba(0,0,0,0.2)' : 'white'}
              stroke={isDarkMode ? 'rgba(255,255,255,0.1)' : '#f0f0f0'}
              strokeWidth="1"
            />

            {/* Percentage text */}
            <text
              x="70"
              y="65"
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="22"
              fontWeight="bold"
              fill={color}
              style={{
                textShadow: isDarkMode ? '0 2px 4px rgba(0,0,0,0.3)' : '0 1px 2px rgba(0,0,0,0.1)',
                animation: isRefreshing ? 'pulse 1.5s infinite ease-in-out' : 'none',
              }}
            >
              {Math.round(animatedPercentage)}%
            </text>

            <text
              x="70"
              y="85"
              textAnchor="middle"
              dominantBaseline="middle"
              fontSize="10"
              fill={isDarkMode ? 'rgba(255,255,255,0.7)' : 'rgba(0,0,0,0.6)'}
            >
              {title.toUpperCase()}
            </text>

            {/* Progress arc - outer ring */}
            <circle
              cx="70"
              cy="70"
              r="52"
              fill="none"
              stroke={isDarkMode ? 'rgba(255,255,255,0.1)' : '#e0e0e0'}
              strokeWidth="6"
              strokeDasharray="3,1"
            />

            {/* Progress arc - colored progress */}
            <path
              d={`
                M 70 18
                A 52 52 0 ${animatedPercentage > 50 ? 1 : 0} 1 ${70 + 52 * Math.sin(animatedPercentage * 3.6 * Math.PI / 180)} ${70 - 52 * Math.cos(animatedPercentage * 3.6 * Math.PI / 180)}
              `}
              fill="none"
              stroke={`url(#progress-gradient-${color.replace('#', '')})`}
              strokeWidth="6"
              strokeLinecap="round"
              style={{
                filter: 'drop-shadow(0px 2px 4px rgba(0,0,0,0.2))',
                transition: 'all 1s ease',
              }}
            />

            <defs>
              <linearGradient id={`progress-gradient-${color.replace('#', '')}`} x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor={color} />
                <stop offset="100%" stopColor={`${color}CC`} />
              </linearGradient>
            </defs>
          </svg>
        </Box>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: 1.5,
            ml: 3,
            flexGrow: 1,
            backgroundColor: isDarkMode ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.02)',
            borderRadius: '12px',
            p: 2,
            pt: 1.5,
            border: isDarkMode ? '1px solid rgba(255,255,255,0.05)' : '1px solid rgba(0,0,0,0.03)',
          }}
        >
          <Typography
            variant="caption"
            sx={{
              color: 'text.secondary',
              mb: 0.5,
              fontWeight: 'medium',
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              fontSize: '0.7rem',
            }}
          >
            Distribution
          </Typography>

          {items.map((item, index) => {
            const isHovered = hoveredItem === index;
            const percentage = Math.round((item.value / totalValue) * 100);

            return (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  p: 1.2,
                  borderRadius: '8px',
                  transition: 'all 0.3s ease',
                  backgroundColor: isHovered
                    ? (isDarkMode ? `${item.color}20` : `${item.color}10`)
                    : (isDarkMode ? 'rgba(255,255,255,0.03)' : 'white'),
                  cursor: 'pointer',
                  border: `1px solid ${item.color}20`,
                  boxShadow: isHovered ? `0 4px 12px ${item.color}20` : 'none',
                  transform: isHovered ? 'translateY(-2px)' : 'translateY(0)',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    bottom: 0,
                    width: `${percentage}%`,
                    backgroundColor: `${item.color}10`,
                    zIndex: 0,
                    transition: 'width 1s ease-in-out',
                  }
                }}
                onMouseEnter={() => setHoveredItem(index)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', zIndex: 1 }}>
                  <Box
                    sx={{
                      width: 10,
                      height: 10,
                      borderRadius: '50%',
                      backgroundColor: item.color,
                      mr: 1.5,
                      transition: 'all 0.3s ease',
                      transform: isHovered ? 'scale(1.3)' : 'scale(1)',
                      boxShadow: isHovered ? `0 0 0 3px ${item.color}30` : 'none',
                    }}
                  />
                  <Typography
                    variant="body2"
                    color={isHovered ? item.color : 'text.secondary'}
                    sx={{
                      fontWeight: isHovered ? 'bold' : 'medium',
                      transition: 'all 0.3s ease',
                    }}
                  >
                    {item.label}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', zIndex: 1 }}>
                  <Typography
                    variant="caption"
                    sx={{
                      color: 'text.secondary',
                      mr: 1.5,
                      opacity: isHovered ? 1 : 0.7,
                    }}
                  >
                    {percentage}%
                  </Typography>

                  <Typography
                    variant="body2"
                    fontWeight="bold"
                    sx={{
                      color: item.color,
                      transition: 'all 0.3s ease',
                      px: 1,
                      py: 0.3,
                      borderRadius: '4px',
                      backgroundColor: isDarkMode ? `${item.color}20` : `${item.color}15`,
                      border: `1px solid ${item.color}30`,
                      minWidth: '36px',
                      textAlign: 'center',
                    }}
                  >
                    {item.value}
                  </Typography>
                </Box>
              </Box>
            );
          })}

          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mt: 1,
              pt: 1.5,
              borderTop: isDarkMode ? `1px solid rgba(255,255,255,0.1)` : '1px solid rgba(0,0,0,0.06)',
            }}
          >
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontWeight: 'medium' }}
            >
              {totalLabel}
            </Typography>
            <Typography
              variant="body2"
              fontWeight="bold"
              sx={{
                color,
                px: 1.5,
                py: 0.5,
                borderRadius: '4px',
                backgroundColor: isDarkMode ? `${color}20` : `${color}15`,
                border: `1px solid ${color}30`,
              }}
            >
              {totalValue}
            </Typography>
          </Box>
        </Box>
      </Box>

      {description && Boolean(infoAnchorEl) && (
        <Popover
          open={Boolean(infoAnchorEl)}
          anchorEl={infoAnchorEl}
          onClose={handleInfoClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          PaperProps={{
            sx: {
              p: 2,
              maxWidth: 300,
              boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
              border: isDarkMode ? `1px solid ${color}30` : 'none',
              borderRadius: '8px',
            }
          }}
        >
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1,
              color,
              fontWeight: 'bold',
              borderBottom: `1px solid ${color}20`,
              pb: 0.5
            }}
          >
            {title} Information
          </Typography>
          <Typography variant="body2">{description}</Typography>
        </Popover>
      )}
    </Paper>
  );
};

export default CircularProgressCard;
