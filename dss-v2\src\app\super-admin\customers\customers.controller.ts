"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { useTheme, useMediaQ<PERSON>y, Theme } from "@mui/material";
import useMeasure from "react-use-measure";

import {
  GET_TABLE_PAGINATION_DATA,
  useAppDispatch,
  useAppSelector,
} from "@/redux";
import { <PERSON><PERSON>eader, TableComponentEnum, IBreadcrumbDisplay } from "@/components/common";
import { MeasureRefType } from "@/interfaces";
import { RoutePathEnum } from "@/enum";
import { useAppSnackbar } from "@/hooks/snackbar.hook";
import { ActionButtons } from "./components/actionButtons";

// Static customer data matching the table structure from the image
const staticCustomerInfo = [
  {
    sn: 1,
    logo: "O",
    name: "Oracle",
    email: "<EMAIL>",
    contact: "7418523647",
    createdAt: "19-06-2025 08:18 PM",
  },
  {
    sn: 2,
    logo: "<PERSON>",
    name: "<PERSON>edIn",
    email: "<EMAIL>",
    contact: "7418523646",
    createdAt: "19-06-2025 08:17 PM",
  },
  {
    sn: 3,
    logo: "T",
    name: "Twitter",
    email: "<EMAIL>",
    contact: "7418523645",
    createdAt: "19-06-2025 08:16 PM",
  },
  {
    sn: 4,
    logo: "T",
    name: "Tesla",
    email: "<EMAIL>",
    contact: "7418523699",
    createdAt: "19-06-2025 08:14 PM",
  },
  {
    sn: 5,
    logo: "I",
    name: "IBM",
    email: "<EMAIL>",
    contact: "7418523698",
    createdAt: "19-06-2025 08:12 PM",
  },
];

/**
 * Customers Controller Hook
 * @return {object}
 */
export const CustomersController = () => {
  const [ref, { height }] = useMeasure();
  const theme: Theme = useTheme();
  const isMobileView: boolean = useMediaQuery(theme.breakpoints.down("sm"));
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<string[]>([
    "name",
    "email",
    "contact",
  ]);

  const { enqueueSnackbar } = useAppSnackbar();

  const handleEdit = useCallback((customerId: string) => {
    console.log(`Editing customer: ${customerId}`);
    enqueueSnackbar("Edit functionality will be implemented soon", { variant: "info" });
  }, [enqueueSnackbar]);

  const handleDelete = useCallback((customerId: string) => {
    console.log(`Deleting customer: ${customerId}`);
    enqueueSnackbar("Delete functionality will be implemented soon", { variant: "info" });
  }, [enqueueSnackbar]);

  const handleAddCustomer = useCallback(() => {
    console.log("Adding new customer");
    enqueueSnackbar("Add customer functionality will be implemented soon", { variant: "info" });
  }, [enqueueSnackbar]);

  // Search and filter functionality
  const filteredData = useMemo(() => {
    if (!searchTerm) return staticCustomerInfo;

    return staticCustomerInfo.filter((customer) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        customer.name.toLowerCase().includes(searchLower) ||
        customer.email.toLowerCase().includes(searchLower) ||
        customer.contact.includes(searchTerm) ||
        customer.createdAt.toLowerCase().includes(searchLower)
      );
    });
  }, [searchTerm]);

  const handleSearch = useCallback((term: string, filters?: any) => {
    setSearchTerm(term);
    if (filters) {
      setActiveFilters(filters);
    }
  }, []);

  // Column visibility management
  const COLUMN_OPTIONS = [
    { id: "sn", label: "SN" },
    { id: "logo", label: "LOGO" },
    { id: "name", label: "NAME" },
    { id: "email", label: "EMAIL" },
    { id: "contact", label: "CONTACT" },
    { id: "createdAt", label: "CREATED AT" },
    { id: "actions", label: "ACTIONS" },
  ];

  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    COLUMN_OPTIONS.map(col => col.id)
  );

  const handleColumnVisibilityChange = useCallback((columnId: string, visible: boolean) => {
    setVisibleColumns(prev =>
      visible
        ? [...prev, columnId]
        : prev.filter(id => id !== columnId)
    );
  }, []);

  // Breadcrumbs
  const breadcrumbs: IBreadcrumbDisplay[] = [
    { name: "Dashboard", path: "/super-admin/dashboard" as RoutePathEnum, forwardParam: false },
    { name: "Customer Management", path: RoutePathEnum.NONE, forwardParam: false },
  ];

  // Transform data for table
  const newApplication = useMemo(
    () =>
      filteredData.map((item) => ({
        sn: item.sn,
        logo: item.logo,
        name: item.name,
        email: item.email,
        contact: item.contact,
        createdAt: item.createdAt,
        actions: item.sn.toString(),
      })),
    [filteredData]
  );

  const tablePaginationData = useAppSelector(GET_TABLE_PAGINATION_DATA);
  const { page, limit, isLoading } = tablePaginationData;

  const contactPagination = {
    totalCount: filteredData.length,
  };

  // Pagination handlers
  const changePage = useCallback((newPage: number) => {
    console.log("Page changed to:", newPage);
  }, []);

  const changeRows = useCallback((newLimit: number) => {
    console.log("Rows per page changed to:", newLimit);
  }, []);

  // Table headers
  const headers: IHeader[] = [
    {
      id: "sn",
      name: "SN",
      hidden: false,
      width: 60,
      type: TableComponentEnum.STRING,
    },
    {
      id: "logo",
      name: "LOGO",
      hidden: false,
      width: 80,
      type: TableComponentEnum.STRING,
    },
    {
      id: "name",
      name: "NAME",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "email",
      name: "EMAIL",
      hidden: false,
      width: 200,
      type: TableComponentEnum.STRING,
    },
    {
      id: "contact",
      name: "CONTACT",
      hidden: false,
      width: 120,
      type: TableComponentEnum.STRING,
    },
    {
      id: "createdAt",
      name: "CREATED AT",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "actions",
      name: "ACTIONS",
      hidden: false,
      width: 200,
      type: TableComponentEnum.COMPONENT,
      component: ActionButtons,
    },
  ];

  return {
    getters: {
      breadcrumbs,
      headers,
      contactPagination,
      tablePaginationData,
      isLoading: false,
      newApplication,
      height,
      isMobileView,
      COLUMN_OPTIONS,
      visibleColumns,
      searchTerm,
    },
    handlers: {
      changePage,
      changeRows,
      handleSearch,
      handleEdit,
      handleDelete,
      handleAddCustomer,
      handleColumnVisibilityChange,
    },
    ref: ref as MeasureRefType,
  };
};
