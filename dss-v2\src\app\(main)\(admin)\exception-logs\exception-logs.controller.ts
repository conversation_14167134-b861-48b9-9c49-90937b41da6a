"use client";

import { useState } from "react";
import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay, IHeader, TableComponentEnum } from "@/components/common";
import { ViewButton } from "./components/viewButton";

interface IExceptionLogsController {
  getters: {
    breadcrumbs: IBreadcrumbDisplay[];
    COLUMN_OPTIONS: { label: string; value: string }[];
    initialVisibleColumns: string[];
    visibleColumns: string[];
    headers: IHeader[];
  };
  handlers: {
    handleColumnVisibilityChange: (visibleColumns: string[]) => void;
  };
}

/**
 * Exception Logs Controller
 * @return {IExceptionLogsController}
 */
export function ExceptionLogsController(): IExceptionLogsController {
  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Logs",
      path: "", // No direct path for Logs category
      forwardParam: false,
    },
    {
      name: "Exception Logs",
      path: RoutePathEnum.EXCEPTION_LOGS,
      forwardParam: false,
    },
  ];

  // Column options for the ActiveColumn component
  const COLUMN_OPTIONS = [
    { label: "SN", value: "sn" },
    { label: "User", value: "user" },
    { label: "Path", value: "path" },
    { label: "Method", value: "method" },
    { label: "Exception Type", value: "exceptionType" },
    { label: "Exception Message", value: "exceptionMessage" },
    { label: "Timestamp", value: "timestamp" },
    { label: "Traceback", value: "traceback" },
  ];

  // Initial visible columns
  const initialVisibleColumns = [
    "sn", "user", "path", "method", "exceptionType", "exceptionMessage", "timestamp", "traceback"
  ];

  // State for visible columns
  const [visibleColumns, setVisibleColumns] = useState<string[]>(initialVisibleColumns);

  // Handle column visibility changes
  const handleColumnVisibilityChange = (newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  };

  // Table headers
  const headers: IHeader[] = [
    {
      id: "sn",
      name: "SN",
      hidden: false,
      width: 60,
      type: TableComponentEnum.STRING,
    },
    {
      id: "user",
      name: "USER",
      hidden: false,
      width: 100,
      type: TableComponentEnum.STRING,
    },
    {
      id: "path",
      name: "PATH",
      hidden: false,
      width: 200,
      type: TableComponentEnum.STRING,
    },
    {
      id: "method",
      name: "METHOD",
      hidden: false,
      width: 80,
      type: TableComponentEnum.STRING,
    },
    {
      id: "exceptionType",
      name: "EXCEPTION TYPE",
      hidden: false,
      width: 120,
      type: TableComponentEnum.STRING,
    },
    {
      id: "exceptionMessage",
      name: "EXCEPTION MESSAGE",
      hidden: false,
      width: 200,
      type: TableComponentEnum.STRING,
    },
    {
      id: "timestamp",
      name: "TIMESTAMP",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "traceback",
      name: "TRACEBACK",
      hidden: false,
      width: 100,
      type: TableComponentEnum.COMPONENT,
      component: ViewButton,
    },
  ];

  return {
    getters: {
      breadcrumbs,
      COLUMN_OPTIONS,
      initialVisibleColumns,
      visibleColumns,
      headers,
    },
    handlers: {
      handleColumnVisibilityChange,
    },
  };
}
