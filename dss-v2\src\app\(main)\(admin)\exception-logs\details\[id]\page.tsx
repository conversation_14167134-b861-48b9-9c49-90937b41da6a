"use client";

import React, { use } from "react";
import {
  Box,
  Paper,
  Typography,
  Grid,
  Divider,
  Chip,
} from "@mui/material";
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PersonIcon from '@mui/icons-material/Person';
import LinkIcon from '@mui/icons-material/Link';
import CodeIcon from '@mui/icons-material/Code';
import ErrorIcon from '@mui/icons-material/Error';
import { useThemeWithToggle } from '@/context/ThemeContext';
import { PageHeader } from "@/components/common";
import { ExceptionDetailsController } from "./exception-details.controller";

export default function ExceptionDetails({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const exceptionId = unwrappedParams.id;

  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';
  const { getters } = ExceptionDetailsController(exceptionId);
  const { exceptionData, breadcrumbs, loading } = getters;

  if (loading || !exceptionData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">Loading exception details...</Typography>
      </Box>
    );
  }

  return (
    <>
      <PageHeader
        title={`Exception Details #${exceptionId}`}
        breadcrumbs={breadcrumbs}
      />

      <Paper
        elevation={0}
        sx={{
          p: 3,
          m: 3,
          borderRadius: 2,
          backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : '#ffffff',
        }}
      >
        {/* Header with exception type */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 3,
            pb: 2,
            borderBottom: '1px solid',
            borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
          }}
        >
          <ErrorOutlineIcon
            sx={{
              mr: 1.5,
              color: '#f44336',
              fontSize: '2rem'
            }}
          />
          <Typography
            variant="h5"
            component="h1"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
            }}
          >
            {exceptionData.exceptionType}
          </Typography>
          <Box sx={{ flexGrow: 1 }} />
          <Chip
            label={exceptionData.method}
            color="primary"
            size="small"
            sx={{
              fontWeight: 600,
              backgroundColor: '#3A52A6',
              mr: 1
            }}
          />
          <Chip
            icon={<AccessTimeIcon fontSize="small" />}
            label={exceptionData.timestamp}
            variant="outlined"
            size="small"
            sx={{
              fontWeight: 500,
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'
            }}
          />
        </Box>

        <Grid container spacing={3}>
          {/* Left column - Basic information */}
          <Grid item xs={12} md={5}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2,
                height: '100%'
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  mb: 2,
                  fontWeight: 600,
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                Exception Information
              </Typography>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <PersonIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>User</Typography>
                </Box>
                <Typography
                  variant="body2"
                  sx={{
                    ml: 4,
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                  }}
                >
                  {exceptionData.user}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <LinkIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>Path</Typography>
                </Box>
                <Typography
                  variant="body2"
                  sx={{
                    ml: 4,
                    wordBreak: 'break-all',
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                  }}
                >
                  {exceptionData.path}
                </Typography>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <ErrorIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>Exception Message</Typography>
                </Box>
                <Typography
                  variant="body2"
                  sx={{
                    ml: 4,
                    wordBreak: 'break-all',
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                  }}
                >
                  {exceptionData.exceptionMessage}
                </Typography>
              </Box>
            </Paper>
          </Grid>

          {/* Right column - Traceback */}
          <Grid item xs={12} md={7}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CodeIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                  }}
                >
                  Traceback
                </Typography>
              </Box>

              <Box
                sx={{
                  p: 2,
                  backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.04)',
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  lineHeight: 1.5,
                  whiteSpace: 'pre-wrap',
                  overflowX: 'auto',
                  maxHeight: '400px',
                  overflow: 'auto',
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                {exceptionData.traceback}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
    </>
  );
}
