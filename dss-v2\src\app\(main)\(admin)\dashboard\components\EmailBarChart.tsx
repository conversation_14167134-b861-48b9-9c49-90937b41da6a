import React, { useState } from 'react';
import { Box, Typography, Paper, Tooltip, Fade, useTheme } from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';

interface EmailData {
  label: string;
  value: number;
}

interface EmailBarChartProps {
  title: string;
  data: EmailData[];
}

const EmailBarChart: React.FC<EmailBarChartProps> = ({ title, data }) => {
  // Find the max value for scaling
  const maxValue = Math.max(...data.map(item => item.value));
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const theme = useTheme();

  // Calculate total emails
  const totalEmails = data.reduce((sum, item) => sum + item.value, 0);

  // Find day with highest value
  const highestDay = data.reduce((prev, current) =>
    (prev.value > current.value) ? prev : current
  );

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        minHeight: '400px',
        display: 'flex',
        flexDirection: 'column',
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.08)',
        }
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 500 }}>
          {title}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            px: 1,
            py: 0.5,
            borderRadius: '4px',
            backgroundColor: `${theme.palette.primary.main}20`,
            color: theme.palette.primary.main,
            fontSize: '0.75rem',
            fontWeight: 'bold',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EmailIcon fontSize="small" sx={{ mr: 0.5 }} />
            {totalEmails} emails
          </Box>
        </Box>
      </Box>

      {/* Chart insights */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Highest volume on <span style={{ fontWeight: 'bold', color: theme.palette.primary.main }}>{highestDay.label}</span> with <span style={{ fontWeight: 'bold', color: theme.palette.primary.main }}>{highestDay.value}</span> emails
        </Typography>
      </Box>

      {/* Simple bar chart visualization */}
      <Box sx={{ flexGrow: 1, height: '320px', mt: 1, position: 'relative' }}>
        {/* Y-axis labels */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          bottom: 30,
          width: '30px',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
          pr: 1,
        }}>
          {[0, 1, 2, 3, 4, 5].map((i) => (
            <Typography
              key={i}
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: 'text.secondary',
              }}
            >
              {Math.round(maxValue * (5 - i) / 5)}
            </Typography>
          ))}
        </Box>

        {/* Chart grid lines */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 30,
          right: 0,
          bottom: 30,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}>
          {[0, 1, 2, 3, 4, 5].map((i) => (
            <Box
              key={i}
              sx={{
                width: '100%',
                height: '1px',
                backgroundColor: '#f0f0f0',
              }}
            />
          ))}
        </Box>

        {/* Chart bars */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 30,
          right: 0,
          bottom: 30,
          display: 'flex',
          alignItems: 'flex-end',
          justifyContent: 'space-around',
          px: 2,
        }}>
          {data.map((item, index) => {
            const height = (item.value / maxValue) * 100;
            const isHovered = hoveredIndex === index;
            const isHighest = item.value === highestDay.value;

            return (
              <Tooltip
                key={index}
                title={`${item.label}: ${item.value} emails`}
                placement="top"
                arrow
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 300 }}
              >
                <Box
                  sx={{
                    width: '40px',
                    height: `${height}%`,
                    backgroundColor: isHovered
                      ? theme.palette.primary.main
                      : isHighest
                        ? `${theme.palette.primary.main}90`
                        : `${theme.palette.primary.main}60`,
                    borderRadius: '6px 6px 0 0',
                    position: 'relative',
                    transition: 'all 0.3s ease',
                    cursor: 'pointer',
                    transform: isHovered ? 'scaleY(1.05)' : 'scaleY(1)',
                    transformOrigin: 'bottom',
                    boxShadow: isHovered ? `0 -4px 8px rgba(0, 0, 0, 0.1)` : 'none',
                  }}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                  {/* X-axis label */}
                  <Typography
                    variant="caption"
                    sx={{
                      position: 'absolute',
                      bottom: -25,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      fontSize: '0.7rem',
                      color: isHovered ? theme.palette.primary.main : 'text.secondary',
                      fontWeight: isHovered || isHighest ? 'bold' : 'normal',
                      whiteSpace: 'nowrap',
                      transition: 'all 0.3s ease',
                    }}
                  >
                    {item.label}
                  </Typography>

                  {/* Value label */}
                  {(isHovered || isHighest) && (
                    <Typography
                      variant="caption"
                      sx={{
                        position: 'absolute',
                        top: -20,
                        left: '50%',
                        transform: 'translateX(-50%)',
                        backgroundColor: theme.palette.primary.main,
                        color: 'white',
                        padding: '2px 4px',
                        borderRadius: '4px',
                        fontSize: '0.7rem',
                        fontWeight: 'bold',
                      }}
                    >
                      {item.value}
                    </Typography>
                  )}
                </Box>
              </Tooltip>
            );
          })}
        </Box>
      </Box>
    </Paper>
  );
};

export default EmailBarChart;
