import React from 'react';
import { Button } from '@mui/material';
import { useRouter } from 'next/navigation';
import { RoutePathEnum } from '@/enum';

interface ViewDetailsButtonProps {
  onClick?: (value: string) => void;
  value: string;
  index: number;
}

export const ViewDetailsButton: React.FC<ViewDetailsButtonProps> = ({ onClick, value, index }) => {
  const router = useRouter();

  const handleClick = () => {
    // Navigate to the email details page with the email ID as a query parameter
    router.push(`${RoutePathEnum.PHISHING_EMAIL_DETAILS}?id=${value}`);

    // Also call the original onClick handler if provided (for any additional logic)
    if (onClick) {
      onClick(value);
    }
  };

  return (
    <Button
      onClick={handleClick}
      size="small"
      variant="contained"
      sx={{
        backgroundColor: '#1565C0',
        '&:hover': {
          backgroundColor: '#0D47A1',
        },
      }}
    >
      View
    </Button>
  );
};

export default ViewDetailsButton;
