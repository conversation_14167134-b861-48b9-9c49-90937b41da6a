import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import { ViewButton } from './viewButton';
import { CustomPagination } from '@/components/common';
import { CommonTableStyle } from '@/components/common/table/commonTableStyle';

interface ErrorLogData {
  id: string;
  user: string;
  path: string;
  method: string;
  errorType: string;
  errorMessage: string;
  timestamp: string;
  traceback: string;
}

interface ErrorLogsTableProps {
  data: ErrorLogData[];
}

export const ErrorLogsTable: React.FC<ErrorLogsTableProps> = ({ data }) => {
  const [page, setPage] = useState(1);
  const [rowsPerPage] = useState(10);
  const [inputPage, setInputPage] = useState('1');
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedTraceback, setSelectedTraceback] = useState('');

  const handleChangePage = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(data.length / rowsPerPage)) {
      setPage(newPage);
      setInputPage(newPage.toString());
    }
  };

  const handleViewTraceback = (traceback: string) => {
    setSelectedTraceback(traceback);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  // Calculate the current page data
  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentPageData = data.slice(startIndex, endIndex);

  return (
    <Box sx={{ width: '100%' }}>
      <CommonTableStyle elevation={0}>
        <div style={{ maxHeight: 'calc(100vh - 245px)', overflow: 'auto' }}>
          <Table sx={{ minWidth: 650 }} aria-label="error logs table">
            <TableHead>
              <TableRow>
                <TableCell sx={{ width: '5%' }}>SN</TableCell>
                <TableCell sx={{ width: '10%' }}>USER</TableCell>
                <TableCell sx={{ width: '20%' }}>PATH</TableCell>
                <TableCell sx={{ width: '8%' }}>METHOD</TableCell>
                <TableCell sx={{ width: '12%' }}>ERROR TYPE</TableCell>
                <TableCell sx={{ width: '20%' }}>ERROR MESSAGE</TableCell>
                <TableCell sx={{ width: '15%' }}>TIMESTAMP</TableCell>
                <TableCell sx={{ width: '10%' }}>TRACEBACK</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {currentPageData.map((row, index) => (
                <TableRow key={row.id}>
                  <TableCell>{startIndex + index + 1}</TableCell>
                  <TableCell>{row.user}</TableCell>
                  <TableCell sx={{ wordBreak: 'break-all' }}>{row.path}</TableCell>
                  <TableCell>{row.method}</TableCell>
                  <TableCell>{row.errorType}</TableCell>
                  <TableCell sx={{ wordBreak: 'break-all' }}>{row.errorMessage}</TableCell>
                  <TableCell>{row.timestamp}</TableCell>
                  <TableCell>
                    <ViewButton onClick={() => handleViewTraceback(row.traceback)} />
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CommonTableStyle>

      {/* Pagination */}
      <CustomPagination
        page={page}
        limit={rowsPerPage}
        total={data.length}
        onPageChange={handleChangePage}
      />

      {/* Traceback Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Error Traceback</DialogTitle>
        <DialogContent>
          <Box
            sx={{
              backgroundColor: '#f5f5f5',
              p: 2,
              borderRadius: 1,
              fontFamily: 'monospace',
              whiteSpace: 'pre-wrap',
              overflowX: 'auto',
            }}
          >
            {selectedTraceback}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ErrorLogsTable;
