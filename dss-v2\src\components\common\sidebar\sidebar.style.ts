import { styled } from "@mui/material";
import { List, ListItem, ListItemIcon, ListItemText, Badge, Tooltip, Box, Typography, Divider } from "@mui/material";

export const SidebarContainer = styled("aside")(({ theme }) => ({
  width: "240px",
  height: "calc(100vh - 64px)",
  position: "fixed",
  top: "64px",
  left: 0,
  backgroundColor: theme.palette.background.paper, // Keep white background
  color: theme.palette.text.primary,
  borderRight: `1px solid ${theme.palette.divider}`,
  overflowY: "auto",
  overflowX: "hidden",
  // No transition by default for better performance during normal use
  transition: "none",
  transform: "translateZ(0) translateX(0)", // Force hardware acceleration
  backfaceVisibility: "hidden", // Optimize rendering
  zIndex: theme.zIndex.drawer,
  boxShadow: "0 0 10px rgba(0,0,0,0.05)",
  // Only apply transitions when the with-transition class is present
  "&.with-transition": {
    transition: "all 0.45s cubic-bezier(0.25, 1, 0.5, 1)", // Improved easing for smoother motion
    willChange: "width, opacity, transform", // Hint to browser for optimization
  },
  // Add subtle opacity transition for smoother appearance
  "&.with-transition:not(.icon-only)": {
    opacity: 1,
  },
  "&.with-transition.icon-only": {
    opacity: 0.97, // Subtle opacity change for smoother transition
  },
  "&.icon-only": {
    width: "64px",
    "& .MuiListItemIcon-root": {
      transform: "translateX(0)", // Reset icon position when collapsed
    },
  },
  // Add sliding animation for content inside sidebar
  "& .MuiListItemIcon-root": {
    transform: "translateX(0)",
    transition: "none",
  },
  "&.with-transition .MuiListItemIcon-root": {
    transition: "transform 0.45s cubic-bezier(0.25, 1, 0.5, 1), color 0.3s ease", // Match main transition
  },
  "&.with-transition.icon-only .MuiListItemIcon-root": {
    transform: "translateX(8px)", // Slide icons slightly when collapsed
  },
  // Add subtle scale effect to icons during transition
  "&.with-transition:not(.icon-only) .MuiListItemIcon-root svg": {
    transform: "scale(1)",
    transition: "transform 0.45s cubic-bezier(0.25, 1, 0.5, 1)",
  },
  "&.with-transition.icon-only .MuiListItemIcon-root svg": {
    transform: "scale(1.1)", // Slightly enlarge icons when collapsed
    transition: "transform 0.45s cubic-bezier(0.25, 1, 0.5, 1)",
  },
  // Custom scrollbar
  "&::-webkit-scrollbar": {
    width: "6px",
  },
  "&::-webkit-scrollbar-track": {
    background: "transparent",
  },
  "&::-webkit-scrollbar-thumb": {
    background: theme.palette.mode === 'light'
      ? "rgba(255, 255, 255, 0.3)"
      : "rgba(255, 255, 255, 0.2)",
    borderRadius: "3px",
  },
  "&::-webkit-scrollbar-thumb:hover": {
    background: theme.palette.mode === 'light'
      ? "rgba(255, 255, 255, 0.5)"
      : "rgba(255, 255, 255, 0.3)",
  },
}));

export const SidebarHeader = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  borderBottom: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(1),
  ".icon-only &": {
    justifyContent: "center",
    padding: theme.spacing(1, 0),
  },
}));

export const LogoText = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: "1.2rem",
  color: theme.palette.text.primary,
  marginLeft: theme.spacing(1),
  ".icon-only &": {
    display: "none",
  },
}));

export const SidebarContent = styled("div")(({ theme }) => ({
  padding: theme.spacing(1, 0),
}));

export const SidebarSection = styled("div")(({ theme }) => ({
  marginBottom: theme.spacing(1),
}));

export const SectionDivider = styled(Divider)(({ theme }) => ({
  margin: theme.spacing(1, 2),
  backgroundColor: theme.palette.divider,
  ".icon-only &": {
    margin: theme.spacing(1, 0.5),
  },
}));

export const StyledListItem = styled(ListItem)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  transition: "background-color 0.15s ease", // Only transition what's necessary
  position: "relative",
  margin: theme.spacing(0.5, 0.75),
  borderRadius: "8px",
  color: theme.palette.text.primary,
  transform: "translateX(0)",
  ".with-transition &": {
    transition: "transform 0.45s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.3s ease, color 0.3s ease",
  },
  ".with-transition.icon-only &": {
    transform: "translateX(-8px)", // Slide items when sidebar collapses
  },
  // Add staggered animation for list items
  "&:nth-of-type(1)": { transitionDelay: "0.02s" },
  "&:nth-of-type(2)": { transitionDelay: "0.04s" },
  "&:nth-of-type(3)": { transitionDelay: "0.06s" },
  "&:nth-of-type(4)": { transitionDelay: "0.08s" },
  "&:nth-of-type(5)": { transitionDelay: "0.1s" },
  "&:nth-of-type(6)": { transitionDelay: "0.12s" },
  "&:nth-of-type(7)": { transitionDelay: "0.14s" },
  "&:nth-of-type(8)": { transitionDelay: "0.16s" },
  "&:hover": {
    backgroundColor: theme.palette.action.hover,
    "& .MuiListItemIcon-root": {
      color: theme.palette.primary.main, // Use color change instead of transform
    },
  },
  "&.primary-item": {
    "& .MuiTypography-root": {
      fontWeight: 500,
    },
  },
  "&.selected": {
    backgroundColor: theme.palette.primary.main + "15",
    color: theme.palette.primary.main,
    fontWeight: 600,
    "&::before": {
      content: '""',
      position: "absolute",
      left: 0,
      top: 0,
      bottom: 0,
      width: "4px",
      backgroundColor: theme.palette.primary.main,
      borderRadius: "0 4px 4px 0",
    },
  },
  ".icon-only &": {
    padding: theme.spacing(1.5, 0),
    justifyContent: "center",
    margin: theme.spacing(0.5, 0.5),
    "&.selected::before": {
      width: "3px",
    },
  },
}));

export const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({
  minWidth: "40px",
  // Remove the hardcoded color to allow custom colors to work
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  transition: "color 0.15s ease", // Change from transform to color for better performance
  "& svg": {
    fontSize: "1.1rem", // Smaller icons
  },
}));

export const StyledBadge = styled(Badge)(({ theme }) => ({
  "& .MuiBadge-badge": {
    backgroundColor: theme.palette.error.main,
    color: "#fff",
    fontWeight: "bold",
    fontSize: "0.7rem",
  },
}));

// Hide expand/collapse arrows in icon-only mode
export const ExpandIconStyle = styled("span")(({ theme }) => ({
  color: theme.palette.mode === 'dark' ? '#FFFFFF' : theme.palette.text.secondary,
  transition: "transform 0.3s ease, color 0.3s ease",
  ".open &": {
    transform: "rotate(180deg)",
  },
  ".icon-only &": {
    display: "none",
  },
}));

export const StyledListItemText = styled(ListItemText)(({ theme }) => ({
  "& .MuiTypography-root": {
    fontSize: "0.9rem",
    fontWeight: 500,
  },
  ".icon-only &": {
    display: "none",
  },
}));

export const NestedList = styled(List)(({ theme }) => ({
  paddingLeft: theme.spacing(2),
  ".icon-only &": {
    display: "none",
  },
}));

export const SidebarFooter = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.5),
  borderTop: `1px solid ${theme.palette.divider}`,
  marginTop: "auto",
  fontSize: "0.75rem",
  color: theme.palette.mode === 'dark' ? '#FFFFFF' : theme.palette.text.secondary,
  textAlign: "center",
  ".icon-only &": {
    padding: theme.spacing(1),
    "& > span": {
      display: "none",
    },
  },
}));
