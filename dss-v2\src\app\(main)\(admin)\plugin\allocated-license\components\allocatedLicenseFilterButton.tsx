"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import AssignmentIcon from '@mui/icons-material/Assignment';

interface AllocatedLicenseFilterButtonProps {
  filterOptions: { label: string; value: string; type?: 'text' | 'date' | 'email' | 'number' }[];
  onApplyFilters: (filters: string[]) => void;
  searchTerm: string;
}

const AllocatedLicenseFilterButton: React.FC<AllocatedLicenseFilterButtonProps> = ({
  filterOptions,
  onApplyFilters,
  searchTerm
}) => {

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Allocated License Filters"
      title="Allocated License Filters"
      description="Apply filters to refine your allocated license search results"
      icon={<AssignmentIcon />}
      searchTerm={searchTerm}
    />
  );
};

export default AllocatedLicenseFilterButton;
