"use client";

import React, { JSX, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Box, Button } from "@mui/material";

import { CustomersController } from "./customers.controller";
import { PageHeader, TableComponent } from "@/components/common";
import { SearchBar } from "@/components/common/searchBar";
import { ActiveColumn } from "@/components/common/activeColumn";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";

/**
 * @page {Customers} - Display Customer Management Information
 * @return {JSX.Element}
 */
export default function Customers(): JSX.Element {
  const router = useRouter();
  const { getters, handlers, ref } = CustomersController();
  const {
    headers,
    tablePaginationData,
    newApplication,
    contactPagination,
    height,
    breadcrumbs,
    COLUMN_OPTIONS,
    visibleColumns,
    searchTerm,
  } = getters;
  const {
    changePage,
    changeRows,
    handleSearch,
    handleEdit,
    handleDelete,
    handleAddCustomer,
    handleColumnVisibilityChange,
  } = handlers;

  const table = useMemo(
    () => (
      <SuppressHydrationWarning>
        <TableComponent<any>
          isLoading={false}
          headerField={headers}
          tableBody={newApplication}
          paginationData={{
            onPageChange: changePage,
            onRowsPerPageChange: changeRows,
            total: contactPagination.totalCount,
            page: tablePaginationData.page,
            limit: tablePaginationData.limit,
          }}
          translation={{
            noDataTitle: "No Customers Found",
          }}
          maxHeight={height}
          onRowClick={() => {}}
          visibleColumns={visibleColumns}
          componentProps={{
            actions: {
              onEdit: handleEdit,
              onDelete: handleDelete,
            },
          }}
        />
      </SuppressHydrationWarning>
    ),
    [
      tablePaginationData.page,
      tablePaginationData.limit,
      headers,
      newApplication,
      changePage,
      changeRows,
      contactPagination.totalCount,
      height,
      handleEdit,
      handleDelete,
      visibleColumns,
    ]
  );

  const FILTER_OPTIONS = [
    { label: "Name", value: "name" },
    { label: "Email", value: "email" },
    { label: "Contact", value: "contact" },
    { label: "Created At", value: "createdAt" },
  ];

  const addCustomerButton = useMemo(() => (
    <Button
      variant="contained"
      onClick={handleAddCustomer}
      size="small"
      sx={{
        bgcolor: '#3A52A6',
        '&:hover': {
          bgcolor: '#2A3F8F',
        },
        padding: '6px 16px',
        fontWeight: 500,
      }}
    >
      Add Customer
    </Button>
  ), [handleAddCustomer]);

  const customSearchBar = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={handleSearch}
          placeholder="Search customers..."
          actionButton={addCustomerButton}
          filterOptions={FILTER_OPTIONS}
          useAdvancedFilter={false}
        />
        <ActiveColumn
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          variant="popover"
        />
      </Box>
    );
  }, [handleSearch, addCustomerButton, FILTER_OPTIONS, COLUMN_OPTIONS, visibleColumns, handleColumnVisibilityChange, searchTerm]);

  const header = useMemo(
    () => (
      <div ref={ref}>
        <SuppressHydrationWarning>
          <PageHeader
            title="Customers"
            breadcrumbs={breadcrumbs}
            actions={customSearchBar}
          />
        </SuppressHydrationWarning>
      </div>
    ),
    [breadcrumbs, ref, customSearchBar]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
}