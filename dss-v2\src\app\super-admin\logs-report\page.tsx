"use client";

import React from "react";
import { Box, Typography, Paper, useTheme } from "@mui/material";
import { PageHeader } from "@/components/common/pageHeader";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";

const LogsReportPage = () => {
  const theme = useTheme();

  return (
    <div>
      <SuppressHydrationWarning>
        <PageHeader
          title="All Logs Report"
          breadcrumbs={[
            { name: "Dashboard", path: "/dashboard", forwardParam: false },
            { name: "Super Admin", path: "/super-admin/dashboard", forwardParam: false },
            { name: "All Logs Report", path: "/super-admin/logs-report", forwardParam: false },
          ]}
        />
      </SuppressHydrationWarning>

      <Paper
        elevation={0}
        sx={{
          mt: 3,
          p: 3,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Typography variant="h6" gutterBottom>
          All Logs Report
        </Typography>
        <Typography variant="body2" color="text.secondary">
          This page will display comprehensive logs and reports across the system.
        </Typography>
      </Paper>
    </div>
  );
};

export default LogsReportPage;
