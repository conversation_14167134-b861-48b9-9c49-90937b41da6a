"use client";

import React, { useState, useMemo } from 'react';
import { Box } from '@mui/material';
import { PageHeader } from '@/components/common/pageHeader/pageHeader';
import { ErrorLogsController } from './error-logs.controller';
import { ErrorLogsTable } from './components/errorLogsTable';
import { SearchBar } from './components/searchBar';
import ErrorLogsFilterButton from './components/errorLogsFilterButton';
import { useColumnVisibility } from '@/hooks';

// Mock data for the error logs table
const mockErrorLogsData = [
  {
    id: '1',
    user: 'Anonymous',
    path: '/media/uploads/customerLogo/IAF_logo_yGO4k7F.jpg',
    method: 'GET',
    errorType: 'Http404',
    errorMessage: 'Not Found',
    timestamp: '14-05-2025 11:55 AM',
    traceback: `HTTP Error`
  },
  {
    id: '2',
    user: 'Anonymous',
    path: '/login-without-otp/',
    method: 'POST',
    errorType: 'HTTP 401',
    errorMessage: 'Invalid credentials',
    timestamp: '30-04-2025 12:53 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/decorators/csrf.py", line 54, in wrapped_view
    return view_func(*args, **kwargs)
  File "/app/rest_framework/views.py", line 505, in dispatch
    response = self.handle_exception(exc)
  File "/app/rest_framework/views.py", line 465, in handle_exception
    self.raise_uncaught_exception(exc)
  File "/app/rest_framework/views.py", line 476, in raise_uncaught_exception
    raise exc
  File "/app/rest_framework/views.py", line 502, in dispatch
    response = handler(request, *args, **kwargs)
  File "/app/rest_framework/decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "/app/api/views.py", line 123, in login
    raise AuthenticationFailed('Invalid credentials')
rest_framework.exceptions.AuthenticationFailed: Invalid credentials`,
  },
  {
    id: '3',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:47 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '4',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:47 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '5',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:34 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '6',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:34 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '7',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:34 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '8',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:34 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '9',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:23 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '10',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:23 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
  {
    id: '11',
    user: 'Anonymous',
    path: '/media/uploads/custo...',
    method: 'GET',
    errorType: 'HTTP 404',
    errorMessage: 'Not Found',
    timestamp: '30-04-2025 12:15 PM',
    traceback: `Traceback (most recent call last):
  File "/app/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
  File "/app/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "/app/django/views/static.py", line 87, in serve
    return static.serve(request, path, document_root=document_root, show_indexes=show_indexes)
  File "/app/django/views/static.py", line 59, in serve
    raise Http404(_('"%(path)s" does not exist') % {'path': fullpath})
django.http.Http404: "/app/media/uploads/customer/..." does not exist`,
  },
];

const Page = () => {
  const { getters } = ErrorLogsController();
  const { breadcrumbs } = getters;
  const [filteredData, setFilteredData] = useState(mockErrorLogsData);
  const [query, setQuery] = useState("");

  // Define the initial columns that should be visible
  const initialVisibleColumns = [
    'sn', 'user', 'path', 'method', 'errorType', 'errorMessage', 'timestamp', 'traceback'
  ];

  // State to track which columns are visible
  const [visibleColumns, setVisibleColumns] = useState(initialVisibleColumns);

  const handleSearch = (query: string, filters: string[]) => {
    setQuery(query);

    if (!query) {
      setFilteredData(mockErrorLogsData);
      return;
    }

    const lowercasedQuery = query.toLowerCase();
    const filtered = mockErrorLogsData.filter((item) => {
      if (filters.length === 0) return false;

      return filters.some(filter => {
        const value = item[filter as keyof typeof item];
        return value?.toString().toLowerCase().includes(lowercasedQuery);
      });
    });
    setFilteredData(filtered);
  };

  // Export functionality removed

  // Handle column visibility changes
  const handleColumnVisibilityChange = (newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  };

  const header = useMemo(
    () => (
      <div>
        <PageHeader
          title="Error Logs"
          breadcrumbs={breadcrumbs}
          actions={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SearchBar
                onSearch={handleSearch}
                placeholder="Search error logs..."
                onColumnVisibilityChange={handleColumnVisibilityChange}
                initialVisibleColumns={initialVisibleColumns}
              />
              <ErrorLogsFilterButton
                onApplyFilters={(filters) => handleSearch(query, filters)}
                searchTerm={query}
              />
            </Box>
          }
        />
      </div>
    ),
    [breadcrumbs, handleSearch, handleColumnVisibilityChange, initialVisibleColumns, query]
  );

  const table = useMemo(
    () => (
      <Box sx={{ mt: 3, px: 3 }}>
        <ErrorLogsTable
          data={filteredData}
          visibleColumns={visibleColumns}
        />
      </Box>
    ),
    [filteredData, visibleColumns]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
};

export default Page;
