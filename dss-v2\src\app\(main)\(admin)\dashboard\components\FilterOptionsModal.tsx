"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  TextField,
  InputAdornment,
  useTheme,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import { useThemeWithToggle } from '@/context/ThemeContext';
import { DateTime } from "luxon";

interface FilterOptionsModalProps {
  open: boolean;
  onClose: () => void;
  onApply: (startDate: DateTime | null, endDate: DateTime | null) => void;
  initialStartDate?: DateTime | null;
  initialEndDate?: DateTime | null;
}

const FilterOptionsModal: React.FC<FilterOptionsModalProps> = ({
  open,
  onClose,
  onApply,
  initialStartDate = null,
  initialEndDate = null,
}) => {
  const [startDate, setStartDate] = useState<string>(
    initialStartDate ? initialStartDate.toFormat("dd-MM-yyyy") : ""
  );
  const [endDate, setEndDate] = useState<string>(
    initialEndDate ? initialEndDate.toFormat("dd-MM-yyyy") : ""
  );
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';
  const theme = useTheme();

  // State for date picker popup
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth());

  const handleApply = () => {
    // Parse dates and validate
    let parsedStartDate: DateTime | null = null;
    let parsedEndDate: DateTime | null = null;

    if (startDate) {
      try {
        const [day, month, year] = startDate.split("-").map(Number);
        parsedStartDate = DateTime.fromObject({ day, month, year });
        if (!parsedStartDate.isValid) parsedStartDate = null;
      } catch (error) {
        parsedStartDate = null;
      }
    }

    if (endDate) {
      try {
        const [day, month, year] = endDate.split("-").map(Number);
        parsedEndDate = DateTime.fromObject({ day, month, year });
        if (!parsedEndDate.isValid) parsedEndDate = null;
      } catch (error) {
        parsedEndDate = null;
      }
    }

    onApply(parsedStartDate, parsedEndDate);
  };

  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setStartDate(e.target.value);
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEndDate(e.target.value);
  };

  const toggleStartDatePicker = () => {
    setShowStartDatePicker(!showStartDatePicker);
    if (showEndDatePicker) setShowEndDatePicker(false);
  };

  const toggleEndDatePicker = () => {
    setShowEndDatePicker(!showEndDatePicker);
    if (showStartDatePicker) setShowStartDatePicker(false);
  };

  const handleDateSelect = (isStartDate: boolean, day: number) => {
    const date = DateTime.fromObject({
      day,
      month: selectedMonth + 1,
      year: selectedYear
    });

    if (isStartDate) {
      setStartDate(date.toFormat("dd-MM-yyyy"));
      setShowStartDatePicker(false);
    } else {
      setEndDate(date.toFormat("dd-MM-yyyy"));
      setShowEndDatePicker(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xs"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.98)",
          backdropFilter: "blur(10px)",
          boxShadow: isDarkMode
            ? "0 8px 32px rgba(0, 0, 0, 0.4)"
            : "0 8px 32px rgba(0, 0, 0, 0.15)",
          overflow: 'hidden',
          border: isDarkMode ? '1px solid rgba(58, 82, 166, 0.3)' : 'none',
        }
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          backgroundColor: "#3A52A6",
          color: "white",
          py: 1.5,
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '5px',
            background: `linear-gradient(90deg, #3A52A6 0%, #4F6AE5 100%)`,
            boxShadow: `0 1px 8px rgba(58, 82, 166, 0.4)`,
          }
        }}
      >
        <Typography variant="h6" component="div" sx={{
          fontWeight: 600,
          letterSpacing: '0.01em',
        }}>
          Filter Options
        </Typography>
        <IconButton
          edge="end"
          color="inherit"
          onClick={onClose}
          aria-label="close"
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.2)',
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ pt: 3, pb: 3, px: 3 }}>
        <Typography
          variant="body2"
          color={isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'text.secondary'}
          sx={{
            mb: 2.5,
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            '&::before': {
              content: '""',
              display: 'inline-block',
              width: 4,
              height: 4,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              mr: 1,
            }
          }}
        >
          Apply filters to refine your search results
        </Typography>

        <Box sx={{ mb: 3, position: 'relative' }}>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
            }}
          >
            Start Date
          </Typography>
          <TextField
            fullWidth
            placeholder="dd-mm-yyyy"
            value={startDate}
            onChange={handleStartDateChange}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={toggleStartDatePicker}
                    edge="end"
                    sx={{
                      color: theme.palette.primary.main,
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                      '&:hover': {
                        backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                      }
                    }}
                  >
                    <CalendarTodayIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            size="small"
            sx={{
              '& .MuiInputBase-root': {
                backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          {showStartDatePicker && (
            <Box
              sx={{
                position: 'absolute',
                top: '100%',
                left: 0,
                right: 0,
                mt: 1,
                p: 2,
                backgroundColor: isDarkMode ? 'rgba(30, 30, 30, 0.95)' : 'rgba(255, 255, 255, 0.95)',
                borderRadius: 1,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                zIndex: 10,
                border: '1px solid',
                borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
              }}
            >
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mb: 1.5,
                pb: 1,
                borderBottom: isDarkMode
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : '1px solid rgba(0, 0, 0, 0.05)',
              }}>
                <Button
                  size="small"
                  onClick={() => setSelectedYear(selectedYear - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &lt;
                </Button>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {selectedYear}
                </Typography>
                <Button
                  size="small"
                  onClick={() => setSelectedYear(selectedYear + 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &gt;
                </Button>
              </Box>

              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mb: 1.5,
                pb: 1,
                borderBottom: isDarkMode
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : '1px solid rgba(0, 0, 0, 0.05)',
              }}>
                <Button
                  size="small"
                  onClick={() => setSelectedMonth(selectedMonth > 0 ? selectedMonth - 1 : 11)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &lt;
                </Button>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {new Date(selectedYear, selectedMonth, 1).toLocaleString('default', { month: 'long' })}
                </Typography>
                <Button
                  size="small"
                  onClick={() => setSelectedMonth(selectedMonth < 11 ? selectedMonth + 1 : 0)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &gt;
                </Button>
              </Box>

              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 0.5 }}>
                {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                  <Typography
                    key={day}
                    variant="caption"
                    sx={{
                      textAlign: 'center',
                      fontWeight: 'bold',
                      color: day === 'Su' || day === 'Sa'
                        ? (isDarkMode ? 'rgba(255, 100, 100, 0.8)' : '#d32f2f')
                        : (isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'),
                      py: 0.5
                    }}
                  >
                    {day}
                  </Typography>
                ))}

                {Array.from({ length: new Date(selectedYear, selectedMonth, 1).getDay() }, (_, i) => (
                  <Box key={`empty-${i}`} />
                ))}

                {Array.from(
                  { length: new Date(selectedYear, selectedMonth + 1, 0).getDate() },
                  (_, i) => i + 1
                ).map((day) => {
                  const isToday = new Date().getDate() === day &&
                                  new Date().getMonth() === selectedMonth &&
                                  new Date().getFullYear() === selectedYear;
                  return (
                    <Button
                      key={`day-${day}`}
                      onClick={() => handleDateSelect(true, day)}
                      size="small"
                      variant="text"
                      sx={{
                        minWidth: 0,
                        p: 0.5,
                        borderRadius: '50%',
                        color: isToday
                          ? 'white'
                          : (isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'),
                        backgroundColor: isToday
                          ? theme.palette.primary.main
                          : 'transparent',
                        fontWeight: isToday ? 'bold' : 'normal',
                        border: isToday
                          ? 'none'
                          : 'none',
                        '&:hover': {
                          backgroundColor: isToday
                            ? theme.palette.primary.dark
                            : (isDarkMode ? 'rgba(58, 82, 166, 0.3)' : 'rgba(58, 82, 166, 0.1)'),
                        }
                      }}
                    >
                      {day}
                    </Button>
                  );
                })}
              </Box>
            </Box>
          )}
        </Box>

        <Box sx={{ position: 'relative' }}>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1,
              fontWeight: 600,
              color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
            }}
          >
            End Date
          </Typography>
          <TextField
            fullWidth
            placeholder="dd-mm-yyyy"
            value={endDate}
            onChange={handleEndDateChange}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={toggleEndDatePicker}
                    edge="end"
                    sx={{
                      color: theme.palette.primary.main,
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                      '&:hover': {
                        backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                      }
                    }}
                  >
                    <CalendarTodayIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            size="small"
            sx={{
              '& .MuiInputBase-root': {
                backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.8)',
              }
            }}
          />

          {showEndDatePicker && (
            <Box
              sx={{
                position: 'absolute',
                top: '100%',
                left: 0,
                right: 0,
                mt: 1,
                p: 2,
                backgroundColor: isDarkMode ? 'rgba(30, 30, 30, 0.95)' : 'rgba(255, 255, 255, 0.95)',
                borderRadius: 1,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                zIndex: 10,
                border: '1px solid',
                borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'
              }}
            >
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mb: 1.5,
                pb: 1,
                borderBottom: isDarkMode
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : '1px solid rgba(0, 0, 0, 0.05)',
              }}>
                <Button
                  size="small"
                  onClick={() => setSelectedYear(selectedYear - 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &lt;
                </Button>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {selectedYear}
                </Typography>
                <Button
                  size="small"
                  onClick={() => setSelectedYear(selectedYear + 1)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &gt;
                </Button>
              </Box>

              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mb: 1.5,
                pb: 1,
                borderBottom: isDarkMode
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : '1px solid rgba(0, 0, 0, 0.05)',
              }}>
                <Button
                  size="small"
                  onClick={() => setSelectedMonth(selectedMonth > 0 ? selectedMonth - 1 : 11)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &lt;
                </Button>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? theme.palette.primary.light : theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {new Date(selectedYear, selectedMonth, 1).toLocaleString('default', { month: 'long' })}
                </Typography>
                <Button
                  size="small"
                  onClick={() => setSelectedMonth(selectedMonth < 11 ? selectedMonth + 1 : 0)}
                  sx={{
                    minWidth: 32,
                    height: 32,
                    borderRadius: '8px',
                    color: theme.palette.primary.main,
                    backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.15)' : 'rgba(58, 82, 166, 0.08)',
                    '&:hover': {
                      backgroundColor: isDarkMode ? 'rgba(58, 82, 166, 0.25)' : 'rgba(58, 82, 166, 0.15)',
                    }
                  }}
                >
                  &gt;
                </Button>
              </Box>

              <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 0.5 }}>
                {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                  <Typography
                    key={day}
                    variant="caption"
                    sx={{
                      textAlign: 'center',
                      fontWeight: 'bold',
                      color: day === 'Su' || day === 'Sa'
                        ? (isDarkMode ? 'rgba(255, 100, 100, 0.8)' : '#d32f2f')
                        : (isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'),
                      py: 0.5
                    }}
                  >
                    {day}
                  </Typography>
                ))}

                {Array.from({ length: new Date(selectedYear, selectedMonth, 1).getDay() }, (_, i) => (
                  <Box key={`empty-${i}`} />
                ))}

                {Array.from(
                  { length: new Date(selectedYear, selectedMonth + 1, 0).getDate() },
                  (_, i) => i + 1
                ).map((day) => {
                  const isToday = new Date().getDate() === day &&
                                  new Date().getMonth() === selectedMonth &&
                                  new Date().getFullYear() === selectedYear;
                  return (
                    <Button
                      key={`day-${day}`}
                      onClick={() => handleDateSelect(false, day)}
                      size="small"
                      variant="text"
                      sx={{
                        minWidth: 0,
                        p: 0.5,
                        borderRadius: '50%',
                        color: isToday
                          ? 'white'
                          : (isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.8)'),
                        backgroundColor: isToday
                          ? theme.palette.primary.main
                          : 'transparent',
                        fontWeight: isToday ? 'bold' : 'normal',
                        border: isToday
                          ? 'none'
                          : 'none',
                        '&:hover': {
                          backgroundColor: isToday
                            ? theme.palette.primary.dark
                            : (isDarkMode ? 'rgba(58, 82, 166, 0.3)' : 'rgba(58, 82, 166, 0.1)'),
                        }
                      }}
                    >
                      {day}
                    </Button>
                  );
                })}
              </Box>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions
        sx={{
          px: 3,
          py: 2.5,
          justifyContent: 'space-between',
          borderTop: '1px solid rgba(0, 0, 0, 0.1)',
        }}
      >
        <Button
          onClick={onClose}
          sx={{
            color: isDarkMode ? theme.palette.grey[300] : theme.palette.grey[700],
            '&:hover': {
              backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'
            }
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleApply}
          variant="contained"
          sx={{
            backgroundColor: '#3A52A6',
            '&:hover': {
              backgroundColor: '#2A3F8F'
            }
          }}
        >
          Apply
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default FilterOptionsModal;
