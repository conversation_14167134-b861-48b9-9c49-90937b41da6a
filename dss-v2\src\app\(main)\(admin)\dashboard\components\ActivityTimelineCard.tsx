import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Avatar,
  Chip,
  Divider,
  Button,
  useTheme
} from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

// Activity types and their colors
export type ActivityType = 'login' | 'report' | 'alert' | 'update' | 'action';

interface TimelineActivity {
  id: string | number;
  type: ActivityType;
  title: string;
  description: string;
  user?: string;
  userAvatar?: string;
  time: string;
  status?: 'success' | 'warning' | 'error' | 'info';
}

interface ActivityTimelineCardProps {
  title: string;
  activities: TimelineActivity[];
  maxItems?: number;
  onViewAll?: () => void;
  isRefreshing?: boolean;
}

const getActivityColor = (type: ActivityType, theme: any): string => {
  const colors = {
    login: theme.palette.info.main,
    report: theme.palette.success.main,
    alert: theme.palette.error.main,
    update: theme.palette.warning.main,
    action: theme.palette.primary.main,
  };
  return colors[type];
};

const getStatusColor = (status: string | undefined, theme: any): string => {
  if (!status) return theme.palette.text.secondary;

  const colors = {
    success: theme.palette.success.main,
    warning: theme.palette.warning.main,
    error: theme.palette.error.main,
    info: theme.palette.info.main,
  };
  return colors[status as keyof typeof colors] || theme.palette.text.secondary;
};

const ActivityTimelineCard: React.FC<ActivityTimelineCardProps> = ({
  title,
  activities,
  maxItems = 5,
  onViewAll,
  isRefreshing = false,
}) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';
  const displayActivities = activities.slice(0, maxItems);

  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: isDarkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid #e0e0e0',
        borderRadius: '12px',
        overflow: 'hidden',
        position: 'relative',
        transition: 'all 0.3s ease',
        backgroundColor: isDarkMode ? 'rgba(0,0,0,0.2)' : 'background.paper',
        transform: isRefreshing ? 'scale(0.98)' : 'scale(1)',
        opacity: isRefreshing ? 0.8 : 1,
        '&:hover': {
          boxShadow: isDarkMode
            ? '0 8px 24px -4px rgba(0,0,0,0.3), 0 0 10px rgba(255,255,255,0.1)'
            : '0 12px 24px -4px rgba(0,0,0,0.1)',
          transform: isRefreshing ? 'scale(0.98)' : 'translateY(-4px)',
        },
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '5px',
          background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
          boxShadow: `0 1px 8px ${theme.palette.primary.main}40`,
        }
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 2.5,
          pb: 2,
          borderBottom: isDarkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid #f0f0f0',
          background: isDarkMode ? 'transparent' : `linear-gradient(to bottom, ${theme.palette.primary.main}08 0%, transparent 100%)`,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              width: 4,
              height: 20,
              backgroundColor: theme.palette.primary.main,
              borderRadius: 1,
              mr: 1.5
            }}
          />
          <Typography
            variant="subtitle1"
            fontWeight="bold"
            sx={{
              color: isDarkMode ? theme.palette.primary.main : 'text.primary',
              letterSpacing: '0.01em',
            }}
          >
            {title}
          </Typography>
        </Box>

        <Chip
          label={`${activities.length} activities`}
          size="small"
          sx={{
            backgroundColor: isDarkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.04)',
            color: 'text.secondary',
            fontWeight: 'medium',
            borderRadius: '12px',
          }}
        />
      </Box>

      <Box sx={{ p: 2, flexGrow: 1, overflow: 'auto' }}>
        {displayActivities.map((activity, index) => (
          <Box key={activity.id}>
            <Box sx={{ display: 'flex', mb: index === displayActivities.length - 1 ? 0 : 3 }}>
              {/* Timeline dot and line */}
              <Box sx={{ position: 'relative', mr: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                <Avatar
                  sx={{
                    width: 36,
                    height: 36,
                    bgcolor: `${getActivityColor(activity.type, theme)}20`,
                    color: getActivityColor(activity.type, theme),
                    fontSize: '0.875rem',
                    fontWeight: 'bold',
                    border: `2px solid ${getActivityColor(activity.type, theme)}`,
                    boxShadow: `0 0 0 4px ${getActivityColor(activity.type, theme)}10`,
                  }}
                >
                  {activity.type.charAt(0).toUpperCase()}
                </Avatar>

                {index !== displayActivities.length - 1 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 36,
                      bottom: -24,
                      width: 2,
                      bgcolor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.06)',
                      left: '50%',
                      transform: 'translateX(-50%)',
                    }}
                  />
                )}
              </Box>

              {/* Activity content */}
              <Box sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 0.5 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: 'bold',
                      color: getActivityColor(activity.type, theme)
                    }}
                  >
                    {activity.title}
                  </Typography>

                  <Typography
                    variant="caption"
                    sx={{
                      color: 'text.secondary',
                      backgroundColor: isDarkMode ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.04)',
                      px: 1,
                      py: 0.5,
                      borderRadius: 10,
                      ml: 1,
                    }}
                  >
                    {activity.time}
                  </Typography>
                </Box>

                <Typography
                  variant="body2"
                  sx={{
                    color: 'text.secondary',
                    mb: 1,
                  }}
                >
                  {activity.description}
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  {activity.user && (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Avatar
                        src={activity.userAvatar}
                        sx={{
                          width: 24,
                          height: 24,
                          mr: 1,
                          fontSize: '0.75rem',
                          bgcolor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                        }}
                      >
                        {activity.user.charAt(0).toUpperCase()}
                      </Avatar>
                      <Typography variant="caption" color="text.secondary">
                        {activity.user}
                      </Typography>
                    </Box>
                  )}

                  {activity.status && (
                    <Chip
                      label={activity.status.toUpperCase()}
                      size="small"
                      sx={{
                        height: 20,
                        fontSize: '0.625rem',
                        backgroundColor: `${getStatusColor(activity.status, theme)}15`,
                        color: getStatusColor(activity.status, theme),
                        fontWeight: 'bold',
                        border: `1px solid ${getStatusColor(activity.status, theme)}30`,
                      }}
                    />
                  )}
                </Box>
              </Box>
            </Box>

            {index !== displayActivities.length - 1 && (
              <Box sx={{ ml: 5, my: 1 }}>
                <Divider />
              </Box>
            )}
          </Box>
        ))}
      </Box>

      {onViewAll && (
        <Box
          sx={{
            p: 2,
            borderTop: isDarkMode ? '1px solid rgba(255,255,255,0.1)' : '1px solid #f0f0f0',
            display: 'flex',
            justifyContent: 'center',
            background: isDarkMode ? 'transparent' : `linear-gradient(to top, ${theme.palette.primary.main}08 0%, transparent 100%)`,
          }}
        >
          <Button
            variant="outlined"
            size="small"
            endIcon={
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 18,
                  height: 18,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.primary.main,
                  transition: 'all 0.2s ease',
                  ml: 0.5,
                }}
              >
                <ArrowForwardIcon sx={{ color: 'white', fontSize: '0.875rem' }} />
              </Box>
            }
            onClick={onViewAll}
            sx={{
              borderColor: theme.palette.primary.main,
              color: theme.palette.primary.main,
              fontWeight: 'medium',
              px: 2,
              borderRadius: '20px',
              borderWidth: '1.5px',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: `${theme.palette.primary.main}10`,
                borderColor: theme.palette.primary.main,
                transform: 'translateY(-2px)',
                boxShadow: `0 4px 8px ${theme.palette.primary.main}30`,
              }
            }}
          >
            View All Activities
          </Button>
        </Box>
      )}
    </Paper>
  );
};

export default ActivityTimelineCard;
