module.exports = {

"[project]/node_modules/@mui/material/Table/index.js [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@mui_material_Table_cc1006f4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mui/material/Table/index.js [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/@mui/material/TableRow/index.js [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@mui_material_TableRow_index_2283ea85.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mui/material/TableRow/index.js [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/@mui/material/TableFooter/index.js [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@mui_material_TableFooter_5b13458a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mui/material/TableFooter/index.js [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/@mui/material/Typography/index.js [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_@mui_material_Typography_index_aaea8dbe.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@mui/material/Typography/index.js [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/node_modules/apexcharts/dist/apexcharts.esm.js [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_apexcharts_dist_apexcharts_esm_06fbcb9d.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/apexcharts/dist/apexcharts.esm.js [app-ssr] (ecmascript)");
    });
});
}}),

};