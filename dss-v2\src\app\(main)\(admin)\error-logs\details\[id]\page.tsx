"use client";

import React from 'react';
import { use } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Chip,
  Divider,
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import LinkIcon from '@mui/icons-material/Link';
import CodeIcon from '@mui/icons-material/Code';
import { PageHeader } from '@/components/common/pageHeader/pageHeader';
import { useThemeWithToggle } from '@/context/ThemeContext';
import ErrorDetailsController from './error-details.controller';

export default function ErrorDetails({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const errorId = unwrappedParams.id;

  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';
  const { getters } = ErrorDetailsController(errorId);
  const { errorData, breadcrumbs, loading } = getters;

  if (loading || !errorData) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6">Loading error details...</Typography>
      </Box>
    );
  }

  return (
    <>
      <PageHeader
        title={`Error Details #${errorId}`}
        breadcrumbs={breadcrumbs}
      />

      <Paper
        elevation={0}
        sx={{
          p: 3,
          borderRadius: 2,
          backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.1)' : 'rgba(0, 0, 0, 0.02)',
          mb: 3
        }}
      >
        {/* Header with error type and metadata */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 3
          }}
        >
          <ErrorOutlineIcon
            sx={{
              mr: 1.5,
              color: '#f44336',
              fontSize: '2rem'
            }}
          />
          <Typography
            variant="h5"
            component="h1"
            sx={{
              fontWeight: 600,
              color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
            }}
          >
            {errorData.errorType}
          </Typography>
          <Box sx={{ flexGrow: 1 }} />
          <Chip
            label={errorData.method}
            color="primary"
            size="small"
            sx={{
              fontWeight: 600,
              backgroundColor: '#3A52A6',
              mr: 1
            }}
          />
          <Chip
            icon={<AccessTimeIcon fontSize="small" />}
            label={errorData.timestamp}
            variant="outlined"
            size="small"
            sx={{
              fontWeight: 500,
              borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'
            }}
          />
        </Box>

        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={3}>
          {/* Left column - Error details */}
          <Grid item xs={12} md={5}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                {/* User */}
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <PersonOutlineIcon
                    sx={{
                      mr: 1.5,
                      mt: 0.3,
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'
                    }}
                    fontSize="small"
                  />
                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                      }}
                    >
                      User
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)'
                      }}
                    >
                      {errorData.user}
                    </Typography>
                  </Box>
                </Box>

                {/* Path */}
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <LinkIcon
                    sx={{
                      mr: 1.5,
                      mt: 0.3,
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)'
                    }}
                    fontSize="small"
                  />
                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                      }}
                    >
                      Path
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                        wordBreak: 'break-all'
                      }}
                    >
                      {errorData.path}
                    </Typography>
                  </Box>
                </Box>

                {/* Error Message */}
                <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                  <ErrorOutlineIcon
                    sx={{
                      mr: 1.5,
                      mt: 0.3,
                      color: '#f44336'
                    }}
                    fontSize="small"
                  />
                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: 600,
                        mb: 0.5,
                        color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                      }}
                    >
                      Error Message
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#f44336',
                        wordBreak: 'break-all'
                      }}
                    >
                      {errorData.errorMessage}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Grid>

          {/* Right column - Traceback */}
          <Grid item xs={12} md={7}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CodeIcon fontSize="small" sx={{ mr: 1, opacity: 0.7 }} />
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                  }}
                >
                  Traceback
                </Typography>
              </Box>

              <Box
                sx={{
                  p: 2,
                  backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.04)',
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  lineHeight: 1.5,
                  whiteSpace: 'pre-wrap',
                  overflowX: 'auto',
                  maxHeight: '400px',
                  overflow: 'auto',
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.9)'
                }}
              >
                {errorData.traceback}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Paper>
    </>
  );
}
