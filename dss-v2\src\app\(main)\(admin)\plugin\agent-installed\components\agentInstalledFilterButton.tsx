"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import ComputerIcon from '@mui/icons-material/Computer';

interface AgentInstalledFilterButtonProps {
  filterOptions: { label: string; value: string; type?: 'text' | 'date' | 'email' | 'number' }[];
  onApplyFilters: (filters: string[]) => void;
  searchTerm: string;
}

const AgentInstalledFilterButton: React.FC<AgentInstalledFilterButtonProps> = ({
  filterOptions,
  onApplyFilters,
  searchTerm
}) => {

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Agent Installed Filters"
      title="Agent Installed Filters"
      description="Apply filters to refine your agent installed search results"
      icon={<ComputerIcon />}
      searchTerm={searchTerm}
    />
  );
};

export default AgentInstalledFilterButton;
