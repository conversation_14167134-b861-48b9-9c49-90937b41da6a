"use client";

import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  useTheme,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  InputAdornment
} from "@mui/material";
import { PageHeader } from "@/components/common/pageHeader";
import { Icon } from "@/components/common";
import {
  faUserShield,
  faUsers,
  faCog,
  faShieldAlt,
  faChartLine,
  faDatabase,
  faKey,
  faClipboardList,
  faUser
} from "@fortawesome/free-solid-svg-icons";
import { useRouter } from "next/navigation";
import FilterListIcon from "@mui/icons-material/FilterList";
import CloseIcon from "@mui/icons-material/Close";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import { DateTime } from "luxon";

const SuperAdminDashboardPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const [filterModalOpen, setFilterModalOpen] = useState(false);
  const [startDate, setStartDate] = useState<DateTime | null>(null);
  const [endDate, setEndDate] = useState<DateTime | null>(null);
  const [isDataRefreshing, setIsDataRefreshing] = useState(false);

  // Stats data
  const [totalActiveUsers, setTotalActiveUsers] = useState(6);
  const [totalLicenses, setTotalLicenses] = useState(197);
  const [totalCustomers, setTotalCustomers] = useState(197);

  // Chart data for trend analysis
  const [usersData, setUsersData] = useState([0, 0, 0, 0, 0, 0, 6]);
  const [licensesData, setLicensesData] = useState([0, 0, 0, 0, 0, 0, 197]);
  const [customersData, setCustomersData] = useState([0, 0, 0, 0, 0, 0, 197]);
  const [chartLabels, setChartLabels] = useState(['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May']);

  // State for chart interactivity
  const [hoveredUserIndex, setHoveredUserIndex] = useState<number | null>(null);
  const [hoveredLicenseIndex, setHoveredLicenseIndex] = useState<number | null>(null);
  const [hoveredCustomerIndex, setHoveredCustomerIndex] = useState<number | null>(null);

  // Handle filter modal
  const handleOpenFilterModal = () => {
    setFilterModalOpen(true);
  };

  const handleCloseFilterModal = () => {
    setFilterModalOpen(false);
  };

  const handleApplyFilters = (newStartDate: DateTime | null, newEndDate: DateTime | null) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
    setFilterModalOpen(false);

    // Simulate data refresh
    setIsDataRefreshing(true);
    setTimeout(() => {
      // Update data based on filters (mock implementation)
      if (newStartDate && newEndDate) {
        // Calculate date difference in days
        const diffInDays = newEndDate.diff(newStartDate, 'days').days;

        // Generate more realistic data based on date range
        const numPoints = Math.min(7, Math.max(2, Math.ceil(diffInDays / 30)));

        // Generate new labels based on date range
        const newLabels = [];
        for (let i = 0; i < numPoints; i++) {
          const date = newStartDate.plus({ days: (diffInDays / (numPoints - 1)) * i });
          newLabels.push(date.toFormat('MMM'));
        }
        setChartLabels(newLabels);

        // Generate random data for demonstration with more realistic patterns
        const userBase = Math.floor(Math.random() * 5) + 3; // Base number of users
        const licenseBase = Math.floor(Math.random() * 50) + 150; // Base number of licenses
        const customerBase = Math.floor(Math.random() * 30) + 170; // Base number of customers

        // Create data with a trend
        const newUsersData = [];
        const newLicensesData = [];
        const newCustomersData = [];

        for (let i = 0; i < numPoints; i++) {
          // Add some randomness but maintain an upward trend for the last point
          const userFactor = i === numPoints - 1 ? 1.5 : 0.8 + (i / numPoints);
          const licenseFactor = i === numPoints - 1 ? 1.2 : 0.9 + (i / numPoints);
          const customerFactor = i === numPoints - 1 ? 1.3 : 0.85 + (i / numPoints);

          newUsersData.push(Math.floor(userBase * userFactor));
          newLicensesData.push(Math.floor(licenseBase * licenseFactor));
          newCustomersData.push(Math.floor(customerBase * customerFactor));
        }

        // Update state with new data
        setTotalActiveUsers(newUsersData[newUsersData.length - 1]);
        setTotalLicenses(newLicensesData[newLicensesData.length - 1]);
        setTotalCustomers(newCustomersData[newCustomersData.length - 1]);

        setUsersData(newUsersData);
        setLicensesData(newLicensesData);
        setCustomersData(newCustomersData);
      } else {
        // Reset to default data if no date range is selected
        setChartLabels(['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr', 'May']);
        setTotalActiveUsers(6);
        setTotalLicenses(197);
        setTotalCustomers(197);
        setUsersData([0, 0, 0, 0, 0, 0, 6]);
        setLicensesData([0, 0, 0, 0, 0, 0, 197]);
        setCustomersData([0, 0, 0, 0, 0, 0, 197]);
      }
      setIsDataRefreshing(false);
    }, 800);
  };

  // Helper function to generate random chart data
  const generateRandomChartData = (length: number, min: number, max: number) => {
    const lastValue = Math.floor(Math.random() * (max - min)) + min;
    const result = Array(length - 1).fill(0).map(() => Math.floor(Math.random() * (min + 2)));
    return [...result, lastValue];
  };

  // Function to render line chart
  const renderLineChart = (data: number[], labels: string[], color: string, hoveredIndex: number | null, setHoveredIndex: (index: number | null) => void) => {
    // Find the max value for scaling
    const maxValue = Math.max(...data, 1); // Ensure we have a non-zero max value

    // Calculate trend (positive or negative)
    const trend = data[data.length - 1] > data[0] ? 'up' : 'down';
    const trendPercentage = data[0] === 0 ? 100 : Math.abs(Math.round(((data[data.length - 1] - data[0]) / Math.max(data[0], 1)) * 100));

    return (
      <Box sx={{ flexGrow: 1, height: '200px', position: 'relative' }}>
        {/* Trend indicator */}
        <Box sx={{ position: 'absolute', top: -30, right: 0, display: 'flex', alignItems: 'center' }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              px: 1,
              py: 0.5,
              borderRadius: '4px',
              backgroundColor: `${color}20`,
              color: color,
              fontSize: '0.75rem',
              fontWeight: 'bold',
            }}
          >
            {trend === 'up' ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  width: 0,
                  height: 0,
                  borderLeft: '4px solid transparent',
                  borderRight: '4px solid transparent',
                  borderBottom: `8px solid ${color}`,
                  mr: 0.5
                }} />
                {trendPercentage}%
              </Box>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  width: 0,
                  height: 0,
                  borderLeft: '4px solid transparent',
                  borderRight: '4px solid transparent',
                  borderTop: `8px solid ${color}`,
                  mr: 0.5
                }} />
                {trendPercentage}%
              </Box>
            )}
          </Box>
        </Box>

        {/* Line chart area */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          height: '100%',
        }}>
          {/* Draw the line */}
          <svg width="100%" height="100%" style={{ overflow: 'visible' }}>
            {/* Area under the line */}
            <defs>
              <linearGradient id={`gradient-${color.replace('#', '')}`} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={color} stopOpacity="0.3" />
                <stop offset="100%" stopColor={color} stopOpacity="0.05" />
              </linearGradient>
            </defs>

            {/* Create the area under the line */}
            <path
              d={`
                M ${0} ${100 - (data[0] / maxValue) * 100}
                ${data.map((value, i) => {
                  const x = (i / (data.length - 1)) * 100;
                  const y = 100 - (value / maxValue) * 100;
                  return `L ${x} ${y}`;
                }).join(' ')}
                L ${100} ${100}
                L ${0} ${100}
                Z
              `}
              fill={`url(#gradient-${color.replace('#', '')})`}
              strokeWidth="0"
            />

            {/* Create the line */}
            <path
              d={`
                M ${0} ${100 - (data[0] / maxValue) * 100}
                ${data.map((value, i) => {
                  const x = (i / (data.length - 1)) * 100;
                  const y = 100 - (value / maxValue) * 100;
                  return `L ${x} ${y}`;
                }).join(' ')}
              `}
              fill="none"
              stroke={color}
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />

            {/* Data points */}
            {data.map((value, i) => {
              const x = (i / (data.length - 1)) * 100;
              const y = 100 - (value / maxValue) * 100;
              const isHovered = hoveredIndex === i;

              return (
                <g key={i}>
                  <circle
                    cx={x}
                    cy={y}
                    r={isHovered ? 5 : 3}
                    fill={isHovered ? color : "white"}
                    stroke={color}
                    strokeWidth="2"
                    style={{
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                    }}
                    onMouseEnter={() => setHoveredIndex(i)}
                    onMouseLeave={() => setHoveredIndex(null)}
                  />

                  {isHovered && (
                    <g>
                      <rect
                        x={x - 20}
                        y={y - 25}
                        width="40"
                        height="20"
                        rx="4"
                        fill={color}
                      />
                      <text
                        x={x}
                        y={y - 12}
                        textAnchor="middle"
                        fill="white"
                        fontSize="10"
                        fontWeight="bold"
                      >
                        {value.toLocaleString()}
                      </text>
                    </g>
                  )}
                </g>
              );
            })}
          </svg>
        </Box>

        {/* X-axis labels */}
        <Box sx={{
          position: 'absolute',
          bottom: -20,
          left: 0,
          right: 0,
          display: 'flex',
          justifyContent: 'space-between',
        }}>
          {labels.map((label, i) => (
            <Typography
              key={i}
              variant="caption"
              sx={{
                fontSize: '0.6rem',
                color: 'text.secondary',
              }}
            >
              {label}
            </Typography>
          ))}
        </Box>
      </Box>
    );
  };

  const modules = [
    {
      title: "Customers",
      description: "Manage customer accounts, view customer details and activity",
      icon: faUsers,
      color: theme.palette.primary.main,
      path: "/super-admin/customers"
    },
    {
      title: "Users",
      description: "Manage user accounts, permissions, and access controls",
      icon: faUser,
      color: theme.palette.success.main,
      path: "/super-admin/users"
    },
    {
      title: "Licenses",
      description: "Manage license keys, allocations, and expirations",
      icon: faKey,
      color: theme.palette.info.main,
      path: "/super-admin/licenses"
    },
    {
      title: "All Logs Report",
      description: "View comprehensive logs and reports across the system",
      icon: faClipboardList,
      color: theme.palette.warning.main,
      path: "/super-admin/logs-report"
    },
    {
      title: "System Settings",
      description: "Configure system-wide settings and preferences",
      icon: faCog,
      color: theme.palette.error.main,
      path: "/super-admin/settings"
    },
    {
      title: "Security",
      description: "Manage security settings, audit logs, and access controls",
      icon: faShieldAlt,
      color: theme.palette.secondary.main,
      path: "/super-admin/security"
    }
  ];

  return (
    <div>
      <PageHeader
        title="Dashboard"
        breadcrumbs={[
          { name: "Dashboard", path: "/dashboard", forwardParam: false },
          { name: "Super Admin", path: "/super-admin", forwardParam: false },
          { name: "Dashboard", path: "/super-admin/dashboard", forwardParam: false }
        ]}
        actions={
          <IconButton
            size="small"
            onClick={handleOpenFilterModal}
            sx={{
              border: '1px solid #e0e0e0',
              borderRadius: '4px',
              p: 1,
              position: 'relative',
              ...(startDate || endDate ? {
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 3,
                  right: 3,
                  width: 8,
                  height: 8,
                  backgroundColor: '#3A52A6',
                  borderRadius: '50%',
                }
              } : {})
            }}
          >
            <FilterListIcon fontSize="small" />
          </IconButton>
        }
      />

      {/* Top Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3, mt: 1 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '4px',
                height: '100%',
                backgroundColor: '#3A52A6',
              }
            }}
          >
            <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 500, mb: 1 }}>
              Total Active Users
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
              <Typography
                variant="h4"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                }}
              >
                {totalActiveUsers}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  backgroundColor: `#3A52A620`,
                  color: '#3A52A6',
                }}
              >
                <Icon icon={faUser} size="small" onlyIcon />
              </Box>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '4px',
                height: '100%',
                backgroundColor: '#4CAF50',
              }
            }}
          >
            <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 500, mb: 1 }}>
              Total Licenses
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
              <Typography
                variant="h4"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                }}
              >
                {totalLicenses}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  backgroundColor: `#4CAF5020`,
                  color: '#4CAF50',
                }}
              >
                <Icon icon={faKey} size="small" onlyIcon />
              </Box>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              position: 'relative',
              overflow: 'hidden',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '4px',
                height: '100%',
                backgroundColor: '#9C27B0',
              }
            }}
          >
            <Typography variant="subtitle2" color="text.secondary" sx={{ fontWeight: 500, mb: 1 }}>
              Total Customers
            </Typography>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 'auto' }}>
              <Typography
                variant="h4"
                component="div"
                sx={{
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                }}
              >
                {totalCustomers}
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  backgroundColor: `#9C27B020`,
                  color: '#9C27B0',
                }}
              >
                <Icon icon={faUsers} size="small" onlyIcon />
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Trend Analysis Charts */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="medium">
                Total Active Users
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary', bgcolor: '#f0f4ff', px: 1, py: 0.5, borderRadius: 1 }}>
                Users
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Users
            </Typography>
            {renderLineChart(usersData, chartLabels, '#3A52A6', hoveredUserIndex, setHoveredUserIndex)}
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="medium">
                Total Licenses
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary', bgcolor: '#f0f4ff', px: 1, py: 0.5, borderRadius: 1 }}>
                Licenses
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Licenses
            </Typography>
            {renderLineChart(licensesData, chartLabels, '#4CAF50', hoveredLicenseIndex, setHoveredLicenseIndex)}
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              border: '1px solid #e0e0e0',
              borderRadius: '8px',
              transition: 'all 0.3s ease',
              transform: isDataRefreshing ? 'scale(0.98)' : 'scale(1)',
              opacity: isDataRefreshing ? 0.8 : 1,
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="medium">
                Total Customers
              </Typography>
              <Typography variant="caption" sx={{ color: 'text.secondary', bgcolor: '#f0f4ff', px: 1, py: 0.5, borderRadius: 1 }}>
                Customers
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Customers
            </Typography>
            {renderLineChart(customersData, chartLabels, '#9C27B0', hoveredCustomerIndex, setHoveredCustomerIndex)}
          </Paper>
        </Grid>
      </Grid>

      <Box sx={{ mt: 3, mb: 4 }}>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
            backgroundColor: theme.palette.background.paper,
            backdropFilter: "blur(4px)",
          }}
        >
          <Typography variant="h6" gutterBottom>
            Welcome to Super Admin Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary">
            As a Super Admin, you have full control over the system. You can manage customers, users,
            licenses, view logs, configure security settings, and oversee all administrative operations.
            Select a module below to get started.
          </Typography>
        </Paper>
      </Box>

      <Grid container spacing={3}>
        {modules.map((module, index) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={index}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                borderRadius: 2,
                border: `1px solid ${theme.palette.divider}`,
                backgroundColor: theme.palette.background.paper,
                transition: 'all 0.3s ease',
                '&:hover': {
                  boxShadow: '0 4px 20px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-4px)',
                },
              }}
            >
              <CardActionArea
                sx={{ height: '100%', p: 1 }}
                onClick={() => router.push(module.path)}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Box
                      sx={{
                        backgroundColor: `${module.color}20`,
                        borderRadius: 1,
                        p: 1,
                        mr: 2,
                      }}
                    >
                      <Icon
                        icon={module.icon}
                        color={module.color}
                        size="small"
                        onlyIcon
                      />
                    </Box>
                    <Typography variant="h6" color="text.primary">
                      {module.title}
                    </Typography>
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {module.description}
                  </Typography>
                </CardContent>
              </CardActionArea>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Filter Options Modal */}
      <Dialog
        open={filterModalOpen}
        onClose={handleCloseFilterModal}
        maxWidth="xs"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
          }
        }}
      >
        <Box sx={{ position: 'absolute', top: 8, right: 8 }}>
          <IconButton
            edge="end"
            color="inherit"
            onClick={handleCloseFilterModal}
            aria-label="close"
            size="small"
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <DialogContent sx={{ p: 3, pt: 2 }}>
          <Typography variant="h6" sx={{ color: '#3A52A6', fontWeight: 600, mb: 1 }}>
            Filter Options
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Apply filters to refine your search results
          </Typography>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Start Date
              </Typography>
              <TextField
                fullWidth
                placeholder="dd-mm-yyyy"
                value={startDate ? startDate.toFormat("dd-MM-yyyy") : ""}
                onChange={(e) => {
                  try {
                    const date = DateTime.fromFormat(e.target.value, "dd-MM-yyyy");
                    if (date.isValid) {
                      setStartDate(date);
                    } else if (e.target.value === "") {
                      setStartDate(null);
                    }
                  } catch (error) {
                    // Invalid date format, do nothing
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <CalendarTodayIcon fontSize="small" color="action" />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>

            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                End Date
              </Typography>
              <TextField
                fullWidth
                placeholder="dd-mm-yyyy"
                value={endDate ? endDate.toFormat("dd-MM-yyyy") : ""}
                onChange={(e) => {
                  try {
                    const date = DateTime.fromFormat(e.target.value, "dd-MM-yyyy");
                    if (date.isValid) {
                      setEndDate(date);
                    } else if (e.target.value === "") {
                      setEndDate(null);
                    }
                  } catch (error) {
                    // Invalid date format, do nothing
                  }
                }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <CalendarTodayIcon fontSize="small" color="action" />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Box>
        </DialogContent>

        <Box sx={{ display: 'flex', p: 2, pt: 0 }}>
          <Button
            fullWidth
            variant="outlined"
            onClick={() => {
              setStartDate(null);
              setEndDate(null);
              handleCloseFilterModal();
            }}
            sx={{ mr: 1 }}
          >
            Cancel
          </Button>
          <Button
            fullWidth
            variant="contained"
            onClick={() => handleApplyFilters(startDate, endDate)}
            sx={{
              bgcolor: '#3A52A6',
              '&:hover': {
                bgcolor: '#2A3F8F',
              }
            }}
          >
            Apply
          </Button>
        </Box>
      </Dialog>
    </div>
  );
};

export default SuperAdminDashboardPage;
