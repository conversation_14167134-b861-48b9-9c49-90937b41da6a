"use client";

import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Link,
  Paper,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";

interface SimpleBulkAllocateModalProps {
  open: boolean;
  onClose: () => void;
}

export default function SimpleBulkAllocateModal({ open, onClose }: SimpleBulkAllocateModalProps) {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle sx={{ 
        display: "flex", 
        justifyContent: "space-between", 
        alignItems: "center",
        pb: 1
      }}>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          Allocate Multiple Licenses
        </Typography>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body2" sx={{ mb: 3 }}>
          Upload a CSV file containing license allocations
        </Typography>
        
        <Paper 
          elevation={0} 
          sx={{ 
            border: '2px dashed rgba(0, 0, 0, 0.2)', 
            p: 4, 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            bgcolor: 'rgba(0, 0, 0, 0.02)'
          }}
        >
          <CloudUploadIcon sx={{ fontSize: 48, color: 'rgba(0, 0, 0, 0.3)', mb: 2 }} />
          
          <Typography variant="body1" align="center" sx={{ mb: 1 }}>
            Drag and drop your CSV file here, or browse
          </Typography>
          
          <Typography variant="body2" align="center" color="textSecondary">
            CSV file should contain the following columns:
            <br />
            <Box component="span" sx={{ fontFamily: 'monospace', color: '#1976d2' }}>
              license_id, allocated_to, mac_address
            </Box>
          </Typography>
        </Paper>
        
        <Box sx={{ mt: 2, textAlign: 'center' }}>
          <Typography variant="body2" color="textSecondary">
            Need a template? <Link href="#" underline="hover">Download CSV template</Link>
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} variant="outlined">
          Cancel
        </Button>
        <Button variant="contained" sx={{ bgcolor: "#3A52A6" }}>
          Upload and Allocate
        </Button>
      </DialogActions>
    </Dialog>
  );
}
