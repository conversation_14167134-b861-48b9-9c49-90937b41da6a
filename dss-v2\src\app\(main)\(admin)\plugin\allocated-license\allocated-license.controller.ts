"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import useMeasure from "react-use-measure";
import { Theme, useMediaQuery, useTheme, Button, Checkbox } from "@mui/material";
import { useAppSnackbar } from "@/hooks/snackbar.hook";
import { SnackbarTypeEnum } from "@/enum";

import {
  IBreadcrumbDisplay,
  IHeader,
  TableComponentEnum,
} from "@/components/common";
import { ChipComponent } from "@/components/common/chipComponent";
import { RoutePathEnum } from "@/enum";
import { MeasureRefType } from "@/interfaces";
import {
  GET_TABLE_PAGINATION_DATA,
  tableActions,
  useAppDispatch,
  useAppSelector,
} from "@/redux";

import { AvatarMenu } from "@/components/common";
import { RevokeButton } from "@/app/(main)/(admin)/plugin/allocated-license/components/revokeButton/revokeButton";
import { CheckboxCell } from "@/components/common/checkboxCell/checkboxCell";
import { HeaderCheckbox } from "@/components/common/checkboxCell/headerCheckbox";

export const AllocatedLicenseController = () => {
  // Static data for the table
  const staticLicenseInfo = [
    {
      sn: 1,
      licenseId: "LIC-00232",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 2,
      licenseId: "LIC-00233",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 3,
      licenseId: "LIC-00234",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 4,
      licenseId: "LIC-00235",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 5,
      licenseId: "LIC-00236",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 6,
      licenseId: "LIC-00237",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 7,
      licenseId: "LIC-00238",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 8,
      licenseId: "LIC-00239",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 9,
      licenseId: "LIC-00240",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
    {
      sn: 10,
      licenseId: "LIC-00241",
      allocatedTo: "-",
      macAddress: "-",
      validityFrom: "23-04-2025 12:00 AM",
      validityTill: "23-04-2026 11:59 PM",
      issue: "-",
    },
  ];

  const [ref, { height }] = useMeasure();
  const theme: Theme = useTheme();
  const isMobileView: boolean = useMediaQuery(theme.breakpoints.down("sm"));
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilters, setActiveFilters] = useState<string[]>([
    "licenseId",
    "allocatedTo",
    "macAddress",
  ]);
  const [selectedLicenses, setSelectedLicenses] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [isRevokeModalOpen, setIsRevokeModalOpen] = useState(false);
  const [selectedLicenseData, setSelectedLicenseData] = useState<{
    licenseId: string;
    email: string;
    macAddress: string;
  }>({
    licenseId: "",
    email: "",
    macAddress: "",
  });

  // Column options for the ActiveColumn component
  const COLUMN_OPTIONS = [
    { label: "Checkbox", value: "checkbox" },
    { label: "SN", value: "sn" },
    { label: "LICENSE ID", value: "licenseId" },
    { label: "ALLOCATED TO", value: "allocatedTo" },
    { label: "MAC ADDRESS", value: "macAddress" },
    { label: "VALIDITY FROM", value: "validityFrom" },
    { label: "VALIDITY TILL", value: "validityTill" },
    { label: "ISSUE", value: "issue" },
    { label: "REVOKE", value: "revoke" },
  ];

  // Initially visible columns
  const [visibleColumns, setVisibleColumns] = useState<string[]>([
    "checkbox", "sn", "licenseId", "allocatedTo", "macAddress",
    "validityFrom", "validityTill", "issue", "revoke"
  ]);

  /**
   * @funtions {changeRows} - Update Rows to Show
   * @param {number} newRows
   */
  const changeRows = useCallback(
    (newRows: number): void => {
      dispatch(tableActions.setTableRows(newRows));
    },
    [dispatch]
  );

  /**
   * @functions {changePage} - Update Active Page
   * @param {number} newPage
   */
  const changePage = useCallback(
    (newPage: number): void => {
      dispatch(tableActions.setTablePage(newPage));
    },
    [dispatch]
  );

  const filteredData = useMemo(
    () =>
      staticLicenseInfo.filter((item) => {
        if (!searchTerm) return true;

        const searchLower = searchTerm.toLowerCase();
        return activeFilters.some((filter) => {
          const value = item[filter as keyof typeof item];
          return value?.toString().toLowerCase().includes(searchLower);
        });
      }),
    [searchTerm, activeFilters]
  );

  const { enqueueSnackbar } = useAppSnackbar();

  const handleRevoke = useCallback((licenseId: string) => {
    console.log(`Opening revoke modal for license: ${licenseId}`);
    // Find the license in the data
    const license = staticLicenseInfo.find(item => item.licenseId === licenseId);
    if (license) {
      // In a real app, you would fetch the email from the API
      setSelectedLicenseData({
        licenseId: license.licenseId,
        email: license.allocatedTo !== "-" ? license.allocatedTo : "<EMAIL>",
        macAddress: license.macAddress !== "-" ? license.macAddress : "03:e8:b9:5c:88:4a",
      });
      setIsRevokeModalOpen(true);
    }
  }, [staticLicenseInfo]);

  const handleCloseRevokeModal = useCallback(() => {
    setIsRevokeModalOpen(false);
  }, []);

  const handleRevokeSubmit = useCallback((licenseId: string) => {
    console.log(`Revoking license: ${licenseId}`);
    // In a real application, you would call an API to revoke the license

    // Show success message
    enqueueSnackbar(
      `License ${licenseId} has been revoked successfully`,
      SnackbarTypeEnum.SUCCESS
    );

    // Close the modal
    setIsRevokeModalOpen(false);
  }, [enqueueSnackbar]);

  const handleSelectLicense = useCallback((licenseId: string, checked: boolean) => {
    setSelectedLicenses(prev => {
      if (checked) {
        return [...prev, licenseId];
      } else {
        return prev.filter(id => id !== licenseId);
      }
    });
  }, []);

  const handleSelectAll = useCallback((checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      const allLicenseIds = filteredData.map(item => item.licenseId);
      setSelectedLicenses(allLicenseIds);
    } else {
      setSelectedLicenses([]);
    }
  }, [filteredData]);

  const handleBulkRevoke = useCallback(() => {
    if (selectedLicenses.length === 0) {
      enqueueSnackbar('No licenses selected', SnackbarTypeEnum.WARNING);
      return;
    }

    console.log(`Revoking licenses: ${selectedLicenses.join(', ')}`);
    // In a real application, you would call an API to revoke the licenses
    enqueueSnackbar(
      `${selectedLicenses.length} licenses have been revoked successfully`,
      SnackbarTypeEnum.SUCCESS
    );

    // Clear selections after revoking
    setSelectedLicenses([]);
    setSelectAll(false);
  }, [selectedLicenses, enqueueSnackbar]);

  const newApplication = useMemo(
    () =>
      filteredData.map((item) => ({
        checkbox: item.licenseId,
        sn: item.sn,
        licenseId: item.licenseId,
        allocatedTo: item.allocatedTo,
        macAddress: item.macAddress,
        validityFrom: item.validityFrom,
        validityTill: item.validityTill,
        issue: item.issue,
        revoke: item.licenseId,
      })),
    [filteredData]
  );

  const tablePaginationData = useAppSelector(GET_TABLE_PAGINATION_DATA);
  // Static pagination data
  const { page, limit, isLoading } = tablePaginationData;

  const contactPagination = {
    totalCount: filteredData.length,
  };

  // Table headers
  const headers: IHeader[] = [
    {
      id: "checkbox",
      name: "",
      hidden: false,
      width: 50,
      type: TableComponentEnum.COMPONENT,
      component: CheckboxCell,
      headerComponent: HeaderCheckbox,
      headerComponentProps: {
        checked: selectAll,
        onChange: handleSelectAll,
      },
    },
    {
      id: "sn",
      name: "SN",
      hidden: false,
      width: 60,
      type: TableComponentEnum.STRING,
    },
    {
      id: "licenseId",
      name: "LICENSE ID",
      hidden: false,
      width: 120,
      type: TableComponentEnum.STRING,
    },
    {
      id: "allocatedTo",
      name: "ALLOCATED TO",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "macAddress",
      name: "MAC ADDRESS",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "validityFrom",
      name: "VALIDITY FROM",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "validityTill",
      name: "VALIDITY TILL",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "issue",
      name: "ISSUE",
      hidden: false,
      width: 100,
      type: TableComponentEnum.STRING,
    },
    {
      id: "revoke",
      name: "REVOKE",
      hidden: false,
      width: 100,
      type: TableComponentEnum.COMPONENT,
      component: RevokeButton,
    },
  ];

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Plugin",
      path: RoutePathEnum.PLUGIN,
      forwardParam: false,
    },
    {
      name: "Allocated License",
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  const handleSearch = useCallback(
    (term: string, filters: string[]) => {
      setSearchTerm(term);
      setActiveFilters(filters);
      // Reset to first page when searching
      dispatch(tableActions.setTablePage(0));
    },
    [dispatch]
  );

  /**
   * Handle column visibility changes
   * @param {string[]} newVisibleColumns - New array of visible column IDs
   */
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  return {
    getters: {
      breadcrumbs,
      headers,
      contactPagination,
      tablePaginationData,
      isLoading: false,
      isOpenDrawer: false,
      filter: {} as any,
      newApplication,
      height,
      isMobileView,
      selectedLicenses,
      selectAll,
      COLUMN_OPTIONS,
      visibleColumns,
      isRevokeModalOpen,
      selectedLicenseData,
      searchTerm,
    },
    handlers: {
      changePage,
      changeRows,
      handleSearch,
      handleRevoke,
      handleSelectLicense,
      handleSelectAll,
      handleBulkRevoke,
      handleColumnVisibilityChange,
      handleCloseRevokeModal,
      handleRevokeSubmit,
    },

    ref: ref as MeasureRefType,
  };
};
