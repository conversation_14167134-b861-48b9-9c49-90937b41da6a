"use client";

import React, { JSX, useMemo, useState } from "react";
import { Paper, Box } from "@mui/material";

import { ResolvedDisputesController } from "./resolved-disputes.controller";
import {
  ResolvedDisputesTableContainer
} from "./components/resolvedDisputesTable.style";
import { ResolvedDisputesTable } from "./components/resolvedDisputesTable";
import { PageHeader, SearchBar, ActiveColumn } from "@/components/common";
import { SuppressHydrationWarning } from "@/components/common/suppressHydrationWarning";
import ResolvedDisputesFilterButton from "./components/resolvedDisputesFilterButton";

/**
 * @page {ResolvedDisputes} - Display Resolved Disputes Information
 * @return {JSX.Element}
 */
export default function ResolvedDisputes(): JSX.Element {
  const { getters, handlers, ref } = ResolvedDisputesController();
  const {
    headers,
    tablePaginationData,
    newApplication,
    contactPagination,
    height,
    breadcrumbs,
    FILTER_OPTIONS,
    COLUMN_OPTIONS,
    visibleColumns,
    initialVisibleColumns,
  } = getters;
  const [searchTerm, setSearchTerm] = useState("");
  const {
    changePage,
    changeRows,
    handleSearch,
    handleColumnVisibilityChange,
    handleAdminStatusChange
  } = handlers;

  const customSearchBar = useMemo(() => {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <SearchBar
          onSearch={(term, filters) => {
            setSearchTerm(term);
            handleSearch(term, filters);
          }}
          placeholder="Search resolved disputes..."
          filterOptions={FILTER_OPTIONS}
          useAdvancedFilter={false}
        />
        <ResolvedDisputesFilterButton
          filterOptions={FILTER_OPTIONS.map(option => ({
            ...option,
            type: option.value === 'createdAt' ? 'date' :
                  option.value.includes('Email') ? 'email' : 'text'
          }))}
          onApplyFilters={(filters) => handleSearch(searchTerm, filters)}
          searchTerm={searchTerm}
        />
        <ActiveColumn
          columnOptions={COLUMN_OPTIONS}
          visibleColumns={visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          variant="popover"
        />
      </Box>
    );
  }, [handleSearch, FILTER_OPTIONS, COLUMN_OPTIONS, visibleColumns, handleColumnVisibilityChange, searchTerm]);

  const table = useMemo(
    () => (
      <Paper elevation={0} sx={{ borderRadius: 1, overflow: 'hidden' }}>
        <ResolvedDisputesTableContainer>
          <ResolvedDisputesTable
            data={newApplication}
            visibleColumns={visibleColumns}
            onAdminStatusChange={handleAdminStatusChange}
          />
        </ResolvedDisputesTableContainer>
      </Paper>
    ),
    [
      newApplication,
      visibleColumns,
      handleAdminStatusChange
    ]
  );

  const header = useMemo(
    () => (
      <SuppressHydrationWarning>
        <div ref={ref}>
          <PageHeader
            title="Resolved Disputes"
            breadcrumbs={breadcrumbs}
            actions={customSearchBar}
          />
        </div>
      </SuppressHydrationWarning>
    ),
    [breadcrumbs, ref, customSearchBar]
  );

  return (
    <>
      {header}
      {table}
    </>
  );
}
