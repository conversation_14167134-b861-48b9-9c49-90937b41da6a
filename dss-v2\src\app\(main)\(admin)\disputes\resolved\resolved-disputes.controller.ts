"use client";

import { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react";
import useMeasure from "react-use-measure";
import { Theme, useMediaQuery, useTheme } from "@mui/material";

import {
  IBreadcrumbDisplay,
  IHeader,
  TableComponentEnum,
} from "@/components/common";
import { LocalStorageEnum, RoutePathEnum } from "@/enum";
import { MeasureRefType } from "@/interfaces";
import {
  GET_TABLE_PAGINATION_DATA,
  tableActions,
  useAppDispatch,
  useAppSelector,
} from "@/redux";
import { StorageHelper } from "@/utills";

import { ViewDetailsButton } from "../pending/components/viewDetailsButton";
import { StatusButton } from "../pending/components/statusButton";

/**
 * Resolved Disputes Controller
 * @return {Object} Controller with getters and handlers
 */
export const ResolvedDisputesController = () => {
  // Static data for the table
  const staticDisputesInfo = [
    {
      sn: 1,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "License Allocation",
      counter: 1,
      aiStatus: "Unsafe",
      adminStatus: "Safe",
      createdAt: "29-04-2023 11:46 AM",
    },
    {
      sn: 2,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "JUTF-8?Spreadsheet",
      counter: 3,
      aiStatus: "Unsafe",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:33 AM",
    },
    {
      sn: 3,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "-",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:34 AM",
    },
    {
      sn: 4,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "Check the urls 22222...",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:24 AM",
    },
    {
      sn: 5,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "-",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 10:52 AM",
    },
    {
      sn: 6,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "Your OTP for Login",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:59 AM",
    },
    {
      sn: 7,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "test",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:10 AM",
    },
    {
      sn: 8,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "test",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:14 AM",
    },
    {
      sn: 9,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "Explore Comprehensive...",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:01 AM",
    },
    {
      sn: 10,
      sendersEmail: "<EMAIL>",
      receiversEmail: "<EMAIL>",
      subject: "License Allocation",
      counter: 3,
      aiStatus: "Pending",
      adminStatus: "Unsafe",
      createdAt: "29-04-2023 11:27 AM",
    },
  ];

  const [ref, { height }] = useMeasure();
  const theme: Theme = useTheme();
  const isMobileView: boolean = useMediaQuery(theme.breakpoints.down("sm"));
  const dispatch = useAppDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const FILTER_OPTIONS = [
    { label: "Sender's Email", value: "sendersEmail" },
    { label: "Receiver's Email", value: "receiversEmail" },
    { label: "Subject", value: "subject" },
    { label: "Counter", value: "counter" },
    { label: "AI Status", value: "aiStatus" },
    { label: "Admin Status", value: "adminStatus" },
    { label: "Created At", value: "createdAt" },
  ];

  // Column options for the active column component
  const COLUMN_OPTIONS = [
    { label: "SN", value: "sn" },
    { label: "Sender's Email", value: "sendersEmail" },
    { label: "Receiver's Email", value: "receiversEmail" },
    { label: "Subject", value: "subject" },
    { label: "Counter", value: "counter" },
    { label: "AI Status", value: "aiStatus" },
    { label: "Admin Status", value: "adminStatus" },
    { label: "Created At", value: "createdAt" },
    { label: "Details", value: "details" },
  ];

  // Initial visible columns
  const initialVisibleColumns = [
    "sn", "sendersEmail", "receiversEmail", "subject", "counter",
    "aiStatus", "adminStatus", "createdAt", "details"
  ];

  const [activeFilters, setActiveFilters] = useState<string[]>([
    "sendersEmail",
    "receiversEmail",
    "subject",
  ]);

  // State to track which columns are visible
  const [visibleColumns, setVisibleColumns] = useState<string[]>(initialVisibleColumns);

  /**
   * @funtions {changeRows} - Update Rows to Show
   * @param {number} newRows
   */
  const changeRows = useCallback(
    (newRows: number): void => {
      dispatch(tableActions.setTableRows(newRows));
    },
    [dispatch]
  );

  /**
   * @functions {changePage} - Update Active Page
   * @param {number} newPage
   */
  const changePage = useCallback(
    (newPage: number): void => {
      dispatch(tableActions.setTablePage(newPage));
    },
    [dispatch]
  );

  // No longer need modal-related state and handlers since we're using direct navigation

  // Get the saved admin status choices from localStorage
  const getSavedAdminStatusChoices = () => {
    const savedChoicesStr = StorageHelper.getLocalStorage(LocalStorageEnum.ADMIN_STATUS_CHOICES);
    if (savedChoicesStr) {
      try {
        return JSON.parse(savedChoicesStr);
      } catch (e) {
        console.error('Error parsing saved admin status choices:', e);
        return {};
      }
    }
    return {};
  };

  // Get the default admin status preference
  const getDefaultAdminStatus = () => {
    return StorageHelper.getLocalStorage(LocalStorageEnum.ADMIN_STATUS_PREFERENCE) || 'safe';
  };

  // Load saved choices
  const savedStatusChoices = getSavedAdminStatusChoices();

  // State to store the disputes data that can be updated
  // Use saved choices for each item if available, otherwise use default for "pending"
  const initialDisputesData = staticDisputesInfo.map(item => {
    const itemId = `dispute-${item.sn}`;
    const savedStatus = savedStatusChoices[itemId];

    if (savedStatus) {
      // If we have a saved choice for this item, use it
      return {
        ...item,
        adminStatus: savedStatus
      };
    } else if (item.adminStatus.toLowerCase() === 'pending') {
      // For pending items without a saved choice, use the default
      return {
        ...item,
        adminStatus: getDefaultAdminStatus()
      };
    }

    // Otherwise, keep the original status
    return item;
  });

  const [disputesData, setDisputesData] = useState(initialDisputesData);

  // Load saved choices when component mounts
  useEffect(() => {
    const savedChoices = getSavedAdminStatusChoices();

    if (Object.keys(savedChoices).length > 0) {
      setDisputesData(prevData =>
        prevData.map(item => {
          const itemId = `dispute-${item.sn}`;
          const savedStatus = savedChoices[itemId];

          if (savedStatus) {
            return { ...item, adminStatus: savedStatus };
          } else if (item.adminStatus.toLowerCase() === 'pending') {
            return { ...item, adminStatus: getDefaultAdminStatus() };
          }

          return item;
        })
      );
    }
  }, []);

  // Handle admin status change
  const handleAdminStatusChange = useCallback((index: number, newStatus: string, comment?: string) => {
    console.log(`Changing admin status for dispute at index ${index} to ${newStatus}`);

    if (!comment) {
      console.warn('Status change attempted without a comment');
      return; // Don't allow status change without a comment
    }

    console.log(`Comment for status change: ${comment}`);

    // Update the status in the state
    setDisputesData(prevData => {
      const newData = [...prevData];
      if (newData[index]) {
        // Get the item being updated
        const item = newData[index];

        // Create a unique ID for this item
        const itemId = `dispute-${item.sn}`;

        // Get current saved choices
        const savedChoices = getSavedAdminStatusChoices();

        // Update the choice for this specific item
        savedChoices[itemId] = newStatus;

        // Save all choices back to localStorage
        StorageHelper.setLocalStorage(
          LocalStorageEnum.ADMIN_STATUS_CHOICES,
          JSON.stringify(savedChoices)
        );

        // Also save as the default preference for new items
        StorageHelper.setLocalStorage(LocalStorageEnum.ADMIN_STATUS_PREFERENCE, newStatus);

        // Update the item in state
        newData[index] = {
          ...item,
          adminStatus: newStatus,
          // In a real application, you would store the comment in the database
          // and potentially display it in a history view
        };
      }
      return newData;
    });
  }, []);

  const filteredData = useMemo(
    () =>
      disputesData.filter((item) => {
        if (!searchTerm) return true;

        const searchLower = searchTerm.toLowerCase();
        return activeFilters.some((filter) => {
          const value = item[filter as keyof typeof item];
          return value?.toString().toLowerCase().includes(searchLower);
        });
      }),
    [searchTerm, activeFilters, disputesData]
  );

  const newApplication = useMemo(
    () =>
      filteredData.map((item) => ({
        sn: item.sn,
        sendersEmail: item.sendersEmail,
        receiversEmail: item.receiversEmail,
        subject: item.subject,
        counter: item.counter,
        aiStatus: item.aiStatus,
        adminStatus: item.adminStatus,
        createdAt: item.createdAt,
        details: item.sn.toString(),
      })),
    [filteredData]
  );

  const tablePaginationData = useAppSelector(GET_TABLE_PAGINATION_DATA);
  // Static pagination data
  const { page, limit, isLoading } = tablePaginationData;

  const contactPagination = {
    totalCount: filteredData.length,
    totalPages: 18, // From the image reference
  };

  // Table headers
  const headers: IHeader[] = [
    {
      id: "sn",
      name: "SN",
      hidden: false,
      width: 60,
      type: TableComponentEnum.STRING,
    },
    {
      id: "sendersEmail",
      name: "SENDER'S EMAIL",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "receiversEmail",
      name: "RECEIVER'S EMAIL",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "subject",
      name: "SUBJECT",
      hidden: false,
      width: 180,
      type: TableComponentEnum.STRING,
    },
    {
      id: "counter",
      name: "COUNTER",
      hidden: false,
      width: 100,
      type: TableComponentEnum.STRING,
    },
    {
      id: "aiStatus",
      name: "AI STATUS",
      hidden: false,
      width: 120,
      type: TableComponentEnum.COMPONENT,
      component: StatusButton,
      componentProps: {
        type: 'ai',
      },
    },
    {
      id: "adminStatus",
      name: "ADMIN STATUS",
      hidden: false,
      width: 120,
      type: TableComponentEnum.COMPONENT,
      component: StatusButton,
      componentProps: {
        type: 'admin',
      },
    },
    {
      id: "createdAt",
      name: "CREATED AT",
      hidden: false,
      width: 150,
      type: TableComponentEnum.STRING,
    },
    {
      id: "details",
      name: "DETAILS",
      hidden: false,
      width: 100,
      type: TableComponentEnum.COMPONENT,
      component: ViewDetailsButton,
    },
  ];

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Disputes",
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
    {
      name: "Resolved Disputes",
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  const handleSearch = useCallback(
    (term: string, filters: string[]) => {
      setSearchTerm(term);
      setActiveFilters(filters);
      // Reset to first page when searching
      dispatch(tableActions.setTablePage(0));
    },
    [dispatch]
  );

  /**
   * Handle column visibility changes
   * @param {string[]} newVisibleColumns - The new list of visible columns
   */
  const handleColumnVisibilityChange = useCallback((newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  }, []);

  return {
    getters: {
      breadcrumbs,
      headers,
      contactPagination,
      tablePaginationData,
      isLoading: false,
      isOpenDrawer: false,
      filter: {} as any,
      newApplication,
      height,
      isMobileView,
      FILTER_OPTIONS,
      COLUMN_OPTIONS,
      visibleColumns,
      initialVisibleColumns,
    },
    handlers: {
      changePage,
      changeRows,
      handleSearch,
      handleColumnVisibilityChange,
      handleAdminStatusChange,
    },

    ref: ref as MeasureRefType,
  };
};
