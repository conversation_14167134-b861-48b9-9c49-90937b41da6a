"use client";

import React, { useState, useMemo } from 'react';
import { Box } from '@mui/material';
import { PageHeader } from '@/components/common/pageHeader/pageHeader';
import { SearchBar } from '@/components/common/searchBar/searchBar';
import { ActiveColumn } from '@/components/common/activeColumn/activeColumn';
import { ExampleTable } from './components/exampleTable';

// Mock data for demonstration
const mockData = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    status: 'Active',
    lastLogin: '2023-05-15 10:30 AM',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'User',
    status: 'Inactive',
    lastLogin: '2023-05-10 09:15 AM',
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Editor',
    status: 'Active',
    lastLogin: '2023-05-14 02:45 PM',
  },
];

const Page = () => {
  const [filteredData, setFilteredData] = useState(mockData);

  // Define column options for the table
  const columnOptions = [
    { value: 'sn', label: 'SN' },
    { value: 'name', label: 'Name' },
    { value: 'email', label: 'Email' },
    { value: 'role', label: 'Role' },
    { value: 'status', label: 'Status' },
    { value: 'lastLogin', label: 'Last Login' },
  ];

  // Define the initial columns that should be visible
  const initialVisibleColumns = [
    'sn', 'name', 'email', 'role', 'status', 'lastLogin'
  ];

  // State to track which columns are visible
  const [visibleColumns, setVisibleColumns] = useState(initialVisibleColumns);

  // Filter options for the search bar
  const FILTER_OPTIONS = [
    { label: 'Name', value: 'name' },
    { label: 'Email', value: 'email' },
    { label: 'Role', value: 'role' },
    { label: 'Status', value: 'status' },
  ];

  const handleSearch = (query: string, filters: string[]) => {
    if (!query) {
      setFilteredData(mockData);
      return;
    }

    const lowercasedQuery = query.toLowerCase();
    const filtered = mockData.filter((item) => {
      if (filters.length === 0) return false;

      return filters.some(filter => {
        const value = item[filter as keyof typeof item];
        return value?.toString().toLowerCase().includes(lowercasedQuery);
      });
    });
    setFilteredData(filtered);
  };

  // Handle column visibility changes
  const handleColumnVisibilityChange = (newVisibleColumns: string[]) => {
    setVisibleColumns(newVisibleColumns);
  };

  // Create breadcrumbs for the page
  const breadcrumbs = [
    { label: 'Home', href: '/' },
    { label: 'Examples', href: '/examples' },
    { label: 'Active Column Example', href: '/example-active-column' },
  ];

  const header = useMemo(
    () => (
      <div>
        <PageHeader
          title="Active Column Example"
          breadcrumbs={breadcrumbs}
          actions={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <SearchBar
                onSearch={handleSearch}
                placeholder="Search..."
                filterOptions={FILTER_OPTIONS}
              />
              <ActiveColumn
                columnOptions={columnOptions}
                visibleColumns={visibleColumns}
                onColumnVisibilityChange={handleColumnVisibilityChange}
                variant="popover"
              />
            </Box>
          }
        />
      </div>
    ),
    [breadcrumbs, handleSearch, FILTER_OPTIONS, columnOptions, visibleColumns]
  );

  const table = useMemo(
    () => (
      <Box sx={{ mt: 3, px: 3 }}>
        <ExampleTable
          data={filteredData}
          visibleColumns={visibleColumns}
        />
      </Box>
    ),
    [filteredData, visibleColumns]
  );

  return (
    <div>
      {header}
      {table}
    </div>
  );
};

export default Page;
