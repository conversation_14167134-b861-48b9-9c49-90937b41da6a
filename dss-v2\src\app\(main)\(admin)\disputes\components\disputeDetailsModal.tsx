"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  Typography,
  IconButton,
  Box,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableRow,
  useTheme,
  Chip,
  Paper,
  Grid,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import SecurityIcon from '@mui/icons-material/Security';
import HistoryIcon from '@mui/icons-material/History';
import EmailIcon from '@mui/icons-material/Email';
import { useThemeWithToggle } from '@/context/ThemeContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dispute-tabpanel-${index}`}
      aria-labelledby={`dispute-tab-${index}`}
      {...other}
      style={{ width: '100%' }}
    >
      {value === index && (
        <Box sx={{ p: 1.5 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `dispute-tab-${index}`,
    'aria-controls': `dispute-tabpanel-${index}`,
  };
}

interface DisputeDetailsModalProps {
  open: boolean;
  onClose: () => void;
  disputeId: string;
}

interface DisputeData {
  id: string;
  sn: number;
  sendersEmail: string;
  receiversEmail: string;
  subject: string;
  counter: number;
  aiStatus: string;
  adminStatus: string;
  createdAt: string;
  securityStatus: {
    final: string;
    reason: string;
  };
  authentication: {
    dkim: boolean;
    spf: boolean;
    dmarc: boolean;
  };
  threatIntelligence: {
    databaseCheck: boolean;
  };
  contentAnalysis: {
    urls: Array<{
      url: string;
      status: string;
    }>;
    attachments: Array<{
      name: string;
      status: string;
    }>;
  };
  disputeHistory: Array<{
    date: string;
    userComment: string;
    adminResponse: string;
    adminStatus: string;
  }>;
  emailBody: string;
}

// Mock data for demonstration
const mockDisputeData: DisputeData = {
  id: "123",
  sn: 1,
  sendersEmail: "<EMAIL>",
  receiversEmail: "<EMAIL>",
  subject: "Your OTP for Login",
  counter: 3,
  aiStatus: "Pending",
  adminStatus: "Pending",
  createdAt: "23-04-2023 11:26 AM",
  securityStatus: {
    final: "UNSAFE",
    reason: "Status reflected by ai"
  },
  authentication: {
    dkim: true,
    spf: false,
    dmarc: true
  },
  threatIntelligence: {
    databaseCheck: true
  },
  contentAnalysis: {
    urls: [
      { url: "https://example.com/login", status: "Malicious" },
      { url: "https://example.com/verify", status: "Safe" }
    ],
    attachments: [
      { name: "document.pdf", status: "Safe" },
      { name: "invoice.xlsx", status: "Malicious" }
    ]
  },
  disputeHistory: [
    {
      date: "24-04-2023 10:30 AM",
      userComment: "I believe this email is legitimate and should be allowed.",
      adminResponse: "After review, we've determined this email is unsafe.",
      adminStatus: "Unsafe"
    },
    {
      date: "23-04-2023 02:15 PM",
      userComment: "This is a legitimate OTP email from our system.",
      adminResponse: "Under review",
      adminStatus: "Pending"
    }
  ],
  emailBody: "Your OTP for login is 123456. This code will expire in 10 minutes."
};

export const DisputeDetailsModal: React.FC<DisputeDetailsModalProps> = ({ open, onClose, disputeId }) => {
  const [tabValue, setTabValue] = useState(0);
  const theme = useTheme();
  const { actualTheme } = useThemeWithToggle();
  const isDarkMode = actualTheme === 'dark';

  // In a real application, you would fetch the dispute data based on the disputeId
  const disputeData = mockDisputeData;

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const getStatusChip = (status: string) => {
    let color = "default";
    let icon = null;

    switch(status.toLowerCase()) {
      case "malicious":
        color = "error";
        icon = <CancelIcon fontSize="small" />;
        break;
      case "safe":
        color = "success";
        icon = <CheckCircleIcon fontSize="small" />;
        break;
      case "unsupported":
        color = "warning";
        break;
      default:
        color = "default";
    }

    return (
      <Chip
        label={status}
        color={color as any}
        size="small"
        icon={icon}
        sx={{
          ml: 1,
          height: '18px',
          fontSize: '0.7rem',
          '& .MuiChip-icon': {
            fontSize: '0.8rem',
            marginLeft: '4px',
            marginRight: '-4px'
          }
        }}
      />
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 1,
          backgroundColor: isDarkMode ? "rgba(30, 30, 30, 0.95)" : "rgba(255, 255, 255, 0.95)",
          backdropFilter: "blur(10px)",
          boxShadow: "0 8px 32px rgba(0, 0, 0, 0.2)",
        }
      }}
    >
      <DialogTitle sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)"}`,
        py: 1.5,
        px: 2
      }}>
        <Typography variant="h6" component="div" sx={{
          fontWeight: 600,
          color: isDarkMode ? "#fff" : "#000",
          fontSize: '1.1rem'
        }}>
          Dispute Details #{disputeData.id}
        </Typography>
        <IconButton edge="end" color="inherit" onClick={onClose} aria-label="close" size="small">
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ p: 0 }}>
        <Box sx={{
          px: 2,
          pt: 1.5,
          pb: 0.5,
          display: 'flex',
          alignItems: 'center',
          borderBottom: `1px solid ${isDarkMode ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.05)"}`,
        }}>
          <Typography
            variant="body2"
            sx={{
              color: isDarkMode ? "rgba(255, 255, 255, 0.7)" : "rgba(0, 0, 0, 0.6)",
              fontSize: '0.8rem',
              fontStyle: 'italic'
            }}
          >
            Detailed information about the selected dispute
          </Typography>
          <Chip
            label={disputeData.adminStatus}
            color={disputeData.adminStatus.toLowerCase() === "unsafe" ? "error" :
                   disputeData.adminStatus.toLowerCase() === "safe" ? "success" : "warning"}
            size="small"
            sx={{ ml: 'auto', height: '20px', fontSize: '0.7rem' }}
          />
        </Box>

        <Box sx={{ width: '100%', mt: 1 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : '#f5f5f5' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="dispute details tabs"
              sx={{
                minHeight: '42px',
                px: 2,
                '& .MuiTab-root': {
                  textTransform: 'none',
                  minWidth: 120,
                  minHeight: '42px',
                  padding: '8px 16px',
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)',
                  fontSize: '0.85rem',
                },
                '& .Mui-selected': {
                  fontWeight: 'bold',
                  color: isDarkMode ? '#fff' : '#3A52A6',
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: isDarkMode ? '#fff' : '#3A52A6',
                }
              }}
            >
              <Tab
                icon={<SecurityIcon fontSize="small" sx={{ fontSize: '1rem' }} />}
                iconPosition="start"
                label="Details & Security"
                {...a11yProps(0)}
              />
              <Tab
                icon={<HistoryIcon fontSize="small" sx={{ fontSize: '1rem' }} />}
                iconPosition="start"
                label="Dispute History"
                {...a11yProps(1)}
              />
              <Tab
                icon={<EmailIcon fontSize="small" sx={{ fontSize: '1rem' }} />}
                iconPosition="start"
                label="Email Body"
                {...a11yProps(2)}
              />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={1.5}>
              {/* Left Column */}
              <Grid item xs={12} md={6}>
                {/* Basic Information */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                    Basic Information
                  </Typography>
                  <Box sx={{ pl: 0.5 }}>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                      <strong>Sender:</strong> {disputeData.sendersEmail}
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                      <strong>Receiver:</strong> {disputeData.receiversEmail}
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                      <strong>Subject:</strong> {disputeData.subject}
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                      <strong>Created:</strong> {disputeData.createdAt}
                    </Typography>
                  </Box>
                </Paper>

                {/* Status Information */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                    Status Information
                  </Typography>
                  <Box sx={{ pl: 0.5 }}>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem', display: 'flex', alignItems: 'center' }}>
                      <strong>Counter:</strong> <Box component="span" sx={{ ml: 0.5 }}>{disputeData.counter}</Box>
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem', display: 'flex', alignItems: 'center' }}>
                      <strong>AI Status:</strong>
                      <Chip
                        label={disputeData.aiStatus}
                        color={disputeData.aiStatus.toLowerCase() === "unsafe" ? "error" :
                               disputeData.aiStatus.toLowerCase() === "safe" ? "success" : "warning"}
                        size="small"
                        sx={{ ml: 1, height: '18px', fontSize: '0.7rem' }}
                      />
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem', display: 'flex', alignItems: 'center' }}>
                      <strong>Admin Status:</strong>
                      <Chip
                        label={disputeData.adminStatus}
                        color={disputeData.adminStatus.toLowerCase() === "unsafe" ? "error" :
                               disputeData.adminStatus.toLowerCase() === "safe" ? "success" : "warning"}
                        size="small"
                        sx={{ ml: 1, height: '18px', fontSize: '0.7rem' }}
                      />
                    </Typography>
                  </Box>
                </Paper>
              </Grid>

              {/* Right Column */}
              <Grid item xs={12} md={6}>
                {/* Authentication Checks */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                    Authentication Checks
                  </Typography>
                  <Box sx={{ pl: 0.5 }}>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem', display: 'flex', alignItems: 'center' }}>
                      {disputeData.authentication.dkim ?
                        <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} /> :
                        <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} />}
                      <strong>DKIM:</strong> <Box component="span" sx={{ ml: 0.5 }}>{disputeData.authentication.dkim ? "Pass" : "Fail"}</Box>
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem', display: 'flex', alignItems: 'center' }}>
                      {disputeData.authentication.spf ?
                        <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} /> :
                        <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} />}
                      <strong>SPF:</strong> <Box component="span" sx={{ ml: 0.5 }}>{disputeData.authentication.spf ? "Pass" : "Fail"}</Box>
                    </Typography>
                    <Typography variant="body2" component="div" sx={{ mb: 0.5, fontSize: '0.85rem', display: 'flex', alignItems: 'center' }}>
                      {disputeData.authentication.dmarc ?
                        <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} /> :
                        <CancelIcon color="error" fontSize="small" sx={{ mr: 0.5, fontSize: '1rem' }} />}
                      <strong>DMARC:</strong> <Box component="span" sx={{ ml: 0.5 }}>{disputeData.authentication.dmarc ? "Pass" : "Fail"}</Box>
                    </Typography>
                  </Box>
                </Paper>

                {/* Content Analysis */}
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    mb: 1.5,
                    bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                    borderRadius: '4px'
                  }}
                >
                  <Typography variant="subtitle2" sx={{ mb: 0.5, fontWeight: 'bold' }}>
                    Content Analysis
                  </Typography>
                  <Box sx={{ pl: 0.5 }}>
                    <Typography variant="body2" sx={{ mb: 0.5, fontSize: '0.85rem' }}>
                      <strong>URLs:</strong> {disputeData.contentAnalysis.urls.length}
                    </Typography>
                    {disputeData.contentAnalysis.urls.slice(0, 2).map((url, index) => (
                      <Typography key={index} variant="body2" component="div" sx={{ mb: 0.3, fontSize: '0.8rem', display: 'flex', alignItems: 'center' }}>
                        <Box component="span" sx={{ mr: 0.5, color: 'primary.main', fontSize: '0.7rem' }}>→</Box>
                        <Box component="span" sx={{ maxWidth: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                          {url.url}
                        </Box>
                        {getStatusChip(url.status)}
                      </Typography>
                    ))}

                    <Typography variant="body2" sx={{ mt: 1, mb: 0.5, fontSize: '0.85rem' }}>
                      <strong>Attachments:</strong> {disputeData.contentAnalysis.attachments.length}
                    </Typography>
                    {disputeData.contentAnalysis.attachments.slice(0, 2).map((attachment, index) => (
                      <Typography key={index} variant="body2" component="div" sx={{ mb: 0.3, fontSize: '0.8rem', display: 'flex', alignItems: 'center' }}>
                        <Box component="span" sx={{ mr: 0.5, color: 'primary.main', fontSize: '0.7rem' }}>→</Box>
                        <Box component="span" sx={{ maxWidth: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                          {attachment.name}
                        </Box>
                        {getStatusChip(attachment.status)}
                      </Typography>
                    ))}
                  </Box>
                </Paper>
              </Grid>
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Paper elevation={0} sx={{
              bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <Table size="small">
                <TableBody>
                  <TableRow sx={{
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.08)' : '#f5f5f5',
                    '& th': {
                      color: isDarkMode ? '#fff' : '#000',
                      fontWeight: 'bold',
                      fontSize: '0.8rem',
                      py: 1
                    }
                  }}>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '15%' }}>Date</TableCell>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '35%' }}>User Comment</TableCell>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '35%' }}>Admin Response</TableCell>
                    <TableCell component="th" sx={{ fontWeight: 'bold', width: '15%' }}>Admin Status</TableCell>
                  </TableRow>
                  {disputeData.disputeHistory.map((dispute, index) => (
                    <TableRow key={index} sx={{
                      '&:nth-of-type(odd)': {
                        backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)'
                      },
                      '& td': {
                        fontSize: '0.8rem',
                        py: 1
                      }
                    }}>
                      <TableCell>{dispute.date}</TableCell>
                      <TableCell>{dispute.userComment}</TableCell>
                      <TableCell>{dispute.adminResponse}</TableCell>
                      <TableCell>
                        <Chip
                          label={dispute.adminStatus}
                          color={dispute.adminStatus.toLowerCase() === "unsafe" ? "error" :
                                 dispute.adminStatus.toLowerCase() === "safe" ? "success" :
                                 dispute.adminStatus.toLowerCase() === "none" ? "default" : "warning"}
                          size="small"
                          sx={{ height: '18px', fontSize: '0.7rem' }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Paper>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                borderLeft: `3px solid ${isDarkMode ? '#8c9eff' : '#3A52A6'}`,
                borderRadius: '4px'
              }}
            >
              <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
                Email Content
              </Typography>
              <Box sx={{
                p: 1.5,
                bgcolor: isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.04)',
                borderRadius: '4px',
                maxHeight: '300px',
                overflow: 'auto'
              }}>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontSize: '0.85rem' }}>
                  {disputeData.emailBody}
                </Typography>
              </Box>
            </Paper>
          </TabPanel>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default DisputeDetailsModal;
