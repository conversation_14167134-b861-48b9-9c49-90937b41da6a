"use client";

import React, { useEffect, useState } from "react";
import { List, Collapse, Tooltip, useTheme } from "@mui/material";
import { useRouter, usePathname } from "next/navigation";
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import Link from "next/link";

import { Icon } from "@/components/common/icon";
import {
  SidebarContainer,
  SidebarContent,
  StyledListItem,
  StyledListItemIcon,
  StyledListItemText,
  ExpandIconStyle,
  NestedList,
  SidebarSection,
  SectionDivider,
  StyledBadge,
  SidebarFooter,
} from "./sidebar.style";
import { adminMenuItems } from "@/json/sidebarData/admin";
import { IMenuItem } from "@/interfaces";

export const Sidebar: React.FC<{ isOpen?: boolean }> = ({ isOpen = true }) => {
  const router = useRouter();
  const pathname = usePathname();
  const theme = useTheme(); // Import theme to detect dark/light mode
  const [openSubMenu, setOpenSubMenu] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [menuItems, setMenuItems] = useState<IMenuItem[]>([]);
  const [hiddenBadges, setHiddenBadges] = useState<{ [key: string]: boolean }>({});
  const timeoutRefs = React.useRef<{ [key: string]: NodeJS.Timeout }>({});

  useEffect(() => {
    // Get user role from localStorage Basu
    // const userRole = localStorage.getItem("userRole");

    // Use admin menu items
    setMenuItems(adminMenuItems);

    // Determine which menu to show based on user role
    // if (userRole === "admin" || userRole === "approver") {
    //   setMenuItems(adminMenuItems);
    // } else if (userRole === "executor") {
    //   setMenuItems(executorMenuItems);
    // }
  }, []);

  useEffect(() => {
    if (!pathname) return; // Guard against null pathname

    // Check if the current path is a details page
    const isExceptionDetailsPage = pathname.startsWith('/exception-logs/details/');
    const isErrorDetailsPage = pathname.startsWith('/error-logs/details/');

    // Special handling for profile paths to distinguish between regular profile and super admin profile
    const isSuperAdminProfile = pathname === '/super-admin/profile';
    const isRegularProfile = pathname === '/profile';

    // If it's a details page, set the parent path as selected
    if (isExceptionDetailsPage) {
      setSelectedItem('/exception-logs');
    } else if (isErrorDetailsPage) {
      setSelectedItem('/error-logs');
    } else {
      setSelectedItem(pathname);
    }

    menuItems.forEach((item) => {
      if (item.submenu) {
        const hasSelectedChild = item.submenu.some(
          (subItem) => {
            // Special handling for profile paths
            if (subItem.path === '/super-admin/profile' && isSuperAdminProfile) {
              return true;
            }
            if (subItem.path === '/profile' && isRegularProfile) {
              return true;
            }

            // Check if the current path starts with the submenu path
            // This handles both exact matches and detail pages
            if (subItem.path && pathname.startsWith(subItem.path) &&
                // Exclude profile paths from the startsWith check to prevent overlap
                subItem.path !== '/profile' &&
                !pathname.startsWith('/super-admin/profile')) {
              return true;
            }

            // Also check for exact matches
            return subItem.path === pathname;
          }
        );
        if (hasSelectedChild) {
          setOpenSubMenu(item.title);
        }
      }
    });
  }, [pathname, menuItems]);

  // Cleanup timeouts when component unmounts
  useEffect(() => {
    return () => {
      // Clear all timeouts when component unmounts
      Object.values(timeoutRefs.current).forEach(timeout => {
        clearTimeout(timeout);
      });
    };
  }, []);

  const handleClick = (path?: string, title?: string) => {
    if (path) {
      setSelectedItem(path);
      // Use router.push for all navigation to prevent page reload
      router.push(path);
    } else if (title) {
      setOpenSubMenu(openSubMenu === title ? null : title);
    }

    // If the clicked item has a notification badge, hide it temporarily
    if (title && getNotificationCount(title) !== null) {
      // Update hidden badges state
      setHiddenBadges(prev => ({ ...prev, [title]: true }));

      // Clear any existing timeout for this item
      if (timeoutRefs.current[title]) {
        clearTimeout(timeoutRefs.current[title]);
      }

      // Set a timeout to make the badge reappear after 2-3 minutes
      const timeoutDuration = Math.floor(Math.random() * (180000 - 120000 + 1)) + 120000; // Random time between 2-3 minutes
      timeoutRefs.current[title] = setTimeout(() => {
        setHiddenBadges(prev => ({ ...prev, [title]: false }));
        delete timeoutRefs.current[title];
      }, timeoutDuration);
    }
  };

  // Function to get notification count for menu items
  const getNotificationCount = (title: string): number | null => {
    // Always return null to hide all badges
    return null;
  };

  // Function to determine if a menu item should have the primary color
  const shouldUsePrimaryColor = (_title: string): boolean => {
    return true; // Return true for all menu items to make all icons use the primary color
  };

  // Function to get the icon color based on the menu item and theme
  const getIconColor = (_title: string, _icon: any, isSelected: boolean): string => {
    if (isSelected) {
      return theme.palette.primary.main;
    }
    // Return white color for dark mode, black for light mode
    return theme.palette.mode === 'dark' ? '#FFFFFF' : '#000000';
  };

  const renderMenuItem = (item: IMenuItem) => {
    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const isOpen = openSubMenu === item.title;

    // Special handling for profile paths
    const isSuperAdminProfile = pathname === '/super-admin/profile';
    const isRegularProfile = pathname === '/profile';

    // Check if this menu item should be selected
    // For items with submenu, check if any child path is selected
    let isSelected = false;

    // Handle the main profile item (not in submenu)
    if (item.path === '/profile' && isRegularProfile && !isSuperAdminProfile) {
      isSelected = true;
    } else if (item.path !== '/profile') {
      // For non-profile items, use the normal selection logic
      isSelected = item.path === selectedItem;
    }

    // For parent items with submenu, they should be selected if any of their children are selected
    if (hasSubmenu && pathname && item.submenu) {
      const hasSelectedChild = item.submenu.some(
        subItem => {
          // Special handling for profile paths in submenu
          if (subItem.path === '/super-admin/profile' && isSuperAdminProfile) {
            return true;
          }
          if (subItem.path === '/profile' && isRegularProfile && !isSuperAdminProfile) {
            return true;
          }

          return subItem.path === selectedItem ||
                 (subItem.path && pathname.startsWith(`${subItem.path}/details/`));
        }
      );
      if (hasSelectedChild) {
        isSelected = true;
      }
    }

    const notificationCount = getNotificationCount(item.title);
    const usePrimaryColor = shouldUsePrimaryColor(item.title);

    // Wrap with tooltip when in collapsed mode
    const menuItem = (
      <React.Fragment>
        {item.path && !hasSubmenu ? (
          <Link href={item.path} passHref style={{ textDecoration: 'none', color: 'inherit' }}>
            <StyledListItem
              onClick={() => {
                setSelectedItem(item.path || '');

                // If the clicked item has a notification badge, hide it temporarily
                if (notificationCount !== null) {
                  // Update hidden badges state
                  setHiddenBadges(prev => ({ ...prev, [item.title]: true }));

                  // Clear any existing timeout for this item
                  if (timeoutRefs.current[item.title]) {
                    clearTimeout(timeoutRefs.current[item.title]);
                  }

                  // Set a timeout to make the badge reappear after 2-3 minutes
                  const timeoutDuration = Math.floor(Math.random() * (180000 - 120000 + 1)) + 120000; // Random time between 2-3 minutes
                  timeoutRefs.current[item.title] = setTimeout(() => {
                    setHiddenBadges(prev => ({ ...prev, [item.title]: false }));
                    delete timeoutRefs.current[item.title];
                  }, timeoutDuration);
                }
              }}
              className={`${isSelected ? "selected" : ""} ${usePrimaryColor ? "primary-item" : ""}`}
            >
              <StyledListItemIcon>
                {notificationCount ? (
                  <StyledBadge badgeContent={notificationCount} color="error">
                    <Icon
                      icon={item.icon}
                      size="small"
                      onlyIcon
                      color={getIconColor(item.title, item.icon, isSelected)}
                    />
                  </StyledBadge>
                ) : (
                  <Icon
                    icon={item.icon}
                    size="small"
                    onlyIcon
                    color={getIconColor(item.title, item.icon, isSelected)}
                  />
                )}
              </StyledListItemIcon>
              <StyledListItemText primary={item.title} />
            </StyledListItem>
          </Link>
        ) : (
          <StyledListItem
            onClick={() => handleClick(item.path, item.title)}
            className={`${isSelected ? "selected" : ""} ${isOpen ? "open" : ""} ${usePrimaryColor ? "primary-item" : ""}`}
          >
            <StyledListItemIcon>
              {notificationCount ? (
                <StyledBadge badgeContent={notificationCount} color="error">
                  <Icon
                    icon={item.icon}
                    size="small"
                    onlyIcon
                    color={getIconColor(item.title, item.icon, isSelected)}
                  />
                </StyledBadge>
              ) : (
                <Icon
                  icon={item.icon}
                  size="small"
                  onlyIcon
                  color={getIconColor(item.title, item.icon, isSelected)}
                />
              )}
            </StyledListItemIcon>
            <StyledListItemText primary={item.title} />
            {hasSubmenu && (
              <ExpandIconStyle className={isOpen ? "open" : ""}>
                {isOpen ? <ExpandLess /> : <ExpandMore />}
              </ExpandIconStyle>
            )}
          </StyledListItem>
        )}

        {hasSubmenu && (
          <Collapse in={isOpen} timeout="auto" unmountOnExit>
            <NestedList>
              {item.submenu?.map((subItem) => {
                // Special handling for profile paths
                const isSuperAdminProfile = pathname === '/super-admin/profile';
                const isRegularProfile = pathname === '/profile';

                // Check if this submenu item should be selected
                // This handles both exact matches and detail pages
                let isSubItemSelected = false;

                // Special handling for profile paths in submenu
                if (subItem.path === '/super-admin/profile' && isSuperAdminProfile) {
                  isSubItemSelected = true;
                } else if (subItem.path === '/profile' && isRegularProfile && !isSuperAdminProfile) {
                  isSubItemSelected = true;
                } else {
                  isSubItemSelected = Boolean(
                    subItem.path === selectedItem ||
                    (pathname && subItem.path && pathname.startsWith(`${subItem.path}/details/`))
                  );
                }

                const subNotificationCount = getNotificationCount(subItem.title);
                // Pass the primary color to submenu items if parent has it
                const subUsePrimaryColor = usePrimaryColor;

                return (
                  <Link
                    key={subItem.title}
                    href={subItem.path || ''}
                    passHref
                    style={{ textDecoration: 'none', color: 'inherit' }}
                  >
                    <StyledListItem
                      onClick={() => {
                        setSelectedItem(subItem.path || '');

                        // If the clicked submenu item has a notification badge, hide it temporarily
                        if (subNotificationCount !== null) {
                          // Update hidden badges state
                          setHiddenBadges(prev => ({ ...prev, [subItem.title]: true }));

                          // Clear any existing timeout for this item
                          if (timeoutRefs.current[subItem.title]) {
                            clearTimeout(timeoutRefs.current[subItem.title]);
                          }

                          // Set a timeout to make the badge reappear after 2-3 minutes
                          const timeoutDuration = Math.floor(Math.random() * (180000 - 120000 + 1)) + 120000; // Random time between 2-3 minutes
                          timeoutRefs.current[subItem.title] = setTimeout(() => {
                            setHiddenBadges(prev => ({ ...prev, [subItem.title]: false }));
                            delete timeoutRefs.current[subItem.title];
                          }, timeoutDuration);
                        }
                      }}
                      className={`${isSubItemSelected ? "selected" : ""} ${subUsePrimaryColor ? "primary-item" : ""}`}
                    >
                      <StyledListItemIcon>
                        {subNotificationCount ? (
                          <StyledBadge badgeContent={subNotificationCount} color="error">
                            <Icon
                              icon={subItem.icon}
                              size="small"
                              onlyIcon
                              color={getIconColor(subItem.title, subItem.icon, isSubItemSelected)}
                            />
                          </StyledBadge>
                        ) : (
                          <Icon
                            icon={subItem.icon}
                            size="small"
                            onlyIcon
                            color={getIconColor(subItem.title, subItem.icon, isSubItemSelected)}
                          />
                        )}
                      </StyledListItemIcon>
                      <StyledListItemText primary={subItem.title} />
                    </StyledListItem>
                  </Link>
                );
              })}
            </NestedList>
          </Collapse>
        )}
      </React.Fragment>
    );

    // Add tooltip for collapsed sidebar
    return !isOpen ? (
      <Tooltip
        title={item.title}
        placement="right"
        arrow
        disableHoverListener={!isOpen || Boolean(item.submenu)}
      >
        <div>{menuItem}</div>
      </Tooltip>
    ) : menuItem;
  };

  // Group menu items by category for better organization
  const groupedMenuItems = () => {
    const groups = [
      { title: "Main", items: menuItems.filter(item => ["Dashboard", "Profile"].includes(item.title)) },
      { title: "Management", items: menuItems.filter(item => ["Super Admin", "Approver", "Executor", "Plugin", "Phishing", "Disputes", "Reports"].includes(item.title)) },
      { title: "Security", items: menuItems.filter(item => ["Sandbox", "Quarantine", "Rogue DB"].includes(item.title)) },
      { title: "System", items: menuItems.filter(item => ["Logs"].includes(item.title)) },
    ];

    return groups.filter(group => group.items.length > 0);
  };

  return (
    <SidebarContainer className={`sidebar-container ${isOpen ? "" : "icon-only"}`}>
      {/* Header section removed */}

      <SidebarContent>
        {/* Render menu items by category */}
        {groupedMenuItems().map((group, index) => (
          <SidebarSection key={group.title}>
            {index > 0 && <SectionDivider />}
            <List component="nav">
              {group.items.map((item) => (
                <React.Fragment key={item.title}>
                  {renderMenuItem(item)}
                </React.Fragment>
              ))}
            </List>
          </SidebarSection>
        ))}
      </SidebarContent>

      {/* Footer with version info */}
      <SidebarFooter>
        <span>v2.0.0 • © 2023</span>
      </SidebarFooter>
    </SidebarContainer>
  );
};
