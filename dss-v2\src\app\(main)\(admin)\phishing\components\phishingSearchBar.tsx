import React, { useState, KeyboardEvent, ChangeEvent, ReactNode } from 'react';
import { Popover, FormControl, FormGroup, FormControlLabel, Checkbox, Box, Button } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';
import {
    SearchContainer,
    SearchPaper,
    SearchInputBase,
    SearchIconButton,
    FilterIconButton,
    FilterPopoverBox
} from '@/components/common/searchBar/searchBar.style';

interface PhishingSearchBarProps {
    onSearch?: (searchTerm: string, filters: string[]) => void;
    placeholder?: string;
    actionButton?: ReactNode;
}

const FILTER_OPTIONS = [
    { label: 'Message ID', value: 'messageId' },
    { label: 'Sender\'s Email', value: 'sendersEmail' },
    { label: 'Receiver\'s Email', value: 'receiversEmail' },
    { label: 'Subject', value: 'subject' },
    { label: 'Create Time', value: 'createTime' }
];

const PhishingSearchBar: React.FC<PhishingSearchBarProps> = ({ 
    onSearch = () => {}, 
    placeholder = 'Search...',
    actionButton
}) => {
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
    const [columnsAnchorEl, setColumnsAnchorEl] = useState<HTMLButtonElement | null>(null);
    const [selectedFilters, setSelectedFilters] = useState<string[]>(['messageId', 'sendersEmail', 'receiversEmail', 'subject']);

    const handleFilterClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleColumnsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setColumnsAnchorEl(event.currentTarget);
    };

    const handleFilterClose = () => {
        setAnchorEl(null);
    };

    const handleColumnsClose = () => {
        setColumnsAnchorEl(null);
    };

    const handleFilterChange = (value: string) => {
        setSelectedFilters(prev => {
            if (prev.includes(value)) {
                return prev.filter(item => item !== value);
            } else {
                return [...prev, value];
            }
        });
    };

    const handleSearchClick = () => {
        onSearch(searchTerm, selectedFilters);
    };

    const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            handleSearchClick();
        }
    };

    const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setSearchTerm(newValue);
        
        // If the input is cleared (empty), trigger search to show all data
        if (newValue === '') {
            onSearch('', selectedFilters);
        }
    };

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SearchContainer>
                <SearchPaper
                    onSubmit={(e: React.FormEvent) => {
                        e.preventDefault();
                    }}
                >
                    <SearchInputBase
                        placeholder={placeholder}
                        value={searchTerm}
                        onChange={handleInputChange}
                        onKeyPress={handleKeyPress}
                    />
                    <SearchIconButton
                        onClick={handleSearchClick}
                        size="small"
                    >
                        <SearchIcon />
                    </SearchIconButton>
                </SearchPaper>
            </SearchContainer>

            <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                onClick={handleFilterClick}
                size="small"
                sx={{ 
                    textTransform: 'none',
                    borderColor: '#e0e0e0',
                    color: 'text.primary',
                    '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        borderColor: '#d5d5d5'
                    }
                }}
            >
                Filter
            </Button>

            <Button
                variant="outlined"
                startIcon={<ViewColumnIcon />}
                onClick={handleColumnsClick}
                size="small"
                sx={{ 
                    textTransform: 'none',
                    borderColor: '#e0e0e0',
                    color: 'text.primary',
                    '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.04)',
                        borderColor: '#d5d5d5'
                    }
                }}
            >
                Columns
            </Button>

            {actionButton && (
                <Box sx={{ ml: 1 }}>
                    {actionButton}
                </Box>
            )}

            <Popover
                open={Boolean(anchorEl)}
                anchorEl={anchorEl}
                onClose={handleFilterClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                <FilterPopoverBox>
                    <FormControl component="fieldset">
                        <FormGroup>
                            {FILTER_OPTIONS.map((option) => (
                                <FormControlLabel
                                    key={option.value}
                                    control={
                                        <Checkbox
                                            checked={selectedFilters.includes(option.value)}
                                            onChange={() => handleFilterChange(option.value)}
                                            size="small"
                                        />
                                    }
                                    label={option.label}
                                />
                            ))}
                        </FormGroup>
                    </FormControl>
                </FilterPopoverBox>
            </Popover>

            <Popover
                open={Boolean(columnsAnchorEl)}
                anchorEl={columnsAnchorEl}
                onClose={handleColumnsClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                <FilterPopoverBox>
                    <FormControl component="fieldset">
                        <FormGroup>
                            {[
                                { label: 'SN', value: 'sn' },
                                { label: 'Message ID', value: 'messageId' },
                                { label: 'Sender\'s Email', value: 'sendersEmail' },
                                { label: 'Receiver\'s Email', value: 'receiversEmail' },
                                { label: 'Subject', value: 'subject' },
                                { label: 'Create Time', value: 'createTime' },
                                { label: 'Details', value: 'details' }
                            ].map((option) => (
                                <FormControlLabel
                                    key={option.value}
                                    control={
                                        <Checkbox
                                            checked={true}
                                            size="small"
                                        />
                                    }
                                    label={option.label}
                                />
                            ))}
                        </FormGroup>
                    </FormControl>
                </FilterPopoverBox>
            </Popover>
        </Box>
    );
};

export default PhishingSearchBar;
