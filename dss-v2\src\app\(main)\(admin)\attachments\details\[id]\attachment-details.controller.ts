"use client";

import { useState, useEffect } from "react";
import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay } from "@/components/common/breadcrumb";

// Mock data for the attachment details
const mockAttachmentData = {
  id: "att-123456",
  name: "invoice_april_2023.pdf",
  status: "Malicious",
  type: "application/pdf",
  size: "1.2 MB",
  uploadDate: "15-05-2023 10:30 AM",
  hash: {
    md5: "d41d8cd98f00b204e9800998ecf8427e",
    sha1: "da39a3ee5e6b4b0d3255bfef95601890afd80709",
    sha256: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
  },
  threatScore: 85,
  scanResults: {
    malware: true,
    phishing: false,
    suspicious: true,
    clean: false
  },
  associatedEmail: {
    messageId: "msg-789012",
    sendersEmail: "<EMAIL>",
    receiversEmail: "<EMAIL>",
    subject: "Invoice for April 2023",
    createTime: "15-05-2023 10:25 AM"
  },
  metadata: {
    creationDate: "14-05-2023 09:15 AM",
    modificationDate: "14-05-2023 09:30 AM",
    author: "Unknown",
    embeddedLinks: [
      "https://malicious-site.com/download",
      "https://another-bad-site.net/payload"
    ],
    embeddedObjects: [
      "JavaScript code",
      "Embedded macro"
    ]
  },
  analysisHistory: [
    {
      date: "15-05-2023 10:31 AM",
      action: "Initial scan",
      result: "Detected malicious JavaScript",
      user: "System"
    },
    {
      date: "15-05-2023 10:35 AM",
      action: "Sandbox analysis",
      result: "Attempted connection to known malicious domains",
      user: "System"
    },
    {
      date: "15-05-2023 11:00 AM",
      action: "Manual review",
      result: "Confirmed malicious",
      user: "Admin"
    }
  ],
  sandboxResults: {
    behaviorSummary: "The file attempts to execute obfuscated JavaScript code that tries to download additional payloads from remote servers.",
    networkConnections: [
      "malicious-site.com:443",
      "another-bad-site.net:80"
    ],
    systemModifications: [
      "Created registry key: HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run",
      "Modified system32 files"
    ],
    suspiciousActivities: [
      "Attempted to disable security software",
      "Tried to access sensitive user data"
    ]
  }
};

interface IAttachmentDetailsController {
  getters: {
    loading: boolean;
    attachmentData: any;
    breadcrumbs: IBreadcrumbDisplay[];
  };
  handlers: {
    handleDownload: () => void;
    handleRelease: () => void;
    handleDelete: () => void;
    handleAddToAllowlist: () => void;
    handleAddToBlocklist: () => void;
    handleRunAdditionalAnalysis: () => void;
  };
}

/**
 * Attachment Details Controller
 * @param {string} attachmentId - The ID of the attachment to display
 * @return {IAttachmentDetailsController} Controller with getters and handlers
 */
export const AttachmentDetailsController = (attachmentId: string): IAttachmentDetailsController => {
  const [loading, setLoading] = useState(true);
  const [attachmentData, setAttachmentData] = useState<any>(null);

  // In a real application, you would fetch the attachment data based on the attachmentId
  useEffect(() => {
    // Simulate API call
    const fetchAttachmentData = async () => {
      setLoading(true);
      try {
        // In a real app, you would fetch data from an API
        // const response = await fetch(`/api/attachments/${attachmentId}`);
        // const data = await response.json();

        // For now, we'll use mock data
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 500));

        // Update the mock data with the provided ID
        const data = { ...mockAttachmentData, id: attachmentId };
        setAttachmentData(data);
      } catch (error) {
        console.error("Error fetching attachment data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchAttachmentData();
  }, [attachmentId]);

  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Quarantine",
      path: RoutePathEnum.QUARANTINE,
      forwardParam: false,
      clickable: true,
    },
    {
      name: `Attachment Details: ${attachmentData?.name || attachmentId}`,
      path: RoutePathEnum.NONE,
      forwardParam: false,
    },
  ];

  // Handler functions
  const handleDownload = () => {
    console.log(`Downloading attachment: ${attachmentId}`);
    // In a real app, implement download functionality
    alert("Download functionality would be implemented here with appropriate security warnings.");
  };

  const handleRelease = () => {
    console.log(`Releasing attachment: ${attachmentId}`);
    // In a real app, implement release functionality
    alert("Release functionality would be implemented here.");
  };

  const handleDelete = () => {
    console.log(`Deleting attachment: ${attachmentId}`);
    // In a real app, implement delete functionality
    alert("Delete functionality would be implemented here.");
  };

  const handleAddToAllowlist = () => {
    console.log(`Adding attachment to allowlist: ${attachmentId}`);
    // In a real app, implement allowlist functionality
    alert("Add to allowlist functionality would be implemented here.");
  };

  const handleAddToBlocklist = () => {
    console.log(`Adding attachment to blocklist: ${attachmentId}`);
    // In a real app, implement blocklist functionality
    alert("Add to blocklist functionality would be implemented here.");
  };

  const handleRunAdditionalAnalysis = () => {
    console.log(`Running additional analysis on attachment: ${attachmentId}`);
    // In a real app, implement additional analysis functionality
    alert("Additional analysis functionality would be implemented here.");
  };

  return {
    getters: {
      loading,
      attachmentData,
      breadcrumbs,
    },
    handlers: {
      handleDownload,
      handleRelease,
      handleDelete,
      handleAddToAllowlist,
      handleAddToBlocklist,
      handleRunAdditionalAnalysis,
    },
  };
};

export default AttachmentDetailsController;
