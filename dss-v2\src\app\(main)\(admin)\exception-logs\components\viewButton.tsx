import React from 'react';
import { Button, Tooltip } from '@mui/material';
import { useRouter } from 'next/navigation';
import { RoutePathEnum } from '@/enum';

interface ViewButtonProps {
  onClick?: () => void;
  exceptionId: string;
}

export const ViewButton: React.FC<ViewButtonProps> = ({ onClick, exceptionId }) => {
  const router = useRouter();

  const handleClick = () => {
    // Navigate to the exception details page
    router.push(`/exception-logs/details/${exceptionId}`);

    // Also call the original onClick handler if provided (for any additional logic)
    if (onClick) {
      onClick();
    }
  };

  return (
    <Tooltip title="View Traceback">
      <Button
        onClick={handleClick}
        size="small"
        variant="contained"
        color="primary"
      >
        View
      </Button>
    </Tooltip>
  );
};

export default ViewButton;
