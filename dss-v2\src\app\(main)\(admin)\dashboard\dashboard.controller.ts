"use client";

import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay } from "@/components/common/breadcrumb";

interface IDashboardController {
  getters: {
    breadcrumbs: IBreadcrumbDisplay[];
  };
  handlers: {};
}

/**
 * Dashboard Controller
 * @return {IDashboardController}
 */
export function DashboardController(): IDashboardController {
  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
  ];

  return {
    getters: {
      breadcrumbs,
    },
    handlers: {},
  };
}
