import React, { useState, useCallback } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableFooter,
  Box,
} from '@mui/material';
import { CustomTablePagination } from '@/components/common/table/tablePagination';
import { CommonTableStyle } from '@/components/common/table/commonTableStyle';

interface ExampleData {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  lastLogin: string;
}

interface ExampleTableProps {
  data: ExampleData[];
  visibleColumns?: string[];
}

export const ExampleTable: React.FC<ExampleTableProps> = ({
  data,
  visibleColumns = ['sn', 'name', 'email', 'role', 'status', 'lastLogin']
}) => {
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const handleChangePage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((newRowsPerPage: number) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
  }, []);

  // Calculate pagination
  const startIndex = (page - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentPageData = data.slice(startIndex, endIndex);

  return (
    <Box sx={{ width: '100%' }}>
      <CommonTableStyle elevation={0}>
        <Table sx={{ minWidth: 650 }} aria-label="example table">
          <TableHead>
            <TableRow sx={{ backgroundColor: '#3A52A6' }}>
              {visibleColumns.includes('sn') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '5%' })}>SN</TableCell>
              )}
              {visibleColumns.includes('name') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '20%' })}>NAME</TableCell>
              )}
              {visibleColumns.includes('email') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '25%' })}>EMAIL</TableCell>
              )}
              {visibleColumns.includes('role') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>ROLE</TableCell>
              )}
              {visibleColumns.includes('status') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '15%' })}>STATUS</TableCell>
              )}
              {visibleColumns.includes('lastLogin') && (
                <TableCell sx={(theme) => ({ color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000', fontWeight: 'bold', width: '20%' })}>LAST LOGIN</TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {currentPageData.map((row, index) => (
              <TableRow
                key={row.id}
                sx={{
                  '&:nth-of-type(odd)': { backgroundColor: '#f9f9f9' },
                  '&:hover': { backgroundColor: '#f1f1f1' },
                }}
              >
                {visibleColumns.includes('sn') && (
                  <TableCell>{startIndex + index + 1}</TableCell>
                )}
                {visibleColumns.includes('name') && (
                  <TableCell>{row.name}</TableCell>
                )}
                {visibleColumns.includes('email') && (
                  <TableCell>{row.email}</TableCell>
                )}
                {visibleColumns.includes('role') && (
                  <TableCell>{row.role}</TableCell>
                )}
                {visibleColumns.includes('status') && (
                  <TableCell>{row.status}</TableCell>
                )}
                {visibleColumns.includes('lastLogin') && (
                  <TableCell>{row.lastLogin}</TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CommonTableStyle>

      {/* Pagination */}
      <Table>
        <TableFooter>
          <TableRow>
            <CustomTablePagination
              page={page}
              limit={rowsPerPage}
              total={data.length}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </TableRow>
        </TableFooter>
      </Table>
    </Box>
  );
};

export default ExampleTable;
