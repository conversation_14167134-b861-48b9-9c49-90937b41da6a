"use client";

import { RoutePathEnum } from "@/enum";
import { IBreadcrumbDisplay } from "@/components/common/breadcrumb";

interface IErrorLogsController {
  getters: {
    breadcrumbs: IBreadcrumbDisplay[];
  };
  handlers: {};
}

/**
 * Error Logs Controller
 * @return {IErrorLogsController}
 */
export function ErrorLogsController(): IErrorLogsController {
  /**
   * Breadcrumbs for the page
   */
  const breadcrumbs: IBreadcrumbDisplay[] = [
    {
      name: "Dashboard",
      path: RoutePathEnum.ADMIN_DASHBOARD,
      forwardParam: false,
    },
    {
      name: "Logs",
      path: "", // No direct path for Logs category
      forwardParam: false,
    },
    {
      name: "Error Logs",
      path: RoutePathEnum.ERROR_LOGS,
      forwardParam: false,
    },
  ];

  return {
    getters: {
      breadcrumbs,
    },
    handlers: {},
  };
}
