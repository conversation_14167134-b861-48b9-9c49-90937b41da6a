import React from 'react';
import { Button, Tooltip } from '@mui/material';
import { useRouter } from 'next/navigation';

interface ViewReportButtonProps {
  onClick?: (value: string) => void;
  value: string;
  index: number;
}

export const ViewReportButton: React.FC<ViewReportButtonProps> = ({ onClick, value, index }) => {
  const router = useRouter();

  const handleClick = () => {
    // Navigate to the system details page with the license ID as a query parameter
    router.push(`/plugin/system-details?licenseId=${value}`);

    // Also call the original onClick handler if provided
    if (onClick) {
      onClick(value);
    }
  };

  return (
    <Button
      onClick={handleClick}
      size="small"
      variant="contained"
      sx={{
        backgroundColor: '#1565C0',
        '&:hover': {
          backgroundColor: '#0D47A1',
        },
      }}
    >
      View
    </Button>
  );
};

export default ViewReportButton;
