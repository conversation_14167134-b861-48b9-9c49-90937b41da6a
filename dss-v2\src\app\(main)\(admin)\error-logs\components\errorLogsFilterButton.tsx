"use client";

import React from 'react';
import { EnhancedFilterButton } from '@/components/common/filterDrawer';
import ErrorIcon from '@mui/icons-material/Error';

interface ErrorLogsFilterButtonProps {
  onApplyFilters: (filters: string[]) => void;
  searchTerm: string;
}

const ErrorLogsFilterButton: React.FC<ErrorLogsFilterButtonProps> = ({
  onApplyFilters,
  searchTerm
}) => {
  const filterOptions = [
    { label: "User", value: "user", type: "text" },
    { label: "Path", value: "path", type: "text" },
    { label: "Method", value: "method", type: "text" },
    { label: "Error Type", value: "errorType", type: "text" },
    { label: "Error Message", value: "errorMessage", type: "text" },
    { label: "Timestamp", value: "timestamp", type: "date" },
    { label: "Traceback", value: "traceback", type: "text" },
  ];

  return (
    <EnhancedFilterButton
      filterOptions={filterOptions}
      onApplyFilters={onApplyFilters}
      buttonLabel="Error Logs Filters"
      title="Error Logs Filters"
      description="Apply filters to refine your error logs search results"
      icon={<ErrorIcon />}
      searchTerm={searchTerm}
    />
  );
};

export default ErrorLogsFilterButton;
